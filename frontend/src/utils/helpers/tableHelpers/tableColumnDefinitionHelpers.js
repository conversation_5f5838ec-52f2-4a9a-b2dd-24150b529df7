import numeral from "numeral";
import { dateFormatter } from "../formatter";
import { label<PERSON><PERSON><PERSON>cy<PERSON>and<PERSON>, replace<PERSON><PERSON><PERSON><PERSON>hara<PERSON> } from "../utility_helpers";

export const columnFormatter = ({col_defs = [], currency = "$"}) => {
    const formattedColDefs = [];
    col_defs.forEach(col => {
        col.headerName = labelCurrencyHandler(col.headerName, currency);
        if (!col.valueGetter) {
            if (col.type === "date") {
                col.valueGetter = (params) => {
                    const { data, column } = params;
                    const field = column.colId;
                    const value = data?.[field];
                    if (value === null || value === undefined) return null;
                    return dateFormatter({value})
                }
            } else if (col.type === "number") {
                const numberFormat = col.numberFormat || "0";
                col.valueGetter = (params) => {
                    const { data, column } = params;
                    const field = column.colId;
                    const value = data?.[field];
                    if (value === null || value === undefined) return null;
                    const formattedValue = numeral(value).format(numberFormat);
                    return +formattedValue;
                }
            } else {
                col.valueGetter = (params) => {
                    const { data, column } = params;
                    const field = column.colId;
                    const value = data?.[field];
                    if (value === null || value === undefined) return null;
                    const params_with_value = {
                        ...params,
                        value: value
                    };
                    const formattedValue = col.valueFormatter ? col.valueFormatter(params_with_value) : replaceSpecialCharacter(value);
                    return formattedValue;
                }
            }
        }

        // for child columns, we need to format the value
        if (col.children?.length) {
            col.children = columnFormatter({col_defs: col.children, currency})
        }

        formattedColDefs.push(col);
    })

    return formattedColDefs;
}