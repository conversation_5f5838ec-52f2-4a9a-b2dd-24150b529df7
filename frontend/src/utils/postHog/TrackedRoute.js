// TrackedRoute.js
import React, { useContext, useEffect } from "react";
import { Route } from "react-router-dom";
// import AnalyticsContext from "./AnalyticsContext";

const TrackedRoute = ({ userInfo, ...props }) => {
	// const analytics = useContext(AnalyticsContext);
	const { path } = props;

	useEffect(() => {
		if (userInfo) {
			// analytics.identify(userInfo.userId, userInfo.properties);
		}
		// analytics.capture("$pageview", { path });
	 }, [path, userInfo]); //[path, analytics, userInfo]

	return <Route {...props} />;
};

export default TrackedRoute;
