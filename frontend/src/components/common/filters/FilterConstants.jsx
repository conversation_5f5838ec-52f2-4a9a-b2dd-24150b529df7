import moment from "moment";
import { product_hierarchy_level_id_mapping, store_hierarchy_level_id_mapping } from "../../../constants/FilterConfigConstants";
import { capitalizeFirstLetter } from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";

export const titleMap = {
  product_hierarchies: "Product",
  customer_hierarchies: "Customer",
  store_hierarchies: "Store",
};

export const fabricatePayload = (filtersData) => {
  const payload = {};
  const keysInProductHierarchy = Object.keys(
    product_hierarchy_level_id_mapping
  );
  const keysInStoreHierarchy = Object.keys(store_hierarchy_level_id_mapping);
  _.forEach(Object.keys(filtersData || {}), (key) => {
    if (keysInProductHierarchy.includes(key)) {
      payload["product_hierarchies"] = {
        ...payload["product_hierarchies"],
        ...filtersData[key]?.selectedOptions?.length ? {[key]: _.cloneDeep(filtersData[key]?.selectedOptions)} : {},
      };
    } else if (keysInStoreHierarchy.includes(key)) {
      payload["store_hierarchies"] = {
        ...payload["store_hierarchies"],
        ...filtersData[key]?.selectedOptions?.length ? {[key]: _.cloneDeep(filtersData[key]?.selectedOptions)} : {},
      };
    } else {
      payload[key] = filtersData[key];
    }
  });
  return payload;
};

export const processFilterData = (data) => {
  const fabricatedData = {};
  const hierarchyPayload = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (titleMap?.[key]) {
      // Process titleMap keys
      const processedEntries = {};
      const hierarchyEntries = {};
      
      for (const [key2, value2] of Object.entries(value)) {
        const mappedValues = value2?.map(item => item.value) || [];
        
        // For fabricatedData
        processedEntries[key2] = {
          selectedOptionsArray: mappedValues,
          selectedOptions: value2,
        };
        
        // For hierarchyPayload
        hierarchyEntries[key2] = mappedValues;
      }
      
      Object.assign(fabricatedData, processedEntries);
      hierarchyPayload[key] = hierarchyEntries;
      
    } else {
      // Handle non-titleMap keys
      if (key === "dateRange") {
        const processedDateRange = {
          start_date: moment(value?.start_date),
          end_date: moment(value?.end_date),
        };
        fabricatedData[key] = processedDateRange;
        hierarchyPayload[key] = value; // Original value for hierarchy
      } else {
        fabricatedData[key] = value;
        hierarchyPayload[key] = value;
      }
    }
  }
  
  return { fabricatedData, hierarchyPayload };
};


export const data_info_content_map = [
  {
    label: "Start Date",
    value: "start_date",
  },
  {
    label: "End Date",
    value: "end_date",
  },
  {
    label: "Show Partially Overlapping Events",
    value: "show_partially_overlapping_events",
  },
  {
    label: "Metrics Display Mode",
    value: "metrics_display_mode",
  },
  {
    label: capitalizeFirstLetter(global_labels?.event_primary),
    value: "event",
  },
  {
    label: "Priority Numbers",
    value: "priority_numbers",
  },
  {
    label: capitalizeFirstLetter(global_labels?.promo_primary),
    value: "promo",
  },
];