import React from "react";
import { But<PERSON>, Tag, TagGroup, Input } from "impact-ui";
import { useSelector } from "react-redux";
import {
  Search as SearchIcon,
  SwapVert as SwapVertIcon,
  Add as AddIcon,
} from "@mui/icons-material";
// import personalImage from "../../../../assets/imageAssets/user.svg?.url";
// import globalImage from "../../../../assets/imageAssets/globe.svg?.url";
import Star from "../../../../components/icons/star";

const FILTER_LABELS = [
  { label: "All", image: null },
  { label: "Personal", image: null },
  { label: "Global", image: null },
];

const STATUS_LABELS = [
  {
    label: "Active",
    icon: <div className="greenDot" />,
  },
  {
    label: "Default",
    icon: <Star fill="#E1BC29" />,
  },
];

const Header = ({ listFilter, setListFilter, handleNewFilter, screen }) => {
  const savedFiltersData = useSelector(
    (store) => store?.pricesmartPromoReducer.filters?.savedFiltersData
  );

  return (
    <div className="saved-filters-container">
      <div className="flexContentAround">
        <div className="flexWithGap12">
          <p className="text-16-800">
            Saved Filters ({savedFiltersData?.[screen?.toLowerCase()]?.length})
          </p>
          <Button
            className=""
            icon={<AddIcon />}
            iconPlacement="left"
            size="medium"
            type="default"
            variant="secondary"
            onClick={handleNewFilter}
          >
            New Filter
          </Button>
        </div>
        <div className="flexWithGap12">
          <Input
            placeholder="Search"
            rightIcon={<SearchIcon />}
            value={listFilter?.search}
            onChange={(e) =>
              setListFilter((prev) => ({ ...prev, search: e.target.value }))
            }
            type="text"
          />
          <Button
            icon={<SwapVertIcon />}
            onClick={() =>
              setListFilter((prev) => ({ ...prev, sort: !prev?.sort }))
            }
            size="medium"
            variant="tertiary"
          />
        </div>
      </div>
      <div className="marginTop-12 flexContentAround">
        <TagGroup>
          {FILTER_LABELS.map((tag) => (
            <Tag
              label={
                <div className="flexWithGap8">
                  {tag.image && (
                    <img
                      src={tag.image}
                      alt={tag.label}
                      width={20}
                      height={20}
                    />
                  )}
                  {tag.label}
                </div>
              }
              onClick={(e) => {
                e.preventDefault();
                setListFilter((prev) => ({ ...prev, chip: tag.label }));
              }}
              size="medium"
              variant={listFilter.chip === tag.label ? "filled" : "stroke"}
              key={tag.label}
            />
          ))}
        </TagGroup>
        <div className="flexWithGap12">
          {STATUS_LABELS.map((tag) => (
            <div className="flexWithGap8" key={tag.label}>
              {tag.icon} {tag.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Header;
