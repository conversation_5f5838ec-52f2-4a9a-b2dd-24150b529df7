import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "impact-ui";
import moment from "moment";
import Header from "./Header";
import SavedFilterCard from "./SavedFilterCard";
import FilterInfo from "./FilterInfo";
import LoaderComponent from "../../../ui/loaderComponent/LoaderComponent";
import {
  copySavedFilter,
  createSavedFilter,
  getSavedFilterById,
  getSavedFiltersData,
  setAppliedSavedFilter,
  setDefaultSavedFilter,
  setEditFilter,
  setIndividualFilterInfo,
  setSavedFiltersView,
  setScreenFilters,
  updateSavedFilter,
} from "../../../../store/features/filters/filters";
import SaveInfo from "./SaveInfo";
import { toastError } from "../../../../store/features/global/global";
import { useModal } from "../../../../utils/helpers/utility_helpers";
import { fabricatePayload, processFilterData } from "../FilterConstants";
import WarningModal from "./WarningModal";
import EmptyData from "../../../ui/emptyData/EmptyData";

const SavedFilters = (props) => {
  const { screen, onFilterApply, handleToggleDefaultView } = props;
  const dispatch = useDispatch();

  const {
    savedFiltersData,
    savedFiltersView,
    filtersData,
    individualFilterInfo,
    editFilter,
    appliedSavedFilter,
  } = useSelector((store) => store?.pricesmartPromoReducer.filters);

  const [filterBasicInfo, setFilterBasicInfo] = useState({
    filterName: "",
    filterDescription: "",
    isDefault: false,
    isMultipleScreens: "this_screen",
    scope: "personal",
  });
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [listFilter, setListFilter] = useState({
    chip: "All",
    search: null,
    sort: true,
  });
  const [isCopyFlow, setIsCopyFlow] = useState(false);

  const [
    isWarningModalOpen,
    openWarningModal,
    closeWarningModal,
    warningModalData,
  ] = useModal();

  const resetStates = () => {
    setSelectedFilter(null);
    setIsCopyFlow(false);
    setFilterBasicInfo({
      filterName: "",
      filterDescription: "",
      isDefault: false,
      isMultipleScreens: "this_screen",
      scope: "personal",
    });
    dispatch(setSavedFiltersView("landing"));
  };

  const handleToggleSavedFiltersLanding = () => {
    resetStates();
  };

  const onFilterStripClick = (filter) => {
    dispatch(setSavedFiltersView("filterInfo"));
    setSelectedFilter(filter);
  };

  const handleSaveFilter = async () => {
    if (!filterBasicInfo?.filterName) {
      dispatch(toastError("Please enter filter name"));
      return;
    }

    const payload = {
      filter_name: filterBasicInfo?.filterName,
      description: filterBasicInfo?.filterDescription,
      scope: filterBasicInfo?.scope,
      screen_name: screen?.toLowerCase(),
      is_multi_screen: filterBasicInfo?.isMultipleScreens === "all_screens",
      is_default: filterBasicInfo?.isDefault,
      filter_config: {
        ...fabricatePayload(filtersData?.[screen]),
      },
    };

    let response;

    if (Object.keys(editFilter || {})?.length > 0) {
      payload.id = editFilter?.filter_id;
      response = await dispatch(updateSavedFilter(payload));
    } else {
      response = await dispatch(createSavedFilter(payload));
    }

    if (response) {
      resetStates();
      dispatch(getSavedFiltersData(screen?.toLowerCase(), true));
    }
  };

  const handleApplyFilter = () => {
    let data =
      individualFilterInfo?.[screen?.toLowerCase()]?.[
        selectedFilter?.filter_id
      ];

    dispatch(setAppliedSavedFilter(data));
    dispatch(setDefaultSavedFilter(data));
  };

  const handleNewFilter = () => {
    handleToggleDefaultView();
    dispatch(
      setScreenFilters({
        activeScreen: screen,
        data: {},
      })
    );
  };

  const handleCopyClick = (filter) => {
    setIsCopyFlow(true);
    dispatch(setSavedFiltersView("filterInfo"));
    setSelectedFilter(filter);
  };

  const handleCopyFilter = async () => {
    const payload = {
      screen_name: screen?.toLowerCase(),
      id: selectedFilter?.filter_id,
      filter_name: selectedFilter?.filter_name,
      is_default: selectedFilter?.is_default,
    };

    const response = await dispatch(copySavedFilter(payload));
    if (response) {
      resetStates();
      dispatch(getSavedFiltersData(screen?.toLowerCase(), true));
    }
  };

  const handleUpdatePanelFilterData = (filterData) => {
    //* "fabricatedData" is how the filter panel wants it, like "l0_ids: {options, selectedOptionsArray, selectedOptions}"
    const { fabricatedData } = processFilterData(filterData);

    dispatch(
      setScreenFilters({
        activeScreen: screen,
        data: fabricatedData,
      })
    );
  };

  const handleEditFilter = async (filter) => {
    dispatch(setEditFilter(filter));
    const currentData =
      individualFilterInfo?.[screen?.toLowerCase()]?.[filter?.filter_id]
        ?.filter_config;
    if (currentData) {
      handleUpdatePanelFilterData(currentData);
    } else {
      const response = await dispatch(getSavedFilterById(filter?.filter_id));
      dispatch(
        setIndividualFilterInfo({
          data: response,
          activeScreen: screen?.toLowerCase(),
        })
      );
      setSelectedFilter(response);
      handleUpdatePanelFilterData(response?.filter_config);
    }

    handleToggleDefaultView();
  };

  const applySort = (data) => {
    //* true -> asc, false -> desc
    if (listFilter?.sort) {
      return data.sort((a, b) => a?.filter_name?.localeCompare(b?.filter_name));
    }
    return data.sort((a, b) => b?.filter_name?.localeCompare(a?.filter_name));
  };

  const renderContent = () => {
    if (savedFiltersView === "saveFilter")
      return (
        <SaveInfo
          filterBasicInfo={filterBasicInfo}
          screen={screen}
          setFilterBasicInfo={setFilterBasicInfo}
        />
      );

    if (savedFiltersView === "filterInfo")
      return (
        <FilterInfo
          selectedFilter={selectedFilter}
          screen={screen}
          setSelectedFilter={setSelectedFilter}
          openWarningModal={openWarningModal}
          isCopyFlow={isCopyFlow}
          setIsCopyFlow={setIsCopyFlow}
          handleEditFilter={handleEditFilter}
        />
      );

    const filteredData = applySort(
      savedFiltersData?.[screen?.toLowerCase()]?.filter((item) => {
        //* once isFilterPassed is false, no need to check the object for next type of filter(could be search or chip)
        let isFilterPassed = true;
        if (listFilter?.search) {
          isFilterPassed = item?.filter_name
            ?.toLowerCase()
            ?.includes(listFilter?.search?.toLowerCase());
        }
        if (listFilter?.chip !== "All" && isFilterPassed) {
          isFilterPassed = item?.scope === listFilter?.chip?.toLowerCase();
        }
        return isFilterPassed;
      })
    );

    const activeFilter =
      savedFiltersData?.[screen?.toLowerCase()]?.find((filter) =>
        appliedSavedFilter?.filter_id
          ? appliedSavedFilter?.filter_id === filter?.filter_id
          : filter?.is_default
      ) || null;

    return (
      <div>
        <Header
          listFilter={listFilter}
          setListFilter={setListFilter}
          handleNewFilter={handleNewFilter}
          screen={screen}
        />
        <div className="cards-container">
          {Object.keys(activeFilter || {})?.length > 0 && (
            <SavedFilterCard
              filter={activeFilter}
              isActive
              onFilterStripClick={onFilterStripClick}
              openWarningModal={openWarningModal}
              handleCopyClick={handleCopyClick}
              handleEditFilter={handleEditFilter}
            />
          )}
          <div className="horizontal-divider" />
          {filteredData?.length === 0 ? (
            <div className="marginTop-20">
              <EmptyData text="No saved filters found" />
            </div>
          ) : (
            <div className="sub-cards-container">
              {filteredData
                ?.filter((item) => item?.filter_id !== activeFilter?.filter_id)
                ?.map((filter) => (
                  <SavedFilterCard
                    filter={filter}
                    onFilterStripClick={onFilterStripClick}
                    openWarningModal={openWarningModal}
                    handleCopyClick={handleCopyClick}
                    handleEditFilter={handleEditFilter}
                  />
                ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (editFilter) {
      setFilterBasicInfo({
        filterName: editFilter?.filter_name,
        filterDescription: editFilter?.description,
        isDefault: editFilter?.is_default,
        isMultipleScreens: editFilter?.is_multi_screen
          ? "all_screens"
          : "this_screen",
        scope: editFilter?.scope,
      });
    }
  }, [editFilter]);

  return (
    <div className="width100">
      <div className="positionRelative width100">
        <LoaderComponent>
          <div className="saved-filters-container">{renderContent()}</div>
          {isWarningModalOpen && (
            <WarningModal
              data={warningModalData}
              onClose={closeWarningModal}
              screen={screen}
            />
          )}
        </LoaderComponent>
      </div>
      {savedFiltersView !== "landing" && (
        <div className="saved-filters-footer">
          <Button
            variant="text"
            size="large"
            onClick={handleToggleSavedFiltersLanding}
          >
            Back
          </Button>
          {savedFiltersView === "saveFilter" ? (
            <Button variant="primary" size="large" onClick={handleSaveFilter}>
              Save
            </Button>
          ) : (
            <Button
              variant="primary"
              size="large"
              onClick={
                isCopyFlow ? handleCopyFilter : () => handleApplyFilter()
              }
            >
              {isCopyFlow ? "Copy" : "Apply"}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default SavedFilters;
