import React from "react";
import { Tag } from "impact-ui";
import moment from "moment";
import {
  DEFAULT_DATE_FORMAT,
} from "../../../../constants/Constants";
import {
  replaceSpecial<PERSON><PERSON><PERSON>,
  snakeToNormalCase,
} from "../../../../utils/helpers/utility_helpers";
import { data_info_content_map } from "../FilterConstants";

const DateInfo = React.memo(({ data }) => {
  const {
    dateRange = {},
    show_partially_overlapping_events = null,
    metrics_display_mode = null,
    event = {},
    priority_numbers = {},
    promo = {},
  } = data || {};

  const modifiedStructure = {
    start_date: dateRange.start_date
      ? [moment(dateRange.start_date).format(DEFAULT_DATE_FORMAT)]
      : null,
    end_date: dateRange.end_date
      ? [moment(dateRange.end_date).format(DEFAULT_DATE_FORMAT)]
      : null,
    event: event?.selectedOptions?.length ? event?.selectedOptions?.map((item) => item?.label) : null,
    priority_numbers: priority_numbers.selectedOptions?.length ? priority_numbers.selectedOptions?.map((item) => item?.label) : null,
    promo: promo.selectedOptions?.length ? promo.selectedOptions?.map((item) => item?.label) : null,
    show_partially_overlapping_events:
      show_partially_overlapping_events ? [show_partially_overlapping_events] : null,
    metrics_display_mode: metrics_display_mode ? [snakeToNormalCase(metrics_display_mode)] : null,
  };

  return (
    <div className="flexColumnWithGap12">
      <p className="secondaryText-14-700">Date Range</p>
      {data_info_content_map?.map((item) => {
        if (modifiedStructure?.[item?.value]?.length) {
          console.log(modifiedStructure?.[item?.value]);
          return (
            <div className="flexWithGap8">
              <p className="text-12-500 label-lhs">{item?.label}</p>
              {modifiedStructure?.[item?.value]?.map((value) => (
                <Tag
                  key={value}
                  label={replaceSpecialCharacter(value)}
                  size="small"
                  variant="stroke"
                />
              ))}
            </div>
          );
        }
      })}
    </div>
  );
});

export default DateInfo;
