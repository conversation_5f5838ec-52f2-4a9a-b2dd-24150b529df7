import React from "react";
import { Input, Select, TextArea, RadioButtonGroup, Switch } from "impact-ui";
import { useDispatch, useSelector } from "react-redux";
import { setEditFilter } from "../../../../store/features/filters/filters";

const SaveInfo = ({ filterBasicInfo, setFilterBasicInfo, screen }) => {
  const dispatch = useDispatch();
  const { savedFiltersData } = useSelector(
    (store) => store?.pricesmartPromoReducer.filters
  );

  const [savedFiltersList, setSavedFiltersList] = React.useState({
    isOpen: false,
    selectedOption: null,
  });

  //* if user chooses a dropdown value, then we need to consider it as an edit flow.
  const handleSelectChange = (selectedOption) => {
    dispatch(
      setEditFilter(
        savedFiltersData?.[screen?.toLowerCase()]?.find(
          (filter) => filter?.filter_id === selectedOption?.value
        )
      )
    );
  };

  return (
    <div className="save-info">
      <p className="text-16-800">Save Filter</p>
      <p className="label-14px-normal marginTop-8">
        Save the filter for quick sorting
      </p>
      <div className="flexWithGap12 flexColumn">
        <div className="flexWithGap8 marginTop-12">
          <Input
            label="Filter Name"
            placeholder="Filter Name"
            maxLength={50}
            defaultValue={filterBasicInfo?.filterName}
            onChange={(e) =>
              setFilterBasicInfo((prev) => ({
                ...prev,
                filterName: e.target.value,
              }))
            }
            isRequired
            value={filterBasicInfo?.filterName}
            disabled={savedFiltersList?.selectedOption}
          />
          <p className="label-12px-normal marginTop-20">or</p>
          <Select
            initialOptions={
              savedFiltersData?.[screen?.toLowerCase()]?.map((filter) => ({
                label: filter?.filter_name,
                value: filter?.filter_id,
              })) || []
            }
            currentOptions={
              savedFiltersData?.[screen?.toLowerCase()]?.map((filter) => ({
                label: filter?.filter_name,
                value: filter?.filter_id,
              })) || []
            }
            label="Select saved filter"
            name="select_saved_filter"
            placeholder="Select.."
            isClearable
            isCloseWhenClickOutside
            isMandatory
            selectedOptions={savedFiltersList?.selectedOption}
            setSelectedOptions={(selectedOption) => {
              setSavedFiltersList((prev) => ({ ...prev, selectedOption }));
            }}
            handleChange={handleSelectChange}
            isOpen={savedFiltersList?.isOpen}
            setIsOpen={(isOpen) =>
              setSavedFiltersList((prev) => ({ ...prev, isOpen }))
            }
          />
        </div>
        <TextArea
          characterLimit={300}
          defaultValue={filterBasicInfo?.filterDescription}
          height="50px"
          width="100%"
          label="Description"
          maxRows={5}
          onChange={(e) =>
            setFilterBasicInfo((prev) => ({
              ...prev,
              filterDescription: e.target.value,
            }))
          }
          placeholder="Tell us about your filter"
          value={filterBasicInfo?.filterDescription}
        />
        <Switch
          leftLabel="Mark filter as default"
          onChange={(e, checked) =>
            setFilterBasicInfo((prev) => ({ ...prev, isDefault: checked }))
          }
          checked={filterBasicInfo?.isDefault}
        />
        <div className="flexWithGap12">
          <p className="label-14px-normal">Applicable to</p>
          <RadioButtonGroup
            name="ia-test-radio-group"
            onChange={(value, option) =>
              setFilterBasicInfo((prev) => ({
                ...prev,
                isMultipleScreens: option,
              }))
            }
            options={[
              {
                label: "This Screen",
                value: "this_screen",
              },
              {
                label: "All Screens",
                value: "all_screens",
              },
            ]}
            orientation="row"
            selectedOption={filterBasicInfo?.isMultipleScreens}
          />
        </div>
        <div className="flexWithGap12">
          <p className="label-14px-normal">Save as</p>
          <RadioButtonGroup
            name="ia-test-radio-group"
            onChange={(value, option) => {
              setFilterBasicInfo((prev) => ({ ...prev, scope: option }));
            }}
            options={[
              {
                label: "Personal",
                value: "personal",
              },
              {
                label: "Global",
                value: "global",
              },
            ]}
            orientation="row"
            selectedOption={filterBasicInfo?.scope}
          />
        </div>
      </div>
    </div>
  );
};

export default SaveInfo;
