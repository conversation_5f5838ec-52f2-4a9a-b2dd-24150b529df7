import React from "react";
import ProfileIcon from "../../../../assets/imageAssets/user.svg?.url";
import CopyIcon from "../../../../assets/imageAssets/copyIcon.svg?.url";
import DeleteIcon from "../../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../../assets/imageAssets/editIcon.svg?.url";
import Star from "../../../../components/icons/star";
import GlobalIcon from "../../../../assets/imageAssets/globe.svg?.url";

const SavedFilterCard = ({
  filter,
  isActive = false,
  onFilterStripClick,
  openWarningModal,
  handleCopyClick,
  handleEditFilter,
}) => {
  const handleIconClick = (e, flow) => {
    e.stopPropagation();
    openWarningModal({ flow, ...filter });
  };

  return (
    <div
      className={`flexContentAround saved-filter-card ${
        isActive ? "active" : ""
      }`}
      onClick={() => onFilterStripClick(filter)}
      key={filter?.id}
    >
      <p className="text-14-600">{filter?.filter_name}</p>
      <div className="flexWithGap8">
        <img
          src={filter?.scope === "personal" ? ProfileIcon : GlobalIcon}
          width={13}
          alt="profileIcon"
        />
        <div className="vertical-divider" />
        <div className="flexWithGap8">
          <img
            src={CopyIcon}
            width={20}
            alt="copyIcon"
            onClick={(e) => {
              e.stopPropagation();
              handleCopyClick(filter);
            }}
          />
          <img
            src={EditIcon}
            width={20}
            alt="penIcon"
            onClick={(e) => {
              e.stopPropagation();
              handleEditFilter(filter);
            }}
          />
          <img
            src={DeleteIcon}
            width={20}
            alt="DeleteIcon"
            onClick={(e) => handleIconClick(e, "delete")}
          />
          <div onClick={(e) => handleIconClick(e, "default")}>
            <Star fill={filter?.is_default ? "#E1BC29" : "#D9DDE7"} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavedFilterCard;
