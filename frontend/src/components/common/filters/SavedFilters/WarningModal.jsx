import React from "react";
import { Prompt } from "impact-ui";
import { useDispatch } from "react-redux";
import {
  deleteSavedFilter,
  getSavedFiltersData,
  setSavedFiltersView,
  updateSavedFilterDefaultStatus,
} from "../../../../store/features/filters/filters";

const WarningModal = ({ data, onClose, screen }) => {
  const dispatch = useDispatch();

  const handlePrimaryButtonClick = async () => {
    let response;
    if (data?.flow === "delete") {
      response = await dispatch(deleteSavedFilter(data?.filter_id));
    } else {
      response = await dispatch(
        updateSavedFilterDefaultStatus({
          filter_id: data?.filter_id,
          is_default: !data?.is_default,
          screen_name: screen?.toLowerCase(),
        })
      );
    }

    if (response) {
      onClose();
      dispatch(getSavedFiltersData(screen?.toLowerCase(), true));
      dispatch(setSavedFiltersView("landing"));
    }
  };

  const flowContent = {
    delete: {
      title: "Are you sure?",
      content: `This will delete the saved filter "${data?.filter_name}".`,
      variant: "error",
    },
    default: {
      title: "Are you sure?",
      content: `This will mark the saved filter "${data?.filter_name}" as default.`,
      variant: "warning",
    },
  };
  return (
    <Prompt
      isOpen
      handleClose={onClose}
      onSecondaryButtonClick={onClose}
      onPrimaryButtonClick={handlePrimaryButtonClick}
      primaryButtonLabel="Yes"
      secondaryButtonLabel="No"
      title={flowContent[data?.flow]?.title}
      variant={flowContent[data?.flow]?.variant}
    >
      {flowContent[data?.flow]?.content}
    </Prompt>
  );
};

export default WarningModal;
