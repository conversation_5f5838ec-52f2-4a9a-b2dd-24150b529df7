import React, { useEffect } from "react";
import { <PERSON><PERSON>, Tag, Select, Menu, Tooltip, Input } from "impact-ui";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import { useDispatch, useSelector } from "react-redux";
import Star from "../../../icons/star";
import personalImage from "../../../../assets/imageAssets/user.svg?.url";
import globalImage from "../../../../assets/imageAssets/globe.svg?.url";
import CopyIcon from "../../../../assets/imageAssets/copyIcon.svg";
import DeleteIcon from "../../../../assets/imageAssets/deleteIcon.svg";
import EditIcon from "../../../../assets/imageAssets/editIcon.svg";
import { getSavedFilterById } from "../../../../store/features/filters/filters";
import { global_labels } from "../../../../constants/Constants";
import { setIndividualFilterInfo } from "../../../../store/features/filters/filters";
import { titleMap } from "../FilterConstants";
import { snakeToNormalCase } from "../../../../utils/helpers/utility_helpers";
import DateInfo from "./DateInfo";

const FilterInfo = ({
  selectedFilter,
  screen,
  openWarningModal,
  isCopyFlow,
  setIsCopyFlow,
  setSelectedFilter,
  handleEditFilter,
}) => {
  const dispatch = useDispatch();

  const { savedFiltersData, individualFilterInfo } = useSelector(
    (store) => store?.pricesmartPromoReducer.filters
  );

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [chooseFilterSelect, setChooseFilterSelect] = React.useState({
    isOpen: false,
    selectedOption: null,
  });

  const handleSelectChange = (selectedOption) => {
    setSelectedFilter(
      savedFiltersData?.[screen?.toLowerCase()]?.find(
        (filter) => filter?.filter_id === selectedOption?.value
      )
    );
  };

  const getFilterById = async () => {
    const response = await dispatch(
      getSavedFilterById(selectedFilter?.filter_id)
    );
    dispatch(
      setIndividualFilterInfo({
        data: response,
        activeScreen: screen?.toLowerCase(),
      })
    );
    setSelectedFilter(response);
  };

  useEffect(() => {
    //* If filter is not already in the cached store, get it from the API
    if (
      selectedFilter?.filter_id &&
      !individualFilterInfo?.[screen?.toLowerCase()]?.[
        selectedFilter?.filter_id
      ]
    ) {
      getFilterById();
    } else {
      setSelectedFilter(
        individualFilterInfo?.[screen?.toLowerCase()]?.[
          selectedFilter?.filter_id
        ]
      );
    }

    if (selectedFilter?.filter_id) {
      setChooseFilterSelect((prev) => ({
        ...prev,
        selectedOption: {
          label: selectedFilter?.filter_name,
          value: selectedFilter?.filter_id,
        },
      }));
    }
  }, [selectedFilter?.filter_id]);

  return (
    <div className="saved-filter-info">
      <div className="flexContentAround">
        <p className="text-16-800">
          Saved Filters ({savedFiltersData?.[screen?.toLowerCase()]?.length})
        </p>
        <Select
          initialOptions={
            savedFiltersData?.[screen?.toLowerCase()]?.map((filter) => ({
              label: filter?.filter_name,
              value: filter?.filter_id,
            })) || []
          }
          currentOptions={
            savedFiltersData?.[screen?.toLowerCase()]?.map((filter) => ({
              label: filter?.filter_name,
              value: filter?.filter_id,
            })) || []
          }
          label="Choose"
          labelOrientation="left"
          name="select_saved_filter"
          placeholder="Select.."
          isClearable
          isCloseWhenClickOutside
          isMandatory
          selectedOptions={chooseFilterSelect?.selectedOption}
          setSelectedOptions={(selectedOption) => {
            setChooseFilterSelect((prev) => ({ ...prev, selectedOption }));
          }}
          handleChange={handleSelectChange}
          isOpen={chooseFilterSelect?.isOpen}
          setIsOpen={(isOpen) =>
            setChooseFilterSelect((prev) => ({ ...prev, isOpen }))
          }
        />
      </div>
      <div className="flexContentAround">
        <div className="flexWithGap8">
          {isCopyFlow ? (
            <Input
              value={selectedFilter?.filter_name}
              onChange={(e) => {
                setSelectedFilter((prev) => ({
                  ...prev,
                  filter_name: e.target.value,
                }));
              }}
            />
          ) : (
            <p className="text-14-800 filter-main-label">
              {selectedFilter?.filter_name}
            </p>
          )}
          <div
            onClick={() => {
              if (isCopyFlow) {
                setSelectedFilter((prev) => ({
                  ...prev,
                  is_default: !prev?.is_default,
                }));
              }
            }}
            className={isCopyFlow ? "star-icon" : ""}
          >
            <Star
              fill={
                savedFiltersData?.[screen?.toLowerCase()]?.find(
                  (filter) => filter?.filter_id === selectedFilter?.filter_id
                )?.is_default
                  ? "#FFC107"
                  : "#D9DDE7"
              }
            />
          </div>
        </div>
        <Button
          icon={<MoreHorizIcon />}
          size="large"
          variant="tertiary"
          onClick={(e) => {
            setAnchorEl(e.currentTarget);
          }}
        />
        <Menu
          anchorEl={anchorEl}
          iconPlacement="left"
          onClose={() => {
            setAnchorEl(null);
          }}
          open={Boolean(anchorEl)}
          options={[
            {
              icon: <CopyIcon />,
              label: "Copy",
              onClick: () => setIsCopyFlow(true),
              value: "copy",
            },
            {
              icon: <DeleteIcon />,
              label: "Delete",
              onClick: () => {
                openWarningModal({
                  flow: "delete",
                  ...selectedFilter,
                });
              },
              value: "delete",
            },
            {
              icon: <EditIcon />,
              label: "Edit",
              onClick: () => handleEditFilter(selectedFilter),
              value: "edit",
            },
          ]}
        />
      </div>
      <div>
        <Tag
          label={
            <div className="flexWithGap8">
              <img
                src={
                  selectedFilter?.scope === "personal"
                    ? personalImage
                    : globalImage
                }
                alt={personalImage}
                width={11}
              />
              {selectedFilter?.scope === "personal" ? "Personal" : "Global"}
            </div>
          }
          size="small"
          variant="solid"
        />
      </div>
      <p className="label-14px-normal marginTop-8">
        {selectedFilter?.description}
      </p>
      <div className="horizontal-divider-line"></div>
      <div className="hierachy-container flexColumnWithGap12">
        {Object.keys(selectedFilter?.filter_config || {}).map((key) => {
          if (titleMap?.[key]) {
            return (
              <>
                <div key={key}>
                  <p className="secondaryText-14-700">{titleMap[key]}</p>
                  {Object.entries(selectedFilter?.filter_config?.[key])?.map(
                    ([key, value]) => {
                      return (
                        <div
                          className="selected-options-container marginTop-12"
                          key={key}
                        >
                          <p className="text-12-500 label-lhs">
                            {global_labels?.[key] || snakeToNormalCase(key)}
                          </p>
                          <div className="flexWithGap12 selected-options">
                            {value?.slice(0, 3)?.map((option) => {
                              return (
                                <Tag
                                  label={option?.label}
                                  size="small"
                                  variant="stroke"
                                />
                              );
                            })}
                            {value?.length > 3 && (
                              <Tooltip
                                variant="tertiary"
                                title={
                                  <ul>
                                    {value?.slice(3)?.map((option) => (
                                      <li>{option?.label}</li>
                                    ))}
                                  </ul>
                                }
                              >
                                <div>
                                  <Tag
                                    label={`+ ${value?.length - 3}`}
                                    size="small"
                                    variant="stroke"
                                  />
                                </div>
                              </Tooltip>
                            )}
                          </div>
                        </div>
                      );
                    }
                  )}
                </div>
                <div className="horizontal-divider-line"></div>
              </>
            );
          } else if (key === "dateRange") {
            return (
              <>
                <DateInfo data={selectedFilter?.filter_config} />
                <div className="horizontal-divider-line"></div>
              </>
            );
          }
        })}
      </div>
    </div>
  );
};

export default FilterInfo;
