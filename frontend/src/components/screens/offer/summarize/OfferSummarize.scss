.offer-summarize-panel {
    width: 800px;
}

.offer_summarize_container{
    overflow-y: auto;
    max-height: calc(100vh - 160px);
    scrollbar-width: none;
}

.briefSummaryDetail {
    padding: 12px;
    background-color: #f5f9ec;
    border-radius: 8px;
    width: 100%;
    display: flex;
    gap: 16px;
}

.bestPromoBadge > div > img {
    margin: 0 !important;
    padding-right: 4px;
}

.aiRadientLine {
    height: 1px;
    width: 100%;
    background: linear-gradient(95deg, #be77ee 10.08%, #20accf 93.72%);
}

.aiRadientLineText {
    font-size: 12px;
    white-space: nowrap;
    padding: 0px 8px 3px 8px;
    background: linear-gradient(95deg, #be77ee 10.08%, #20accf 93.72%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.greenVerticalLine {
    width: 1px;
    height: 12px;
    background-color: #26734b;
}

.aiSummaryCardsContainer {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    .aiSummaryCard {
        padding: 12px;
        border-radius: 8px;

        &:nth-child(1) {
            background-color: #eceefd;
        }

        &:nth-child(2) {
            background-color: #f6edfd;
        }

        &:nth-child(3) {
            background-color: #f5f9ec;
        }
    }
}

.aiSummaryDetailsContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;

    > div {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1.5px solid transparent;

        &:nth-child(1) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #26734bcc, #d7e5b2) border-box;
        }

        &:nth-child(2) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #e1bc29, #f8c8ba) border-box;
        }
    }
}

.aiDetailedSummaryDetailsContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;

    > div {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1.5px solid transparent;

        &:nth-child(1) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #82637b, #c4b1bf) border-box;
        }

        &:nth-child(2) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #1789a5, #b7dfe1) border-box;
        }

        &:nth-child(3) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #26734bcc, #d7e5b2) border-box;
        }

        &:nth-child(4) {
            background: linear-gradient(white, white) padding-box,
                linear-gradient(to right, #e1bc29, #f8c8ba) border-box;
        }
    }
}

.discTypeList {
    list-style-type: disc !important;
}


// Loading Box
.loadingBoxContainer {
    border-radius: 4px;
    margin-left: 20px;
    overflow: hidden;
}

.loadingBoxMovable {
    animation-duration: 3s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: linear;
    background: linear-gradient(
        85deg,
        rgba(185, 185, 185, 0.4) 5%,
        #ffffff 30%,
        #ffffff 40%,
        rgba(185, 185, 185, 0.4) 60%
    );

    background-size: 800px 300px;
    height: 100%;
    position: relative;
}

@keyframes placeHolderShimmer {
    0% {
        background-position: -800px 0;
    }
    100% {
        background-position: 800px 0;
    }
}