import React, { memo } from "react";
import _ from "lodash";

import ComparisionInsightIcon from "../../../../assets/imageAssets/comparisionInsightAI.png";
import TradeOffSummaryAIIcons from "../../../../assets/imageAssets/tradeOffSummaryAI.png";
import WhyBestScenarioIcon from "../../../../assets/imageAssets/whyBestScenario.png";
import RiskTradeOffIcon from "../../../../assets/imageAssets/riskTradeOff.png";
import "./OfferSummarize.scss";

const SummarySection = memo(({ icon, title, items }) => {
    if (!items?.length) return null;

    return (
        <div>
            <div className="flexWithGap8">
                <img src={icon} alt={title} style={{ maxHeight: 24 }} />
                <span className="text-16-800">{title}</span>
            </div>
            <ul className="marginLeft-16 marginTop-12 flexWithGap12 flexColumn">
                {items.map((item, index) => (
                    <li key={index} className="text-16-600 discTypeList">
                        {item}
                    </li>
                ))}
            </ul>
        </div>
    );
});

const DetailedSummary = memo(({ data = {} }) => {
    const {
        final_recommendation = [],
        generic_comparison_insights = [],
        tradeoff_summary = [],
        risks = [],
    } = data;

    const sections = [
        {
            icon: ComparisionInsightIcon,
            title: "Generic comparison and insights",
            items: generic_comparison_insights,
        },
        {
            icon: TradeOffSummaryAIIcons,
            title: "Tradeoff summary",
            items: tradeoff_summary,
        },
        {
            icon: WhyBestScenarioIcon,
            title: "Final recommendation",
            items: final_recommendation,
        },
        {
            icon: RiskTradeOffIcon,
            title: "Risks",
            items: risks,
        },
    ];

    return (
        <div className="marginTop-16 aiDetailedSummaryDetailsContainer">
            {sections.map((section, index) => (
                <SummarySection
                    key={index}
                    icon={section.icon}
                    title={section.title}
                    items={section.items}
                />
            ))}
        </div>
    );
});

export default DetailedSummary;
