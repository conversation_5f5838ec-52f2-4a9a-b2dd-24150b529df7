import React from "react";
import { Badge } from "impact-ui";
import _ from "lodash";

import ThumbsUpIcon from "../../../../assets/imageAssets/greenThumbsUp.svg?.url";
import WhyBestScenarioIcon from "../../../../assets/imageAssets/whyBestScenario.png";
import RiskTradeOffIcon from "../../../../assets/imageAssets/riskTradeOff.png";
import "./OfferSummarize.scss";

function BriefSummary(props) {
    const { data, offerTypeConstants } = props;

    return (
        <div className="marginTop-28">
            <div className="briefSummaryDetail flexColumn">
                <div className="flexWithGap8 flexContentAround">
                    <span className="text-16-800">
                        {data?.best_scenario?.scenario_name || ""}
                    </span>
                    <div className="bestPromoBadge">
                        <Badge
                            color="success"
                            icon={<img src={ThumbsUpIcon} alt="ThumbsUpIcon" />}
                            label="Best"
                            onClick={() => {}}
                            size="default"
                            variant="stroke"
                            isIcon={true}
                        />
                    </div>
                </div>
                <div className="marginTop-8 flexWithGap16">
                    <span className="flexWithGap4">
                        <span className="text-16-600">Discount Type:</span>
                        <span className="text-16-800">
                            {
                                offerTypeConstants[
                                    data?.best_scenario?.discount_type
                                ]
                            }
                        </span>
                    </span>
                    <div className="greenVerticalLine" />
                    <span className="flexWithGap4">
                        <span className="text-16-600">Discount Value:</span>
                        <span className="text-16-800">
                            {data?.best_scenario?.discount_value}
                        </span>
                    </span>
                </div>
            </div>
            <div className="marginTop-16 aiSummaryDetailsContainer">
                <div>
                    <div className="flexWithGap8">
                        <img
                            src={WhyBestScenarioIcon}
                            alt="WhyBestScenarioIcon"
                            style={{ maxHeight: 24 }}
                        />
                        <span className="text-16-800">Why best scenario</span>
                    </div>
                    <ul className="marginTop-12 marginLeft-16">
                        {data?.reason && (
                            <li className="text-16-600 discTypeList">
                                {data?.reason}
                            </li>
                        )}
                        {data?.top_metric && (
                            <li className="text-16-600 discTypeList marginTop-12">
                                {data?.top_metric?.charAt(0).toUpperCase() +
                                    data?.top_metric?.slice(1)}
                            </li>
                        )}
                    </ul>
                </div>
                <div>
                    <div className="flexWithGap8">
                        <img
                            src={RiskTradeOffIcon}
                            alt="RiskTradeOffIcon"
                            style={{ maxHeight: 24 }}
                        />
                        <span className="text-16-800">Risk & Trade-offs</span>
                    </div>
                    <ul className="marginLeft-16">
                        {data?.risk.length
                            ? _.map(data?.risk, (risk) => (
                                  <li className="text-16-600 marginTop-12 discTypeList">
                                      {risk}
                                  </li>
                              ))
                            : null}
                    </ul>
                </div>
            </div>
        </div>
    );
}

export default BriefSummary;
