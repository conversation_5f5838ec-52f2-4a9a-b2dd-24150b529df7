import React from "react";
import _ from "lodash";

import WhyBestScenarioIcon from "../../../../assets/imageAssets/whyBestScenario.png";
import RiskTradeOffIcon from "../../../../assets/imageAssets/riskTradeOff.png";
import "./OfferSummarize.scss";

function ModerateSummary(props) {
    const { data } = props;

    return (
        <div className="marginTop-16 aiSummaryDetailsContainer">
            <div>
                <div className="flexWithGap8">
                    <img
                        src={WhyBestScenarioIcon}
                        alt="WhyBestScenarioIcon"
                        style={{ maxHeight: 24 }}
                    />
                    <span className="text-16-800">Recommendation</span>
                </div>
                <ul className="marginTop-12 marginLeft-16 flexWithGap12 flexColumn">
                    {data?.content?.selected_scenario && (
                        <li className="text-16-600 discTypeList">
                            Selected Scenario:{" "}
                            {data?.content?.selected_scenario}
                        </li>
                    )}
                    {data?.content?.reason && (
                        <li className="text-16-600 discTypeList">
                            {data?.content?.reason}
                        </li>
                    )}
                    {data?.content?.top_metric && (
                        <li className="text-16-600 discTypeList">
                            {data?.content?.top_metric}
                        </li>
                    )}
                </ul>
            </div>
            <div>
                <div className="flexWithGap8">
                    <img
                        src={RiskTradeOffIcon}
                        alt="RiskTradeOffIcon"
                        style={{ maxHeight: 24 }}
                    />
                    <span className="text-16-800">Risk & Trade-offs</span>
                </div>
                <ul className="marginLeft-16  marginTop-12 flexWithGap12 flexColumn">
                    {data?.content?.risk?.length
                        ? _.map(data.content.risk, (risk) => (
                              <li className="text-16-600 discTypeList">
                                  {risk}
                              </li>
                          ))
                        : null}
                </ul>
            </div>
        </div>
    );
}

export default ModerateSummary;
