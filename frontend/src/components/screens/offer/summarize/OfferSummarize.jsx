import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Panel, ButtonGroup, Badge } from "impact-ui";
import _ from "lodash";
import moment from "moment";

import BriefSummary from "./BriefSummary";
import ModerateSummary from "./ModerateSummary";
import DetailedSummary from "./DetailedSummary";

import {
    toDollar,
    toPercentage,
    toUnit,
} from "../../../../utils/helpers/formatter";

import {
    setShowSummarizeModal,
    getOptimizationDetailsSummarize,
    getSummarizeResponse,
} from "../../../../store/features/promoReducer/promoReducer";

import AIStarIcon from "../../../../assets/imageAssets/aiStarIcon.svg?.url";
import PromoNameIcon from "../../../../assets/imageAssets/promoNameIcon.png";
import CalendarIcon from "../../../../assets/imageAssets/calendarIconAI.png";
import ThumbsUpIcon from "../../../../assets/imageAssets/greenThumbsUp.svg?.url";

import "./OfferSummarize.scss";

const summaryTypeButton = [
    {
        label: "Brief summary",
        value: "short",
    },
    {
        label: "Moderate summary",
        value: "medium",
    },
    {
        label: "Detailed summary",
        value: "long",
    },
];

function OfferSummarize() {
    const dispatch = useDispatch();
    const {
        showSummarizeModal = false,
        aiSummarizePayload = {},
        activePromoId = null,
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);
    const [selectedSummaryType, setSelectedSummaryType] = useState("short");
    const [showPanel, setShowPanel] = useState(false);
    const [summaryData, setSummaryData] = useState({});
    const [summaryPayload, setSummaryPayload] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [loadingSummaryType, setLoadingSummaryType] = useState("short");

    useEffect(async () => {
        if (showSummarizeModal && activePromoId) {
            const optDataResponse = await dispatch(
                getOptimizationDetailsSummarize({ promo_id: activePromoId })
            );
            if (!_.isEmpty(optDataResponse)) {
                const summarizePayload = {
                    ...aiSummarizePayload,
                    json_3: _.cloneDeep(optDataResponse),
                    summary_type: selectedSummaryType,
                };
                const summarizeResponse = await dispatch(
                    getSummarizeResponse({
                        payload: summarizePayload,
                        useLoader: true,
                    })
                );
                if (!_.isEmpty(summarizeResponse)) {
                    setSummaryData({
                        ...summaryData,
                        [selectedSummaryType]: summarizeResponse,
                    });
                    setShowPanel(true);
                    setSummaryPayload(_.cloneDeep(summarizePayload));
                }
            }
        }
        if (!showSummarizeModal) {
            setSummaryData({});
            setSelectedSummaryType("short");
            setSummaryPayload({});
            setShowPanel(false);
        }
    }, [showSummarizeModal]);

    const handleSummaryTypeChange = async (e) => {
        const summaryType = e.target.value;
        setLoadingSummaryType(summaryType);
        if (!summaryData?.[summaryType]) {
            setIsLoading(true);
            const summarizeResponse = await dispatch(
                getSummarizeResponse({
                    payload: {
                        ...summaryPayload,
                        summary_type: summaryType,
                    },
                    useLoader: false,
                })
            );
            if (!_.isEmpty(summarizeResponse)) {
                setSummaryData({
                    ...summaryData,
                    [summaryType]: summarizeResponse,
                });
                setSelectedSummaryType(summaryType);
                setIsLoading(false);
            }
        } else {
            setSelectedSummaryType(summaryType);
        }
    };

    const moderateDetailedSummary = () => {
        const comparisionCardData =
            selectedSummaryType === "medium"
                ? summaryData?.medium?.sections?.[1]?.content
                : summaryData?.long?.summary_of_all_scenarios;
        return (
            <div className="marginTop-28">
                <div className="aiSummaryCardsContainer">
                    {_.map(comparisionCardData, (item) => {
                        return (
                            <div className="aiSummaryCard flexWithGap8 flexColumn">
                                <div className="flexContentAround paddingBottom-8">
                                    <span className="text-16-800">
                                        {item?.scenario_name}
                                    </span>
                                    {item?.is_best ? (
                                        <div className="bestPromoBadge">
                                            <Badge
                                                color="success"
                                                icon={
                                                    <img
                                                        src={ThumbsUpIcon}
                                                        alt="ThumbsUpIcon"
                                                    />
                                                }
                                                label="Best"
                                                onClick={() => {}}
                                                size="default"
                                                variant="stroke"
                                                isIcon={true}
                                            />
                                        </div>
                                    ) : null}
                                </div>
                                {selectedSummaryType === "long" ? (
                                    <div>
                                        <span className="text-14-600">
                                            GM %:{" "}
                                        </span>
                                        <span className="text-14-800">
                                            {toPercentage({
                                                value: item?.gm_percent,
                                            })}
                                        </span>
                                    </div>
                                ) : null}
                                <div>
                                    <span className="text-14-600">Units: </span>
                                    <span className="text-14-800">
                                        {toUnit({ value: item?.sales_units })}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-14-600">
                                        Revenue:{" "}
                                    </span>
                                    <span className="text-14-800">
                                        {toDollar({ value: item?.revenue })}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-14-600">
                                        Margin:{" "}
                                    </span>
                                    <span className="text-14-800">
                                        {toDollar({ value: item?.margin })}
                                    </span>
                                </div>
                            </div>
                        );
                    })}
                </div>
                {selectedSummaryType === "medium" ? (
                    <ModerateSummary
                        data={summaryData?.medium?.sections?.[2]}
                    />
                ) : (
                    <DetailedSummary data={summaryData?.long} />
                )}
            </div>
        );
    };

    const getOfferSummaryLoadingBox = () => {
        const LoadingBox = ({ width, height, className }) => (
            <div
                className={`loadingBoxContainer ${className}`}
                style={{ width, height }}
            >
                <div className="loadingBoxMovable" />
            </div>
        );

        const SummaryCards = () => (
            <div className="marginTop-28 aiSummaryCardsContainer">
                {[...Array(3)].map((_, i) => (
                    <LoadingBox key={i} height={144} />
                ))}
            </div>
        );

        const LongSummaryBoxes = () => (
            <div>
                {[...Array(2)].map((_, i) => (
                    <LoadingBox key={i} height={152} className="marginTop-16" />
                ))}
            </div>
        );

        return (
            <div className="marginTop-20 offer_summarize_container">
                <LoadingBox
                    width="248px"
                    height="24px"
                    className="marginTop-20"
                />
                <LoadingBox
                    width="248px"
                    height="24px"
                    className="marginTop-16"
                />
                <LoadingBox
                    width="248px"
                    height="24px"
                    className="marginTop-12"
                />
                <SummaryCards />
                <LoadingBox height={152} className="marginTop-16" />
                <LoadingBox height={152} className="marginTop-16" />
                {loadingSummaryType === "long" && <LongSummaryBoxes />}
            </div>
        );
    };

    return (
        <Panel
            anchor="right"
            open={showPanel}
            onClose={() => {
                dispatch(setShowSummarizeModal(false));
                setShowPanel(false);
            }}
            size="large"
            title="Promotion summary"
            className="offer-summarize-panel"
        >
            <div className="center_flex">
                <ButtonGroup
                    selectedOption={selectedSummaryType}
                    onChange={handleSummaryTypeChange}
                    options={summaryTypeButton}
                />
            </div>
            <div className="center_flex marginTop-20">
                <div className="aiRadientLine" />
                <span className="flexWithGap4 aiRadientLineText">
                    <img src={AIStarIcon} alt="aiStarIcon" />
                    <span>Generated by AI</span>
                </span>
                <div className="aiRadientLine" />
            </div>
            {isLoading ? (
                getOfferSummaryLoadingBox()
            ) : (
                <div className="marginTop-20 offer_summarize_container">
                    <div className="text-16-800">
                        {
                            _.find(summaryTypeButton, {
                                value: selectedSummaryType,
                            })?.label
                        }{" "}
                        :
                    </div>
                    <div className="flexWithGap8 marginTop-16">
                        <img
                            src={PromoNameIcon}
                            style={{ maxHeight: "16px" }}
                            alt="promoNameIcon"
                        />
                        <span className="text-16-800">
                            {summaryData?.short?.sections[0].content.promo_name}
                        </span>
                    </div>
                    <div className="flexWithGap8 marginTop-12">
                        <img
                            src={CalendarIcon}
                            style={{ maxHeight: "16px" }}
                            alt="calendarIcon"
                        />
                        <span className="text-16-800">
                            {moment(
                                summaryData?.short?.sections[0].content.date_range.split(
                                    " – "
                                )[0],
                                "MM/DD/YYYY"
                            ).format("MMMM D, YYYY")}{" "}
                            -{" "}
                            {moment(
                                summaryData?.short?.sections[0].content.date_range.split(
                                    " – "
                                )[1],
                                "MM/DD/YYYY"
                            ).format("MMMM D, YYYY")}
                        </span>
                    </div>
                    {selectedSummaryType === "short" ? (
                        <BriefSummary
                            data={
                                summaryData[selectedSummaryType]?.sections[0]
                                    ?.content
                            }
                            offerTypeConstants={offerTypeConstants}
                        />
                    ) : (
                        moderateDetailedSummary()
                    )}
                </div>
            )}
        </Panel>
    );
}

export default OfferSummarize;

const offerTypeConstants = {
    bxgy_percent_off: "BXGY % Off",
    percent_off: "% Off",
    fixed_price: "PP",
    extra_amount_off: "$ Off",
    bmsm: "BMSM",
    tiered_offer: "Tier",
    bxgy: "BXGY",
};
