export const tableDataFormatter = (data = []) => {
	let formattedData = [];
	data.forEach((item) => {
		let formattedItem = {
			...item,
			actualized_st_percent: item?.actualized?.actualized_st_percent,
			actualized_revenue_per_style: item?.actualized?.revenue_per_style,
			actualized_order_discount: item?.actualized?.order_discount,
			actualized_promo_spend: item?.actualized?.promo_spend,
			actualized_sales_units: item?.actualized?.sales_units,
			actualized_revenue: item?.actualized?.revenue,
			actualized_margin: item?.actualized?.margin,
			actualized_margin_percent: item?.actualized?.margin_percent,

			actualized_incremental_sales_units: item?.actualized_incremental?.sales_units,
			actualized_incremental_revenue: item?.actualized_incremental?.revenue,
			actualized_incremental_margin: item?.actualized_incremental?.margin,
			actualized_incremental_margin_percent: item?.actualized_incremental?.margin_percent,

            ia_recommend_discount: item?.ia_recommend?.discount,
			// Finalized
			finalized_margin_percent: item?.finalized?.margin_percent,
			finalized_discount: item?.finalized?.discount,
			finalized_contribution_margin: item?.finalized?.contribution_margin,
			finalized_contribution_margin_percent: item?.finalized?.contribution_margin_percent,
			finalized_sales_units: item?.finalized?.sales_units,
			finalized_revenue: item?.finalized?.revenue,
			finalized_margin: item?.finalized?.margin,
			finalized_promo_spend: item?.finalized?.promo_spend,
			finalized_st_percent: item?.finalized?.finalized_st_percent,
			finalized_inventory: item?.finalized?.total_inventory,
			finalized_revenue_per_style: item?.finalized?.revenue_per_style,
			finalized_order_discount: item?.finalized?.order_discount,
			//Baseline
			baseline_sales_units: item?.baseline?.sales_units,
			baseline_margin: item?.baseline?.margin,
			baseline_revenue: item?.baseline?.revenue,
			baseline_margin_percent: item?.baseline?.margin_percent,

			// Incremental
			incremental_sales_units: item?.incremental?.sales_units,
			incremental_revenue: item?.incremental?.revenue,
			incremental_margin: item?.incremental?.margin,
 
			// for Original selection
			original_sales_units: item?.original?.sales_units,
			original_revenue: item?.original?.revenue,
			original_margin: item?.original?.margin,
			original_margin_percent: item?.original?.margin_percent,
			original_contribution_margin: item?.original?.contribution_margin,
			original_contribution_margin_percent: item?.original?.contribution_margin_percent,
			original_order_discount: item?.original?.order_discount,
			original_promo_spend: item?.original?.promo_spend,

			original_incremental_margin_percent: item?.original_incremental?.margin_percent,

            //--finalized Incremental
            finalized_incremental_sales_units: item?.finalized_incremental?.sales_units,
            finalized_incremental_revenue: item?.finalized_incremental?.revenue,
            finalized_incremental_margin: item?.finalized_incremental?.margin,
            finalized_incremental_margin_percent: item?.finalized_incremental?.margin_percent,

			//  Stacked
			// --- Finalized Stack
			finalized_stack_sales_units: item?.finalized_stack?.sales_units,
			finalized_stack_revenue: item?.finalized_stack?.revenue,
			finalized_stack_margin: item?.finalized_stack?.margin,
			finalized_stack_margin_percent: item?.finalized_stack?.margin_percent,
			finalized_stack_contribution_margin_percent: item?.finalized_stack?.contribution_margin_percent,
			finalized_stack_contribution_margin: item?.finalized_stack?.contribution_margin,
			finalized_stack_st_percent: item?.finalized_stack?.finalized_stack_st_percent,
			finalized_stack_revenue_per_style: item?.finalized_stack?.revenue_per_style,
			finalized_stack_order_discount: item?.finalized_stack?.order_discount,
			finalized_stack_promo_spend: item?.finalized_stack?.promo_spend,
			
            // --- Finalized Stack Incremental
			finalized_stack_incremental_sales_units: item?.finalized_stack_incremental?.sales_units,
			finalized_stack_incremental_margin: item?.finalized_stack_incremental?.margin,
            finalized_stack_incremental_revenue: item?.finalized_stack_incremental?.revenue,
			finalized_stack_incremental_margin_percent: item?.finalized_stack_incremental?.margin_percent,
			// --- Baseline Stack
			baseline_stack_sales_units: item?.stack_baseline?.sales_units,
			baseline_stack_revenue: item?.stack_baseline?.revenue,
			baseline_stack_margin: item?.stack_baseline?.margin,
			baseline_stack_margin_percent: item?.stack_baseline?.margin_percent,
			// --- Original Stack
			original_stack_sales_units: item?.original_stack?.sales_units,
			original_stack_revenue: item?.original_stack?.revenue,
			original_stack_margin: item?.original_stack?.margin,
			original_stack_margin_percent: item?.original_stack?.margin_percent,
			original_stack_contribution_margin_percent: item?.original_stack?.contribution_margin_percent,
			original_stack_contribution_margin: item?.original_stack?.contribution_margin,
			original_stack_order_discount: item?.original_stack?.order_discount,


		};
		formattedData.push(formattedItem);
	});
	return formattedData;
}