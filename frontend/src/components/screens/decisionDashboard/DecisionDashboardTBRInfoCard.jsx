import React from "react";
import { DEFAULT_TBR_METRIC } from "./DecisionDashboardConstants";

const TbrInfoCard = ({ data, tbrMetric = DEFAULT_TBR_METRIC }) => {
	const { label, icon, value } = data;
	return (
		<div className="content_container_border tbr-info-card">
			<div className="tbr-info-card-header">
				<img src={icon} alt="icon" className="info-card-image" />
				<span className="text-16-600 tbr-info-card-title">{label}</span>
			</div>
			<div className="tbr-info-card-grid">
				{tbrMetric.map((metricLabel) => (
					<div className="tbr-info-card-item" key={metricLabel}>
						<div className="tbr-info-card-label">{metricLabel}</div>
						<div className="tbr-info-card-value">{value?.[metricLabel]}</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default TbrInfoCard;
