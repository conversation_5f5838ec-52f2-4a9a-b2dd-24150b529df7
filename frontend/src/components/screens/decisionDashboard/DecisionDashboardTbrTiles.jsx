import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Carousel from "../../ui/carousel/Carousel";
import _ from "lodash";

import { labelCurrencyHandler } from "../../../utils/helpers/utility_helpers";
import * as formatters from "../../../utils/helpers/formatter";

import TbrInfoCard from "./DecisionDashboardTBRInfoCard";

import { 
	decisionDashboardTbrTilesConfig,
 } from "./DecisionDashboardConstants";

function DecisionDashboardTbrTiles() {
	const [tilesData, setTilesData] = useState([]);
	const { decisionDashboardTBRChartsData = {} } = useSelector(
		(store) => store?.pricesmartPromoReducer.decisionDashboard
	);

	const tbrMetric = {
		forecasted: "Forecasted", 
		actualized: "Actual",
		ly: "Last year",
		plan: "Plan"
	}

	const tilesDataFormatter = (data, tilesConfig, formatters_params) => {
		const tilesData = [];

		_.forEach(tilesConfig, (config) => {
			const tilesMEtricsData = {}
			Object.keys(tbrMetric).forEach(metric => {
				tilesMEtricsData[tbrMetric[metric]] = formatters[config.formatter]({
					value: data[`${metric}_${config.key}`],
					...formatters_params,
				})
				
			})
			tilesData.push({
				label: labelCurrencyHandler(config.label, formatters_params?.currency_symbol || "$"),
				value: tilesMEtricsData,
				icon: config.icon,
			});
		});
	
		return tilesData;
	}

	useEffect(() => {
		if (!_.isEmpty(decisionDashboardTBRChartsData) && decisionDashboardTBRChartsData?.Total?.length) {
			//get metrics data from the api response
			const tiles = decisionDashboardTBRChartsData?.Total?.[0];
			const {
                currency_id = null,
                currency_name = null,
                currency_symbol = null
            } = decisionDashboardTBRChartsData?.Total?.[0] || {};
            
            const formatters_params = {
                currency_id,
                currency_name,
                currency_symbol
            };
			
			const tilesData = tilesDataFormatter(
				tiles,
				decisionDashboardTbrTilesConfig,
				formatters_params
			);

			setTilesData(_.cloneDeep(tilesData));
		}
	}, [decisionDashboardTBRChartsData]);

	return (
		<div className="carousel_container marginTop-20">
			<Carousel>
				{tilesData.length
					? _.map(tilesData, (data, idx) => {
						return <TbrInfoCard key={idx} data={data} />;
					  })
					: null}
			</Carousel>
		</div>
	);
}

export default DecisionDashboardTbrTiles;
