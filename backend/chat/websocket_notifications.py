from typing import List, Dict, Optional, Set
from chat.websocket_manager import websocket_manager
from chat import data as chat_data
from chat import queries as chat_queries
from chat import constants as chat_constants
from pricesmart_common.utils import async_execute_query, get_str_repr, get_array_format
from configuration.environment import environment
from logger.logger import logger
import asyncio


class WebSocketNotificationService:
    """
    Service for sending websocket notifications about topic updates.
    
    This service handles:
    - Topic creation notifications
    - Member addition/removal notifications
    - Topic deletion notifications
    - Data aggregation for affected users
    """
    
    @staticmethod
    async def notify_topic_creation(topic_id: int, object_type: str, object_ids: List[int], creator_user_id: int):
        """
        Notify users about a new topic creation.
        Only the creator gets notified initially.
        
        Args:
            topic_id: The newly created topic ID
            object_type: The object type for the topic
            object_ids: List of object IDs for the topic
            creator_user_id: The user who created the topic
        """
        try:

            logger.info(f"Notifying topic creation for topic {topic_id} to {creator_user_id}. Type of creator_user_id: {type(creator_user_id)}")  

            # Get connected users for this object type
            connected_users = websocket_manager.get_connected_users(object_type)

            
            if not connected_users:
                logger.debug(f"No connected users for object type {object_type}")
                return
            
            # For topic creation, only notify the creator
            target_users = [creator_user_id] if creator_user_id in connected_users else []
            
            if not target_users:
                logger.debug(f"Creator {creator_user_id} not connected to {object_type} websocket")
                return
            
            # Get topic data for the target users using optimized query
            user_data_map = await WebSocketNotificationService._get_topic_data_for_users_optimized(
                object_type, object_ids, target_users
            )
            
            # Broadcast to connected users with enhanced message structure
            await websocket_manager.broadcast_to_users(
                obj_type=object_type,
                user_data_map=user_data_map,
                action=chat_constants.TOPIC_CREATED,
                topic_id=topic_id,
                object_ids=object_ids,
                triggered_user=creator_user_id
            )
            
            logger.info(f"Notified topic creation for topic {topic_id} to {len(target_users)} users")
            
        except Exception as e:
            logger.error(f"Error notifying topic creation for topic {topic_id}: {str(e)}")
    
    @staticmethod
    async def notify_member_changes(topic_id: int, object_type: str, object_ids: List[int], 
                                  affected_user_ids: List[int], action: str = "added"):
        """
        Notify users about member additions or removals.
        
        Args:
            topic_id: The topic ID
            object_type: The object type for the topic
            object_ids: List of object IDs for the topic
            affected_user_ids: List of user IDs that were added/removed
            action: Either "added" or "removed"
        """
        try:
            # Get connected users for this object type
            connected_users = websocket_manager.get_connected_users(object_type)
            
            if not connected_users:
                logger.debug(f"No connected users for object type {object_type}")
                return
            
            # For member changes, notify all affected users who are connected
            target_users = [user_id for user_id in affected_user_ids if user_id in connected_users]
            
            if not target_users:
                logger.debug(f"No affected users connected to {object_type} websocket")
                return
            
            # Get topic data for the target users using optimized query
            user_data_map = await WebSocketNotificationService._get_topic_data_for_users_optimized(
                object_type, object_ids, target_users
            )
            
            # Map action string to constant
            action_constant = chat_constants.MEMBERS_ADDED if action == "added" else chat_constants.MEMBERS_REMOVED
            
            # Broadcast to connected users with enhanced message structure
            await websocket_manager.broadcast_to_users(
                obj_type=object_type,
                user_data_map=user_data_map,
                action=action_constant,
                topic_id=topic_id,
                object_ids=object_ids,
                triggered_user=affected_user_ids[0] if affected_user_ids else 0  # Use first affected user as trigger
            )
            
            logger.info(f"Notified member {action} for topic {topic_id} to {len(target_users)} users")
            
        except Exception as e:
            logger.error(f"Error notifying member {action} for topic {topic_id}: {str(e)}")
    
    @staticmethod
    async def notify_topic_deletion(topic_id: int, object_type: str, object_ids: List[int], 
                                   member_user_ids: List[int], deleted_by_user_id: int):
        """
        Notify users about topic deletion.
        
        Args:
            topic_id: The deleted topic ID
            object_type: The object type for the topic
            object_ids: List of object IDs for the topic
            member_user_ids: List of user IDs who were members
            deleted_by_user_id: The user who deleted the topic
        """
        try:
            # Get connected users for this object type
            connected_users = websocket_manager.get_connected_users(object_type)
            
            if not connected_users:
                logger.debug(f"No connected users for object type {object_type}")
                return
            
            # Notify all members who are connected
            target_users = [user_id for user_id in member_user_ids if user_id in connected_users]
            
            if not target_users:
                logger.debug(f"No members connected to {object_type} websocket")
                return
            
            # Get updated topic data for the target users (topic should be gone now)
            user_data_map = await WebSocketNotificationService._get_topic_data_for_users_optimized(
                object_type, object_ids, target_users
            )
            
            # Broadcast to connected users with enhanced message structure
            await websocket_manager.broadcast_to_users(
                obj_type=object_type,
                user_data_map=user_data_map,
                action=chat_constants.TOPIC_DELETED,
                topic_id=topic_id,
                object_ids=object_ids,
                triggered_user=deleted_by_user_id
            )
            
            logger.info(f"Notified topic deletion for topic {topic_id} to {len(target_users)} users")
            
        except Exception as e:
            logger.error(f"Error notifying topic deletion for topic {topic_id}: {str(e)}")
    
    @staticmethod
    async def handle_topic_created(topic_id: int, object_type: str, object_ids: List[int], 
                                 creator_user_id: int) -> None:
        """
        Handle websocket notifications after a topic is created.
        
        Args:
            topic_id: The newly created topic ID
            object_type: The object type for the topic
            object_ids: List of object IDs for the topic
            creator_user_id: The user who created the topic
        """
        try:
            logger.info(f"Sending websocket notification for topic creation: {topic_id}")
            
            # Send notification to creator (only creator gets notified initially)
            await WebSocketNotificationService.notify_topic_creation(
                topic_id=topic_id,
                object_type=object_type,
                object_ids=object_ids,
                creator_user_id=creator_user_id
            )
            
        except Exception as e:
            logger.error(f"Error sending websocket notification for topic creation {topic_id}: {str(e)}")
    
    @staticmethod
    async def handle_members_added(topic_id: int, added_user_ids: List[int]) -> None:
        """
        Handle websocket notifications after members are added to a topic.
        
        Args:
            topic_id: The topic ID
            added_user_ids: List of user IDs that were added
        """
        try:
            # Get topic info to determine object type and IDs
            topic_info = await WebSocketNotificationService._get_topic_info(topic_id)
            if not topic_info:
                logger.warning(f"Could not find topic info for websocket notification: {topic_id}")
                return
            
            logger.info(f"Sending websocket notification for members added to topic: {topic_id}")
            
            # Send notification to added members
            await WebSocketNotificationService.notify_member_changes(
                topic_id=topic_id,
                object_type=topic_info['object_type'],
                object_ids=topic_info['object_ids'],
                affected_user_ids=added_user_ids,
                action="added"
            )
            
        except Exception as e:
            logger.error(f"Error sending websocket notification for members added to topic {topic_id}: {str(e)}")
    
    @staticmethod
    async def handle_members_removed(topic_id: int, removed_user_ids: List[int]) -> None:
        """
        Handle websocket notifications after members are removed from a topic.
        
        Args:
            topic_id: The topic ID
            removed_user_ids: List of user IDs that were removed
        """
        try:
            # Get topic info to determine object type and IDs
            topic_info = await WebSocketNotificationService._get_topic_info(topic_id)
            if not topic_info:
                logger.warning(f"Could not find topic info for websocket notification: {topic_id}")
                return
            
            logger.info(f"Sending websocket notification for members removed from topic: {topic_id}")
            
            # Send notification to removed members
            await WebSocketNotificationService.notify_member_changes(
                topic_id=topic_id,
                object_type=topic_info['object_type'],
                object_ids=topic_info['object_ids'],
                affected_user_ids=removed_user_ids,
                action="removed"
            )
            
        except Exception as e:
            logger.error(f"Error sending websocket notification for members removed from topic {topic_id}: {str(e)}")
    
    @staticmethod
    async def handle_topic_deleted(topic_id: int) -> None:
        """
        Handle websocket notifications before a topic is deleted.
        
        Note: This should be called BEFORE the topic is actually deleted
        so we can still get topic info and member list.
        
        Args:
            topic_id: The topic ID that will be deleted
        """
        try:
            # Get topic info and members before deletion
            topic_info = await WebSocketNotificationService._get_topic_info(topic_id)
            if not topic_info:
                logger.warning(f"Could not find topic info for deletion notification: {topic_id}")
                return
            
            # Get all members of the topic
            members = await chat_data.get_topic_members(topic_id)
            member_user_ids = [member["user_id"] for member in members]
            
            logger.info(f"Sending websocket notification for topic deletion: {topic_id}")
            
            # Send notification to all members
            await WebSocketNotificationService.notify_topic_deletion(
                topic_id=topic_id,
                object_type=topic_info['object_type'],
                object_ids=topic_info['object_ids'],
                member_user_ids=member_user_ids,
                deleted_by_user_id=0  # This should be passed from the service layer
            )
            
        except Exception as e:
            logger.error(f"Error sending websocket notification for topic deletion {topic_id}: {str(e)}")
    
    @staticmethod
    async def handle_topic_deleted_with_user(topic_id: int, deleted_by_user_id: int) -> None:
        """
        Handle websocket notifications before a topic is deleted with the user who deleted it.
        
        Note: This should be called BEFORE the topic is actually deleted
        so we can still get topic info and member list.
        
        Args:
            topic_id: The topic ID that will be deleted
            deleted_by_user_id: The user ID who deleted the topic
        """
        try:
            # Get topic info and members before deletion
            topic_info = await WebSocketNotificationService._get_topic_info(topic_id)
            if not topic_info:
                logger.warning(f"Could not find topic info for deletion notification: {topic_id}")
                return
            
            # Get all members of the topic
            members = await chat_data.get_topic_members(topic_id)
            member_user_ids = [member["user_id"] for member in members]
            
            logger.info(f"Sending websocket notification for topic deletion: {topic_id} by user: {deleted_by_user_id}")
            
            # Send notification to all members
            await WebSocketNotificationService.notify_topic_deletion(
                topic_id=topic_id,
                object_type=topic_info['object_type'],
                object_ids=topic_info['object_ids'],
                member_user_ids=member_user_ids,
                deleted_by_user_id=deleted_by_user_id
            )
            
        except Exception as e:
            logger.error(f"Error sending websocket notification for topic deletion {topic_id}: {str(e)}")
    
    @staticmethod
    async def _get_topic_data_for_users_optimized(object_type: str, object_ids: List[int],
                                                 user_ids: List[int]) -> Dict[int, List[dict]]:
        """
        Get topic data for specific users using the optimized multi-user query with fallback.

        Args:
            object_type: The object type
            object_ids: List of object IDs
            user_ids: List of user IDs to get data for

        Returns:
            Dict mapping user_id to their topic data
        """
        try:
            # Use the new optimized query that gets data for all users in one go
            logger.debug(f"Getting topic data for {len(user_ids)} users using optimized batch query")
            result = await chat_data.get_topics_by_objects_multi_user(object_type, object_ids, user_ids)

            # Ensure all requested users have entries (even if empty)
            for user_id in user_ids:
                if user_id not in result:
                    result[user_id] = []

            return result

        except Exception as e:
            logger.error(f"Error getting topic data for users {user_ids}: {str(e)}")
            # Fallback to individual queries if optimized query fails
            logger.warning("Falling back to individual user queries")
            return await WebSocketNotificationService._get_topic_data_fallback(object_type, object_ids, user_ids)

    @staticmethod
    async def _get_topic_data_fallback(object_type: str, object_ids: List[int], user_ids: List[int]) -> Dict[int, List[dict]]:
        """
        Fallback method using individual queries for each user.

        Args:
            object_type: The object type
            object_ids: List of object IDs
            user_ids: List of user IDs to get data for

        Returns:
            Dict mapping user_id to their topic data
        """
        user_data_map = {}

        for user_id in user_ids:
            try:
                data = await chat_data.get_topics_by_objects(object_type, object_ids, user_id)
                user_data_map[user_id] = data
            except Exception as e:
                logger.error(f"Error getting topic data for user {user_id}: {str(e)}")
                user_data_map[user_id] = []

        return user_data_map
    
# Deprecated function removed - use _get_topic_data_for_users_optimized instead
    
    @staticmethod
    async def _get_topic_members(topic_id: int) -> List[int]:
        """
        Get all member user IDs for a topic.
        
        Args:
            topic_id: The topic ID
            
        Returns:
            List of user IDs who are members of the topic
        """
        try:
            members = await chat_data.get_topic_members(topic_id)
            return [member["user_id"] for member in members]
        except Exception as e:
            logger.error(f"Error getting topic members for topic {topic_id}: {str(e)}")
            return []
    
    @staticmethod
    async def _get_topic_info(topic_id: int) -> Optional[dict]:
        """
        Get basic topic information needed for websocket notifications.
        
        Args:
            topic_id: The topic ID
            
        Returns:
            Dict with object_type and object_ids, or None if topic not found
        """
        try:
            # Use the existing data function to get topic by ID
            topic_data = await chat_data.get_topics_by_topic_id_with_members(topic_id)
            
            if not topic_data:
                return None
            
            topic = topic_data[0]
            return {
                'object_type': topic['object_type'],
                'object_ids': topic['object_ids']
            }
            
        except Exception as e:
            logger.error(f"Error getting topic info for {topic_id}: {str(e)}")
            return None


# Global notification service instance
notification_service = WebSocketNotificationService()
