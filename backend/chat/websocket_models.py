from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class WebSocketEventType(str, Enum):
    """Types of websocket events"""
    TOPIC_UPDATE = "topic_update"
    MEMBER_UPDATE = "member_update"
    CONNECTION_STATUS = "connection_status"
    ERROR = "error"


class WebSocketAction(str, Enum):
    """Types of websocket actions"""
    TOPIC_CREATED = "topic_created"
    TOPIC_UPDATED = "topic_updated"
    TOPIC_DELETED = "topic_deleted"
    MEMBERS_ADDED = "members_added"
    MEMBERS_REMOVED = "members_removed"
    MESSAGE_CREATED = "message_created"
    MESSAGE_UPDATED = "message_updated"
    MESSAGE_DELETED = "message_deleted"


class WebSocketMessage(BaseModel):
    """Base websocket message structure"""
    type: WebSocketEventType
    obj_type: str
    timestamp: str
    data: Optional[Dict[str, Any]] = None
    message: Optional[str] = None


class TopicUpdateNotification(BaseModel):
    """Enhanced notification for topic updates with detailed information"""
    type: WebSocketEventType = WebSocketEventType.TOPIC_UPDATE
    obj_type: str
    action: WebSocketAction
    topic_id: int
    object_ids: List[int]
    triggered_user: int
    data: List[Dict[str, Any]]
    timestamp: str
    
    @field_validator("obj_type")
    @classmethod
    def validate_object_type(cls, value):
        valid_types = ["promo", "event", "store_group", "product"]
        if value not in valid_types:
            raise ValueError(f"Invalid object type. Must be one of: {', '.join(valid_types)}")
        return value


class WebSocketConnectionParams(BaseModel):
    """Parameters for websocket connection"""
    obj_type: str = Field(..., description="Object type to subscribe to")
    user_id: int = Field(..., description="User ID for the connection")
    
    @field_validator("obj_type")
    @classmethod
    def validate_object_type(cls, value):
        valid_types = ["promo", "event", "store_group", "product"]
        if value not in valid_types:
            raise ValueError(f"Invalid object type. Must be one of: {', '.join(valid_types)}")
        return value


class WebSocketStats(BaseModel):
    """WebSocket connection statistics"""
    total_active: int
    by_object_type: Dict[str, int]
    timestamp: str
