# Chat WebSocket System

This document describes the WebSocket implementation for real-time chat topic updates in the PriceSmart Promo application.

## Overview

The WebSocket system enables real-time notifications for chat topic updates, allowing the frontend to automatically update the UI when:
- New topics are created
- Members are added to or removed from topics
- Topics are deleted/archived

## Architecture

The system consists of several components:

1. **WebSocketManager** (`websocket_manager.py`) - Manages connections and broadcasting
2. **WebSocketNotificationService** (`websocket_notifications.py`) - Handles data aggregation and notifications
3. **ChatWebSocketService** (`websocket_service.py`) - Integration layer with existing chat services
4. **WebSocket Controllers** (`websocket_controller.py`) - API endpoints and connection handling
5. **Models** (`websocket_models.py`) - Data structures for WebSocket messages

## Connection Pattern

### WebSocket Endpoint
```
ws://your-domain/chat/ws/{obj_type}/{user_id}
```

**Parameters:**
- `obj_type`: Object type (`promo`, `event`, `store_group`, `product`)
- `user_id`: User ID subscribing to updates

### Example Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/chat/ws/promo/123');

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);
    console.log('Received:', message);
};
```

## Message Format

### Topic Update Messages
```json
{
    "type": "topic_update",
    "obj_type": "promo",
    "data": [
        {
            "obj_id": 1,
            "topics_count": 2,
            "message_count": 5,
            "unread_count": 1,
            "obj_type": "promo"
        }
    ],
    "timestamp": "2024-01-01T12:00:00"
}
```

### Connection Status Messages
```json
{
    "type": "connection_status",
    "obj_type": "promo",
    "message": "Successfully connected to promo updates",
    "timestamp": "2024-01-01T12:00:00",
    "user_id": 123
}
```

### Error Messages
```json
{
    "type": "error",
    "message": "Invalid JSON format",
    "timestamp": "2024-01-01T12:00:00"
}
```

## Client-Side Implementation

### Basic Connection
```javascript
class ChatWebSocket {
    constructor(objType, userId) {
        this.objType = objType;
        this.userId = userId;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    connect() {
        const wsUrl = `ws://localhost:8000/chat/ws/${this.objType}/${this.userId}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.handleReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    handleMessage(message) {
        switch (message.type) {
            case 'topic_update':
                this.updateTopicData(message.data);
                break;
            case 'connection_status':
                console.log('Connection status:', message.message);
                break;
            case 'error':
                console.error('WebSocket error:', message.message);
                break;
        }
    }

    updateTopicData(data) {
        // Update your UI with the new topic data
        console.log('Updating topic data:', data);
    }

    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, 1000 * this.reconnectAttempts);
        }
    }

    ping() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ type: 'ping' }));
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// Usage
const chatWS = new ChatWebSocket('promo', 123);
chatWS.connect();

// Optional: Send periodic pings to keep connection alive
setInterval(() => chatWS.ping(), 30000);
```

## API Endpoints

### WebSocket Statistics
```
GET /chat/ws/stats
```
Returns connection statistics by object type.

### Connected Users
```
GET /chat/ws/connections/{obj_type}
```
Returns list of users connected to a specific object type.

### Test Broadcast
```
POST /chat/ws/test/{obj_type}
```
Send a test message to all connected users (for testing).

## Integration with Chat Operations

The WebSocket system automatically integrates with existing chat operations:

1. **Topic Creation**: When a topic is created, the creator receives a WebSocket notification
2. **Member Addition**: When users are added to a topic, they receive notifications
3. **Member Removal**: When users are removed, they receive notifications
4. **Topic Deletion**: When a topic is deleted, all members receive notifications

## Data Flow

1. User performs an action (create topic, add members, etc.)
2. Chat service processes the action
3. WebSocket service is called asynchronously
4. Notification service aggregates data using `GET_TOPICS_BY_OBJECTS` query
5. WebSocket manager broadcasts to connected users
6. Frontend receives updates and refreshes UI

## Error Handling

The system includes comprehensive error handling:

- **Connection failures**: Automatic cleanup and reconnection support
- **Broken connections**: Dead connections are detected and removed
- **Invalid messages**: Proper error responses for malformed data
- **Database errors**: Graceful handling of data retrieval failures

## Future Extensions

The architecture supports future messaging WebSocket implementations:
- Message broadcasting
- Typing indicators
- Read receipts
- Real-time notifications

## Monitoring

Use the stats endpoint to monitor WebSocket health:
- Total active connections
- Connections by object type
- Connected users per object type

This helps with debugging and capacity planning.
