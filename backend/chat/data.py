from typing import Any, List, Optional, Dict
from pricesmart_common.utils import async_execute_query, get_str_repr, get_array_format
from chat import queries as chat_queries
from chat import models as chat_models
from chat import constants as chat_constants
from configuration.environment import environment
from logger.logger import logger
from exceptions.exceptions import NotFoundException, InvalidAccessException
import aiopg
import asyncio
from contextlib import asynccontextmanager


async def async_execute_parameterized_query(query: str, params: tuple = (), transaction_mode: bool = False, timelimit: int = 300) -> List[dict]:
    """
    Execute a parameterized SQL query to prevent SQL injection.

    Args:
        query: SQL query with %s placeholders for parameters
        params: Tuple of parameters to bind to the query
        transaction_mode: Whether to run in transaction mode
        timelimit: Query timeout in seconds

    Returns:
        List of dictionaries containing query results
    """
    async with aiopg.connect(dsn=environment.conn_string, timeout=timelimit) as conn:
        async with conn.cursor() as cursor:
            try:
                if transaction_mode:
                    await cursor.execute("BEGIN")
                    await cursor.execute(query, params)
                    await cursor.execute("COMMIT")
                else:
                    await cursor.execute(query, params)

                # Fetch results if available
                if cursor.description:
                    columns = [col[0] for col in cursor.description]
                    rows = await cursor.fetchall()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    return []

            except Exception as e:
                if transaction_mode:
                    await cursor.execute("ROLLBACK")
                logger.error(f"Error executing parameterized query: {str(e)}")
                raise


async def get_topics_by_objects(
    object_type: str, object_ids: Optional[List[int]] = None, user_id: Optional[int] = None
) -> List[dict]:
    """Get all topics for objects of a specific type using parameterized queries"""
    try:
        if object_ids:
            # Query for specific object IDs using parameterized query
            query = chat_queries.GET_TOPICS_BY_OBJECTS_PARAM.format(
                environment.promo_schema,
                environment.promo_schema,
                environment.promo_schema
            )
            params = (object_ids, object_type, user_id if user_id else 0, object_type)
        else:
            # Query for all objects of the specified type using parameterized query
            query = chat_queries.GET_TOPICS_BY_OBJECT_TYPE_PARAM.format(
                environment.promo_schema,
                environment.promo_schema,
                environment.promo_schema
            )
            params = (object_type, user_id if user_id else 0, object_type)

        logger.info(f"Executing parameterized query with params: {params}")
        data = await async_execute_parameterized_query(query, params)
        return data
    except Exception as e:
        logger.error(f"Error in get_topics_by_objects: {str(e)}")
        # Fallback to legacy method for backward compatibility
        logger.warning("Falling back to legacy query method")
        return await get_topics_by_objects_legacy(object_type, object_ids, user_id)


async def get_topics_by_objects_legacy(
    object_type: str, object_ids: Optional[List[int]] = None, user_id: Optional[int] = None
) -> List[dict]:
    """Legacy method using string formatting (for backward compatibility)"""
    if object_ids:
        query = chat_queries.GET_TOPICS_BY_OBJECTS.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            object_type=get_str_repr(object_type),
            object_ids=get_array_format(object_ids),
            user_id=user_id if user_id else 0,
        )
    else:
        query = chat_queries.GET_TOPICS_BY_OBJECT_TYPE.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            object_type=get_str_repr(object_type),
            user_id=user_id if user_id else 0,
        )

    logger.info(f"Executing legacy query: {query}")
    data = await async_execute_query(query)
    return data


async def get_topic_by_id(topic_id: int) -> Optional[dict]:
    """Get a specific topic by ID with members (legacy function for verification)"""
    query = chat_queries.GET_TOPIC_BY_ID_WITH_MEMBERS.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    data = await async_execute_query(query)

    if not data:
        return None

    # Structure the response with topic info and members
    topic_info = {
        "topic_id": data[0]["topic_id"],
        "name": data[0]["name"],
        "description": data[0]["description"],
        "object_type": data[0]["object_type"],
        "object_ids": data[0]["object_ids"],
        "created_by": data[0]["created_by"],
        "created_at": data[0]["created_at"],
        "updated_by": data[0]["updated_by"],
        "updated_at": data[0]["updated_at"],
        "status": data[0]["status"],
        "created_by_user": data[0]["created_by_user"],
        "updated_by_user": data[0]["updated_by_user"],
        "members": [],
    }

    # Add members to the response
    for row in data:
        if row["user_id"] is not None:  # Only add if there's a member
            member = {
                "user_id": row["user_id"],
                "user_name": row["user_name"],
                "is_starred": row["is_starred"],
                "member_id": row["member_id"],
            }
            topic_info["members"].append(member)

    return topic_info


async def verify_topic_exists(topic_id: int) -> bool:
    """Simple verification that a topic exists using parameterized query"""
    try:
        query = chat_queries.GET_TOPIC_BY_ID_PARAM.format(
            environment.promo_schema, environment.global_schema
        )
        params = (topic_id,)
        data = await async_execute_parameterized_query(query, params)
        return len(data) > 0 if data else False
    except Exception as e:
        logger.error(f"Error in verify_topic_exists: {str(e)}")
        # Fallback to legacy method
        query = chat_queries.GET_TOPIC_BY_ID.format(
            promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
        )
        data = await async_execute_query(query)
        return len(data) > 0 if data else False


async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: int) -> int:
    """Create a new chat topic with optimized batch operations"""
    # Prepare all user IDs including the creator
    all_user_ids = list(set(topic_data.user_ids + [user_id]))

    # Use optimized single query to create topic, add members, and create unread entries
    query = chat_queries.CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD.format(
        promo_schema=environment.promo_schema,
        name=get_str_repr(topic_data.name),
        description=get_str_repr(topic_data.description) if topic_data.description else "NULL",
        object_type=get_str_repr(topic_data.object_type),
        object_ids=get_array_format(topic_data.object_ids),
        created_by=user_id,
        all_user_ids=get_array_format(all_user_ids),
    )

    logger.info(f"Executing optimized create_topic query for {len(all_user_ids)} members {query}")

    result = await async_execute_query(query)
    topic_id = result[0]["topic_id"]

    return topic_id


async def update_topic(topic_id: int, topic_data: chat_models.ChatTopicUpdate, user_id: int) -> bool:
    """Update a chat topic"""
    query = chat_queries.UPDATE_TOPIC.format(
        promo_schema=environment.promo_schema,
        name=get_str_repr(topic_data.name) if topic_data.name else "NULL",
        description=get_str_repr(topic_data.description) if topic_data.description else "NULL",
        status=get_str_repr(topic_data.status.value) if topic_data.status else "NULL",
        updated_by=user_id,
        topic_id=topic_id,
    )
    await async_execute_query(query)
    return True


async def delete_topic(topic_id: int, user_id: int) -> bool:
    """Archive a chat topic (soft delete)"""
    # Get topic info before deletion
    topic_exists = await verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Reset all unread counts to 0
    await reset_all_unread_counts_for_topic(topic_id)

    # Archive the topic
    query = chat_queries.DELETE_TOPIC.format(
        promo_schema=environment.promo_schema, updated_by=user_id, topic_id=topic_id
    )
    await async_execute_query(query)

    return True


async def add_topic_members(topic_id: int, user_ids: List[int]) -> bool:
    """Add members to a chat topic"""
    # Create unread entries for new members in batch
    await create_multiple_unread_entries(topic_id, user_ids)

    query = chat_queries.ADD_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )
    await async_execute_query(query)

    return True


async def remove_topic_members(topic_id: int, user_ids: List[int]) -> List[int]:
    """Remove multiple members from a chat topic using a single query"""
    query = chat_queries.REMOVE_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )

    result = await async_execute_query(query)
    removed_user_ids = [row["user_id"] for row in result] if result else []

    return removed_user_ids


async def get_topic_members(topic_id: int) -> List[dict]:
    """Get all members of a chat topic"""
    query = chat_queries.GET_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    return await async_execute_query(query)


async def check_user_is_member(topic_id: int, user_id: int) -> bool:
    """Check if user is a member of the topic"""
    query = chat_queries.CHECK_USER_IS_MEMBER.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    result = await async_execute_query(query)
    return result[0]["count"] > 0 if result else False


async def get_messages_by_topic(topic_id: int) -> List[dict]:
    """Get messages for a specific topic (removed LIMIT/OFFSET)"""
    query = chat_queries.GET_MESSAGES_BY_TOPIC.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    return await async_execute_query(query)


async def create_message(message_data: chat_models.ChatMessageCreate, user_id: int) -> int:
    """Create a new message using parameterized query"""
    try:
        query = chat_queries.CREATE_MESSAGE_PARAM.format(environment.promo_schema)
        params = (
            message_data.topic_id,
            user_id,
            message_data.content,
            message_data.tagged_users if message_data.tagged_users else None
        )

        result = await async_execute_parameterized_query(query, params)
        message_id = result[0]["message_id"]
        return message_id
    except Exception as e:
        logger.error(f"Error in create_message: {str(e)}")
        # Fallback to legacy method
        query = chat_queries.CREATE_MESSAGE.format(
            promo_schema=environment.promo_schema,
            topic_id=message_data.topic_id,
            sender_id=user_id,
            content=get_str_repr(message_data.content),
            tagged_users=get_array_format(message_data.tagged_users) if message_data.tagged_users else "NULL",
            priority=get_str_repr(message_data.priority.value),
            reply_to=message_data.reply_to if message_data.reply_to else "NULL",
        )
        result = await async_execute_query(query)
        message_id = result[0]["id"]
        return message_id


async def update_message(message_id: int, message_data: chat_models.ChatMessageUpdate, user_id: int) -> bool:
    """Update a message using parameterized query"""
    try:
        query = chat_queries.UPDATE_MESSAGE_PARAM.format(environment.promo_schema)
        params = (message_data.content, message_id)
        await async_execute_parameterized_query(query, params)
        return True
    except Exception as e:
        logger.error(f"Error in update_message: {str(e)}")
        # Fallback to legacy method
        query = chat_queries.UPDATE_MESSAGE.format(
            promo_schema=environment.promo_schema, content=get_str_repr(message_data.content), message_id=message_id
        )
        await async_execute_query(query)
        return True


async def delete_message(message_id: int, user_id: int) -> bool:
    """Delete a message using parameterized query"""
    try:
        query = chat_queries.DELETE_MESSAGE_PARAM.format(environment.promo_schema)
        params = (message_id,)
        await async_execute_parameterized_query(query, params)
        return True
    except Exception as e:
        logger.error(f"Error in delete_message: {str(e)}")
        # Fallback to legacy method
        query = chat_queries.DELETE_MESSAGE.format(promo_schema=environment.promo_schema, message_id=message_id)
        await async_execute_query(query)
        return True


async def get_message_by_id(message_id: int) -> Optional[dict]:
    """Get a specific message by ID using parameterized query"""
    try:
        query = chat_queries.GET_MESSAGE_BY_ID_PARAM.format(
            environment.promo_schema, environment.global_schema
        )
        params = (message_id,)
        data = await async_execute_parameterized_query(query, params)
        return data[0] if data else None
    except Exception as e:
        logger.error(f"Error in get_message_by_id: {str(e)}")
        # Fallback to legacy method
        query = chat_queries.GET_MESSAGE_BY_ID.format(
            promo_schema=environment.promo_schema, global_schema=environment.global_schema, message_id=message_id
        )
        data = await async_execute_query(query)
        return data[0] if data else None


async def get_unread_counts_by_user(user_id: int) -> List[dict]:
    """Get unread counts for all topics for a user"""
    query = chat_queries.GET_UNREAD_COUNTS_BY_USER.format(promo_schema=environment.promo_schema, user_id=user_id)
    return await async_execute_query(query)


async def get_unread_count_by_topic(topic_id: int) -> List[dict]:
    """Get unread counts for all users in a topic"""
    query = chat_queries.GET_UNREAD_COUNT_BY_TOPIC.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    return await async_execute_query(query)


async def get_total_unread_by_object(object_type: str, object_id: int, user_id: int) -> int:
    """Get total unread count for an object for a user"""
    query = chat_queries.GET_TOTAL_UNREAD_BY_OBJECT.format(
        promo_schema=environment.promo_schema,
        object_type=get_str_repr(object_type),
        object_id=object_id,
        user_id=user_id,
    )
    result = await async_execute_query(query)
    return result[0]["total_unread"] if result else 0


async def create_unread_entry(topic_id: int, user_id: int) -> bool:
    """Create unread entry for a user in a topic"""
    query = chat_queries.CREATE_UNREAD_ENTRY.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    await async_execute_query(query)
    return True


async def create_multiple_unread_entries(topic_id: int, user_ids: List[int]) -> bool:
    """Create unread entries for multiple users in a topic"""
    query = chat_queries.CREATE_MULTIPLE_UNREAD_ENTRIES.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )
    await async_execute_query(query)
    return True


async def update_unread_counts_for_message(topic_id: int, sender_id: int) -> bool:
    """Update unread counts for all members except sender"""
    query = chat_queries.UPDATE_UNREAD_COUNTS_FOR_MESSAGE.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, sender_id=sender_id
    )
    await async_execute_query(query)
    return True


async def update_unread_counts_for_offline_users(topic_id: int, user_ids: List[int]) -> bool:
    """Update unread counts for specific offline users"""
    if not user_ids:
        return True
        
    query = chat_queries.UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )
    await async_execute_query(query)
    return True


async def reset_unread_count(topic_id: int, user_id: int) -> bool:
    """Reset unread count for a user in a topic"""
    query = chat_queries.RESET_UNREAD_COUNT.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    await async_execute_query(query)
    return True


async def reset_all_unread_counts_for_topic(topic_id: int) -> bool:
    """Reset all unread counts to 0 for a topic"""
    query = chat_queries.RESET_ALL_UNREAD_COUNTS_FOR_TOPIC.format(
        promo_schema=environment.promo_schema, topic_id=topic_id
    )
    await async_execute_query(query)
    return True


async def search_messages(user_id: int, search_term: str, limit: int = 50, offset: int = 0) -> List[dict]:
    """Search messages for a user"""
    if not search_term.strip():
        return []

    query = chat_queries.SEARCH_MESSAGES.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        search_term=get_str_repr(f"%{search_term}%"),
        user_id=user_id,
        limit=limit,
        offset=offset,
    )
    return await async_execute_query(query)


async def get_topics_by_object_with_members(object_type: str, object_id: int, user_id: int) -> List[dict]:
    """Get topics for a specific object with members for a user"""
    query = chat_queries.GET_TOPICS_BY_OBJECT_WITH_MEMBERS.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        object_type=get_str_repr(object_type),
        object_id=object_id,
        user_id=user_id,
    )

    logger.info(f"Executing get_topics_by_object_with_members query: {query}")
    data = await async_execute_query(query)
    return data


async def get_topics_by_topic_id_with_members(topic_id: int, user_id: int = None) -> List[dict]:
    """Get topics by topic ID with members using the same query structure as object endpoints"""
    query = chat_queries.GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        topic_id=topic_id,
        user_id=user_id if user_id else 0,
    )

    logger.info(f"Executing get_topics_by_topic_id_with_members query: {query}")
    data = await async_execute_query(query)
    return data


async def get_topics_by_objects_multi_user(
    object_type: str, object_ids: List[int], user_ids: List[int]
) -> Dict[int, List[dict]]:
    """
    Get topic data for multiple users using a single optimized parameterized SQL query.

    Args:
        object_type: The object type (promo, event, store_group, product)
        object_ids: List of object IDs
        user_ids: List of user IDs to get data for

    Returns:
        Dict mapping user_id to their topic data
    """
    if not object_ids or not user_ids:
        return {}

    try:
        # Use parameterized query for better security and performance
        query = chat_queries.GET_TOPICS_BY_OBJECTS_MULTI_USER_PARAM.format(
            environment.promo_schema,
            environment.promo_schema,
            environment.promo_schema
        )
        params = (object_ids, user_ids, object_type, object_type)

        logger.info(f"Executing parameterized get_topics_by_objects_multi_user query for {len(user_ids)} users and {len(object_ids)} objects")
        data = await async_execute_parameterized_query(query, params)

    except Exception as e:
        logger.error(f"Error in parameterized multi-user query: {str(e)}")
        # Fallback to legacy method
        logger.warning("Falling back to legacy multi-user query")
        query = chat_queries.GET_TOPICS_BY_OBJECTS_MULTI_USER.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            object_type=get_str_repr(object_type),
            object_ids=get_array_format(object_ids),
            user_ids=get_array_format(user_ids),
        )
        data = await async_execute_query(query)
    
    # Group data by user_id
    user_data_map = {}
    for row in data:
        user_id = row["user_id"]
        if user_id not in user_data_map:
            user_data_map[user_id] = []
        
        user_data_map[user_id].append({
            "obj_id": row["obj_id"],
            "topics_count": row["topics_count"],
            "message_count": row["message_count"],
            "unread_count": row["unread_count"],
            "obj_type": row["obj_type"]
        })
    
    return user_data_map
