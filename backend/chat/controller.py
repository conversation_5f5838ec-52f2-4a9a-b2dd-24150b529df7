from typing import Optional
from fastapi import APIRouter
from app.dependencies import UserDependency
from chat import constants as chat_constants
from chat import models as chat_models
from chat import service as chat_service
from chat.websocket_controller import router as websocket_router
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as global_constants

router = APIRouter(tags=[chat_constants.CHAT_API_TAG])

# Include websocket routes
router.include_router(websocket_router, prefix="/chat")


# Topic Endpoints
@router.post("/topics")
async def get_topics_by_objects(filter_data: chat_models.ChatTopicFilter):
    """Get all topics for objects of a specific type"""
    data = await chat_service.get_topics_by_objects(
        filter_data.object_type, filter_data.object_ids, filter_data.user_id
    )
    return common_utils.create_response(data=data)


@router.get("/topic/{object_type}/{object_id}")
async def get_topics_by_object_with_members(object_type: str, object_id: int, user_id: UserDependency):
    """Get topics for a specific object with members for the authenticated user"""

    data = await chat_service.get_topics_by_object_with_members(object_type, object_id, user_id)
    return common_utils.create_response(data=data)


@router.post("/topic")
async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: UserDependency):
    """Create a new chat topic"""
    data = await chat_service.create_topic(topic_data, user_id)
    return common_utils.create_response(data=data, message=f"Chat topic '{topic_data.name}' created successfully.")


@router.get("/topic/{topic_id}")
async def get_topic(topic_id: int, user_id: UserDependency):
    """Get a specific topic by ID using the same response structure as object endpoints"""
    data = await chat_service.get_topic_by_id(topic_id, user_id)
    return common_utils.create_response(data=data)


@router.patch("/topic/{topic_id}")
async def update_topic(topic_id: int, topic_data: chat_models.ChatTopicUpdate, user_id: UserDependency):
    """Update a chat topic"""
    data = await chat_service.update_topic(topic_id, topic_data, user_id)
    return common_utils.create_response(data=data, message="Chat topic updated successfully.")


@router.delete("/topic/{topic_id}")
async def delete_topic(topic_id: int, user_id: UserDependency):
    """Archive a chat topic"""
    await chat_service.delete_topic(topic_id, user_id)
    return common_utils.create_response(message="Chat topic archived successfully.")


# Message Endpoints
@router.get("/messages/{topic_id}")
async def get_messages_by_topic(topic_id: int, user_id: UserDependency):
    """Get messages for a specific topic"""
    data = await chat_service.get_messages_by_topic(topic_id, user_id)
    return common_utils.create_response(data=data)


@router.post("/message")
async def create_message(message_data: chat_models.ChatMessageCreate, user_id: UserDependency):
    """Create a new message"""
    data = await chat_service.create_message(message_data, user_id)
    return common_utils.create_response(data=data, message="Message sent successfully.")


@router.patch("/message/{message_id}")
async def update_message(message_id: int, message_data: chat_models.ChatMessageUpdate, user_id: UserDependency):
    """Update a message"""
    data = await chat_service.update_message(message_id, message_data, user_id)
    return common_utils.create_response(data=data, message="Message updated successfully.")


@router.delete("/message/{message_id}")
async def delete_message(message_id: int, user_id: UserDependency):
    """Delete a message"""
    await chat_service.delete_message(message_id, user_id)
    return common_utils.create_response(message="Message deleted successfully.")


# Unread Endpoints
@router.post("/unread")
async def get_unread_counts_by_user(filter_data: chat_models.ChatUnreadFilter):
    """Get unread counts for all topics for a user"""
    data = await chat_service.get_unread_counts_by_user(filter_data.user_id)
    return common_utils.create_response(data=data)


@router.post("/unread/object-summary")
async def get_total_unread_by_object(filter_data: chat_models.ChatUnreadFilter):
    """Get total unread count for an object for a user"""
    data = await chat_service.get_total_unread_by_object(
        filter_data.object_type, filter_data.object_id, filter_data.user_id
    )
    return common_utils.create_response(data={"total_unread": data})


@router.post("/unread/topic-summary")
async def get_unread_count_by_topic(filter_data: chat_models.ChatUnreadFilter):
    """Get unread counts for all users in a topic"""
    data = await chat_service.get_unread_count_by_topic(filter_data.topic_id)
    return common_utils.create_response(data=data)


# Search Endpoints
@router.post("/search")
async def search_messages(filter_data: chat_models.ChatSearchFilter):
    """Search messages for a user"""
    data = await chat_service.search_messages(
        filter_data.user_id, filter_data.search_term, filter_data.limit, filter_data.offset
    )
    return common_utils.create_response(data=data)


# Member Endpoints
@router.get("/topic/{topic_id}/members")
async def get_topic_members(topic_id: int):
    """Get all members of a chat topic"""
    data = await chat_service.get_topic_members(topic_id)
    return common_utils.create_response(data=data)


@router.post("/topic/{topic_id}/members")
async def add_topic_members(topic_id: int, user_ids: list[int], user_id: UserDependency):
    """Add members to a chat topic"""
    await chat_service.add_topic_members(topic_id, user_ids)
    return common_utils.create_response(message="Members added to chat topic successfully.")


@router.delete("/topic/{topic_id}/members")
async def remove_topic_members(topic_id: int, request: chat_models.ChatRemoveMembersRequest, user_id: UserDependency):
    """Remove multiple members from a chat topic"""
    removed_user_ids = await chat_service.remove_topic_member(topic_id, request.user_ids)
    return common_utils.create_response(
        data={"removed_user_ids": removed_user_ids},
        message=f"Successfully removed {len(removed_user_ids)} member(s) from chat topic.",
    )
