# Parameterized queries to prevent SQL injection
GET_TOPICS_BY_OBJECTS_PARAM = """
    WITH object_ids_expanded AS (
        SELECT DISTINCT unnest(%s::int[]) AS object_id
    ),
    topic_object_map AS (
        SELECT
            oie.object_id,
            ct.topic_id
        FROM object_ids_expanded oie
        JOIN {}.tb_chat_topic ct
            ON ct.object_ids && ARRAY[oie.object_id]
        WHERE ct.object_type = %s
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        LEFT JOIN {}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = %s
        GROUP BY tom.object_id, cu.unread_count
    )
    SELECT
        object_id as obj_id,
        topics_count,
        message_count,
        unread_count,
        %s::text as obj_type
    FROM counts
    ORDER BY object_id
"""

GET_TOPICS_BY_OBJECT_TYPE_PARAM = """
    WITH topic_object_map AS (
        SELECT
            unnest(ct.object_ids) AS object_id,
            ct.topic_id
        FROM {}.tb_chat_topic ct
        WHERE ct.object_type = %s
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        LEFT JOIN {}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = %s
        GROUP BY tom.object_id, cu.unread_count
    )
    SELECT
        object_id as obj_id,
        topics_count,
        message_count,
        unread_count,
        %s::text as obj_type
    FROM counts
    ORDER BY object_id
"""

# Legacy queries (keeping for backward compatibility during transition)
GET_TOPICS_BY_OBJECTS = """
    WITH object_ids_expanded AS (
        SELECT DISTINCT unnest({object_ids}::int[]) AS object_id
    ),
    topic_object_map AS (
        SELECT
            oie.object_id,
            ct.topic_id
        FROM object_ids_expanded oie
        JOIN {promo_schema}.tb_chat_topic ct
            ON ct.object_ids && ARRAY[oie.object_id]
        WHERE ct.object_type = {object_type}
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        LEFT JOIN {promo_schema}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = {user_id}
        GROUP BY tom.object_id, cu.unread_count
    )
    SELECT
        object_id as obj_id,
        topics_count,
        message_count,
        unread_count,
        {object_type}::text as obj_type
    FROM counts
    ORDER BY object_id
"""

GET_TOPICS_BY_OBJECTS_MULTI_USER = """
    WITH object_ids_expanded AS (
        SELECT DISTINCT unnest({object_ids}::int[]) AS object_id
    ),
    user_ids_expanded AS (
        SELECT DISTINCT unnest({user_ids}::int[]) AS user_id
    ),
    topic_object_map AS (
        SELECT 
            oie.object_id,
            ct.topic_id
        FROM object_ids_expanded oie
        JOIN {promo_schema}.tb_chat_topic ct
            ON ct.object_ids && ARRAY[oie.object_id]
        WHERE ct.object_type = {object_type}
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            uie.user_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        CROSS JOIN user_ids_expanded uie
        LEFT JOIN {promo_schema}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = uie.user_id
        GROUP BY tom.object_id, uie.user_id, cu.unread_count
    )
    SELECT 
        object_id as obj_id,
        user_id,
        topics_count,
        message_count,
        unread_count,
        {object_type}::text as obj_type
    FROM counts
    ORDER BY object_id, user_id
"""

GET_TOPICS_BY_OBJECT_TYPE = """
    WITH topic_object_map AS (
        SELECT 
            unnest(ct.object_ids) AS object_id,
            ct.topic_id
        FROM {promo_schema}.tb_chat_topic ct
        WHERE ct.object_type = {object_type}
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        LEFT JOIN {promo_schema}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = {user_id}
        GROUP BY tom.object_id, cu.unread_count
    )
    SELECT 
        object_id as obj_id,
        topics_count,
        message_count,
        unread_count,
        {object_type}::text as obj_type
    FROM counts
    ORDER BY object_id
"""


# Parameterized versions of critical queries
GET_TOPIC_BY_ID_PARAM = """
    SELECT
        ct.topic_id,
        ct.name,
        ct.description,
        ct.object_type,
        ct.object_ids,
        ct.created_by,
        ct.created_at,
        ct.updated_by,
        ct.updated_at,
        ct.status,
        um.name as created_by_user,
        uum.name as updated_by_user
    FROM {}.tb_chat_topic ct
    LEFT JOIN {}.user_master um ON um.user_code = ct.created_by
    LEFT JOIN {}.user_master uum ON uum.user_code = ct.updated_by
    WHERE ct.topic_id = %s
"""

CREATE_TOPIC_PARAM = """
    INSERT INTO {}.tb_chat_topic (name, description, object_type, object_ids, created_by, updated_by)
    VALUES (%s, %s, %s, %s, %s, %s)
    RETURNING topic_id
"""

UPDATE_TOPIC_PARAM = """
    UPDATE {}.tb_chat_topic
    SET name = COALESCE(%s, name),
        description = COALESCE(%s, description),
        status = COALESCE(%s, status),
        updated_by = %s,
        updated_at = CURRENT_TIMESTAMP
    WHERE topic_id = %s
"""

DELETE_TOPIC_PARAM = """
    UPDATE {}.tb_chat_topic
    SET status = 'archived',
        updated_by = %s,
        updated_at = CURRENT_TIMESTAMP
    WHERE topic_id = %s
"""

CREATE_MESSAGE_PARAM = """
    INSERT INTO {}.tb_chat_message (topic_id, sender_id, content, tagged_users)
    VALUES (%s, %s, %s, %s)
    RETURNING message_id
"""

UPDATE_MESSAGE_PARAM = """
    UPDATE {}.tb_chat_message
    SET content = %s,
        updated_at = CURRENT_TIMESTAMP
    WHERE message_id = %s
"""

DELETE_MESSAGE_PARAM = """
    UPDATE {}.tb_chat_message
    SET deleted = true,
        updated_at = CURRENT_TIMESTAMP
    WHERE message_id = %s
"""

GET_MESSAGE_BY_ID_PARAM = """
    SELECT
        m.message_id,
        m.topic_id,
        m.sender_id,
        m.content,
        m.tagged_users,
        m.created_at,
        m.updated_at,
        m.deleted,
        um.name as sender_name
    FROM {}.tb_chat_message m
    LEFT JOIN {}.user_master um ON um.user_code = m.sender_id
    WHERE m.message_id = %s
"""

GET_TOPICS_BY_OBJECTS_MULTI_USER_PARAM = """
    WITH object_ids_expanded AS (
        SELECT DISTINCT unnest(%s::int[]) AS object_id
    ),
    user_ids_expanded AS (
        SELECT DISTINCT unnest(%s::int[]) AS user_id
    ),
    topic_object_map AS (
        SELECT
            oie.object_id,
            ct.topic_id
        FROM object_ids_expanded oie
        JOIN {}.tb_chat_topic ct
            ON ct.object_ids && ARRAY[oie.object_id]
        WHERE ct.object_type = %s
          AND ct.status != 'archived'
    ),
    counts AS (
        SELECT
            tom.object_id,
            uie.user_id,
            COUNT(DISTINCT tom.topic_id) AS topics_count,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM topic_object_map tom
        CROSS JOIN user_ids_expanded uie
        LEFT JOIN {}.tb_chat_message m
            ON m.topic_id = tom.topic_id
        LEFT JOIN {}.tb_chat_unread cu
            ON cu.topic_id = tom.topic_id AND cu.user_id = uie.user_id
        GROUP BY tom.object_id, uie.user_id, cu.unread_count
    )
    SELECT
        object_id as obj_id,
        user_id,
        topics_count,
        message_count,
        unread_count,
        %s::text as obj_type
    FROM counts
    ORDER BY object_id, user_id
"""

# Legacy queries (keeping for backward compatibility)
GET_TOPIC_BY_ID = """
    SELECT
        ct.topic_id,
        ct.name,
        ct.description,
        ct.object_type,
        ct.object_ids,
        ct.created_by,
        ct.created_at,
        ct.updated_by,
        ct.updated_at,
        ct.status,
        um.name as created_by_user,
        uum.name as updated_by_user
    FROM {promo_schema}.tb_chat_topic ct
    LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
    WHERE ct.topic_id = {topic_id}
"""

GET_TOPIC_BY_ID_WITH_MEMBERS = """
    SELECT 
        ct.topic_id,
        ct.name,
        ct.description,
        ct.object_type,
        ct.object_ids,
        ct.created_by,
        ct.created_at,
        ct.updated_by,
        ct.updated_at,
        ct.status,
        um.name as created_by_user,
        uum.name as updated_by_user,
        cm.user_id,
        cm.is_starred,
        cm.id as member_id,
        cm_user.name as user_name
    FROM {promo_schema}.tb_chat_topic ct
    LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
    LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
    LEFT JOIN {promo_schema}.tb_chat_member cm ON cm.topic_id = ct.topic_id
    LEFT JOIN {global_schema}.user_master cm_user ON cm_user.user_code = cm.user_id
    WHERE ct.topic_id = {topic_id}
    ORDER BY cm_user.name
"""

# Optimized batch queries for create_topic
CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD = """
    WITH topic_insert AS (
        INSERT INTO {promo_schema}.tb_chat_topic (
            name, description, object_type, object_ids, created_by, status
        ) VALUES (
            {name}, {description}, {object_type}, {object_ids}, {created_by}, 'open'
        ) RETURNING topic_id
    ),
    members_insert AS (
        INSERT INTO {promo_schema}.tb_chat_member (topic_id, user_id)
        SELECT ti.topic_id, unnest({all_user_ids}::int[])
        FROM topic_insert ti
        ON CONFLICT (topic_id, user_id) DO NOTHING
    ),
    unread_insert AS (
        INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
        SELECT ti.topic_id, unnest({all_user_ids}::int[]), 0
        FROM topic_insert ti
        ON CONFLICT (topic_id, user_id) DO NOTHING
    )
    SELECT topic_id FROM topic_insert
"""

UPDATE_TOPIC = """
    UPDATE {promo_schema}.tb_chat_topic 
    SET 
        name = COALESCE({name}, name),
        description = COALESCE({description}, description),
        status = COALESCE({status}, status),
        updated_by = {updated_by},
        updated_at = NOW()
    WHERE topic_id = {topic_id}
"""

DELETE_TOPIC = """
    UPDATE {promo_schema}.tb_chat_topic 
    SET status = 'archived', updated_at = NOW(), updated_by = {updated_by}
    WHERE topic_id = {topic_id}
"""

# Chat Member Queries
ADD_TOPIC_MEMBERS = """
    INSERT INTO {promo_schema}.tb_chat_member (topic_id, user_id)
    SELECT {topic_id}, unnest({user_ids}::int[])
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

CREATE_UNREAD_ENTRIES_FOR_MEMBERS = """
    INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
    SELECT {topic_id}, unnest({user_ids}::int[]), 0
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

REMOVE_TOPIC_MEMBERS = """
    WITH removed_members AS (
        DELETE FROM {promo_schema}.tb_chat_member 
        WHERE topic_id = {topic_id} AND user_id = ANY({user_ids}::int[])
        RETURNING user_id
    ),
    removed_unread AS (
        DELETE FROM {promo_schema}.tb_chat_unread 
        WHERE topic_id = {topic_id} AND user_id = ANY({user_ids}::int[])
    )
    SELECT user_id FROM removed_members
"""

GET_TOPIC_MEMBERS = """
    SELECT 
        cm.user_id,
        um.name as user_name,
        cm.is_starred,
        cm.id as member_id
    FROM {promo_schema}.tb_chat_member cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
    WHERE cm.topic_id = {topic_id}
    ORDER BY um.name
"""

CHECK_USER_IS_MEMBER = """
    SELECT COUNT(*) as count
    FROM {promo_schema}.tb_chat_member
    WHERE topic_id = {topic_id} AND user_id = {user_id}
"""

# Chat Message Queries
GET_MESSAGES_BY_TOPIC = """
    SELECT 
        cm.id,
        cm.topic_id,
        cm.sender_id,
        cm.content,
        cm.tagged_users,
        cm.priority,
        cm.created_at,
        cm.edited,
        cm.deleted,
        cm.reply_to,
        um.name as sender_name
    FROM {promo_schema}.tb_chat_message cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.sender_id
    WHERE cm.topic_id = {topic_id}
    ORDER BY cm.created_at ASC
"""

CREATE_MESSAGE = """
    INSERT INTO {promo_schema}.tb_chat_message (
        topic_id, sender_id, content, tagged_users, priority, reply_to
    ) VALUES (
        {topic_id}, {sender_id}, {content}, {tagged_users}, {priority}, {reply_to}
    ) RETURNING id
"""

UPDATE_MESSAGE = """
    UPDATE {promo_schema}.tb_chat_message 
    SET 
        content = {content},
        edited = true
    WHERE id = {message_id}
"""

DELETE_MESSAGE = """
    UPDATE {promo_schema}.tb_chat_message 
    SET deleted = true
    WHERE id = {message_id}
"""

GET_MESSAGE_BY_ID = """
    SELECT 
        cm.id,
        cm.topic_id,
        cm.sender_id,
        cm.content,
        cm.tagged_users,
        cm.priority,
        cm.created_at,
        cm.edited,
        cm.deleted,
        cm.reply_to,
        um.name as sender_name
    FROM {promo_schema}.tb_chat_message cm
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.sender_id
    WHERE cm.id = {message_id}
"""

# Chat Unread Queries
GET_UNREAD_COUNTS_BY_USER = """
    SELECT 
        cu.topic_id,
        cu.unread_count,
        cu.last_read_at,
        ct.name as topic_name,
        ct.object_type,
        ct.object_ids
    FROM {promo_schema}.tb_chat_unread cu
    LEFT JOIN {promo_schema}.tb_chat_topic ct ON ct.topic_id = cu.topic_id
    WHERE cu.user_id = {user_id}
    AND ct.status != 'archived'
    ORDER BY cu.unread_count DESC
"""

GET_UNREAD_COUNT_BY_TOPIC = """
    SELECT 
        cu.user_id,
        cu.unread_count,
        cu.last_read_at,
        um.name as user_name
    FROM {promo_schema}.tb_chat_unread cu
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cu.user_id
    WHERE cu.topic_id = {topic_id}
    ORDER BY cu.unread_count DESC
"""

GET_TOTAL_UNREAD_BY_OBJECT = """
    SELECT COALESCE(SUM(cu.unread_count), 0) as total_unread
    FROM {promo_schema}.tb_chat_unread cu
    LEFT JOIN {promo_schema}.tb_chat_topic ct ON ct.topic_id = cu.topic_id
    WHERE cu.user_id = {user_id}
    AND ct.object_type = {object_type}
    AND {object_id} = ANY(ct.object_ids)
    AND ct.status != 'archived'
"""

CREATE_UNREAD_ENTRY = """
    INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
    VALUES ({topic_id}, {user_id}, 0)
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

CREATE_MULTIPLE_UNREAD_ENTRIES = """
    INSERT INTO {promo_schema}.tb_chat_unread (topic_id, user_id, unread_count)
    SELECT {topic_id}, unnest({user_ids}::int[]), 0
    ON CONFLICT (topic_id, user_id) DO NOTHING
"""

UPDATE_UNREAD_COUNTS_FOR_MESSAGE = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = unread_count + 1
    WHERE topic_id = {topic_id} 
    AND user_id != {sender_id}
"""

UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = unread_count + 1
    WHERE topic_id = {topic_id} 
    AND user_id = ANY({user_ids}::int[])
"""

RESET_UNREAD_COUNT = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = 0, last_read_at = NOW()
    WHERE topic_id = {topic_id} AND user_id = {user_id}
"""

RESET_ALL_UNREAD_COUNTS_FOR_TOPIC = """
    UPDATE {promo_schema}.tb_chat_unread 
    SET unread_count = 0, last_read_at = NOW()
    WHERE topic_id = {topic_id}
"""

# Search Queries
SEARCH_MESSAGES = """
    SELECT 
        cm.id,
        cm.topic_id,
        cm.sender_id,
        cm.content,
        cm.created_at,
        cm.edited,
        cm.deleted,
        um.name as sender_name,
        ct.name as topic_name
    FROM {promo_schema}.tb_chat_message cm
    LEFT JOIN {promo_schema}.tb_chat_topic ct ON ct.topic_id = cm.topic_id
    LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.sender_id
    WHERE cm.content ILIKE {search_term}
    AND cm.deleted = false
    AND ct.status != 'archived'
    AND EXISTS (
        SELECT 1 FROM {promo_schema}.tb_chat_member cm2 
        WHERE cm2.topic_id = cm.topic_id AND cm2.user_id = {user_id}
    )
    ORDER BY cm.created_at DESC
    LIMIT {limit} OFFSET {offset}
"""

GET_TOPICS_BY_OBJECT_WITH_MEMBERS = """
    WITH topic_data AS (
        SELECT 
            ct.topic_id,
            ct.name,
            ct.description,
            ct.object_type,
            ct.object_ids,
            ct.created_by,
            ct.created_at,
            ct.updated_by,
            ct.updated_at,
            ct.status,
            um.name as created_by_user,
            uum.name as updated_by_user,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM {promo_schema}.tb_chat_topic ct
        LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
        LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
        LEFT JOIN {promo_schema}.tb_chat_message m ON m.topic_id = ct.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu ON cu.topic_id = ct.topic_id AND cu.user_id = {user_id}
        WHERE ct.object_type = {object_type}
          AND {object_id} = ANY(ct.object_ids)
          AND ct.status != 'archived'
          AND EXISTS (
              SELECT 1 FROM {promo_schema}.tb_chat_member cm 
              WHERE cm.topic_id = ct.topic_id AND cm.user_id = {user_id}
          )
        GROUP BY ct.topic_id, ct.name, ct.description, ct.object_type, ct.object_ids, 
                 ct.created_by, ct.created_at, ct.updated_by, ct.updated_at, ct.status,
                 um.name, uum.name, cu.unread_count
    ),
    member_data AS (
        SELECT 
            cm.topic_id,
            json_agg(
                json_build_object(
                    'user_id', cm.user_id,
                    'user_name', um.name,
                    'is_starred', cm.is_starred,
                    'member_id', cm.id, 
                    'email', um.email
                ) ORDER BY um.name
            ) as members
        FROM {promo_schema}.tb_chat_member cm
        LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
        WHERE cm.topic_id IN (SELECT topic_id FROM topic_data)
        GROUP BY cm.topic_id
    )
    SELECT 
        td.*,
        COALESCE(md.members, '[]'::json) as members
    FROM topic_data td
    LEFT JOIN member_data md ON md.topic_id = td.topic_id
    ORDER BY td.created_at DESC
"""

GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS = """
    WITH topic_data AS (
        SELECT 
            ct.topic_id,
            ct.name,
            ct.description,
            ct.object_type,
            ct.object_ids,
            ct.created_by,
            ct.created_at,
            ct.updated_by,
            ct.updated_at,
            ct.status,
            um.name as created_by_user,
            uum.name as updated_by_user,
            COUNT(*) FILTER (WHERE m.deleted = false) AS message_count,
            COALESCE(cu.unread_count, 0) AS unread_count
        FROM {promo_schema}.tb_chat_topic ct
        LEFT JOIN {global_schema}.user_master um ON um.user_code = ct.created_by
        LEFT JOIN {global_schema}.user_master uum ON uum.user_code = ct.updated_by
        LEFT JOIN {promo_schema}.tb_chat_message m ON m.topic_id = ct.topic_id
        LEFT JOIN {promo_schema}.tb_chat_unread cu ON cu.topic_id = ct.topic_id AND cu.user_id = {user_id}
        WHERE ct.topic_id = {topic_id}
          AND ct.status != 'archived'
        GROUP BY ct.topic_id, ct.name, ct.description, ct.object_type, ct.object_ids, 
                 ct.created_by, ct.created_at, ct.updated_by, ct.updated_at, ct.status,
                 um.name, uum.name, cu.unread_count
    ),
    member_data AS (
        SELECT 
            cm.topic_id,
            json_agg(
                json_build_object(
                    'user_id', cm.user_id,
                    'user_name', um.name,
                    'is_starred', cm.is_starred,
                    'member_id', cm.id, 
                    'email', um.email
                ) ORDER BY um.name
            ) as members
        FROM {promo_schema}.tb_chat_member cm
        LEFT JOIN {global_schema}.user_master um ON um.user_code = cm.user_id
        WHERE cm.topic_id IN (SELECT topic_id FROM topic_data)
        GROUP BY cm.topic_id
    )
    SELECT 
        td.*,
        COALESCE(md.members, '[]'::json) as members
    FROM topic_data td
    LEFT JOIN member_data md ON md.topic_id = td.topic_id
    ORDER BY td.created_at DESC
"""
