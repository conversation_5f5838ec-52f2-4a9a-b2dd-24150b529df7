from typing import List, Optional, Coroutine, Any
from chat import data as chat_data
from chat import models as chat_models
from chat import constants as chat_constants
from chat.websocket_notifications import notification_service
from chat.websocket_manager import topic_websocket_manager
from exceptions.exceptions import NotFoundException, InvalidAccessException
from pricesmart_common import utils as common_utils
from pricesmart_common.utils import async_execute_query
from configuration.environment import environment
from logger.logger import logger
import asyncio


async def _safe_create_task(coro: Coroutine[Any, Any, Any], task_name: str) -> None:
    """
    Safely create and execute an async task with proper error handling.

    Args:
        coro: The coroutine to execute
        task_name: Name of the task for logging purposes
    """
    try:
        task = asyncio.create_task(coro)
        # Add a callback to handle any exceptions
        task.add_done_callback(lambda t: _handle_task_exception(t, task_name))
    except Exception as e:
        logger.error(f"Error creating task '{task_name}': {str(e)}")


def _handle_task_exception(task: asyncio.Task, task_name: str) -> None:
    """
    Handle exceptions from background tasks.

    Args:
        task: The completed task
        task_name: Name of the task for logging purposes
    """
    try:
        if task.exception():
            logger.error(f"Background task '{task_name}' failed with exception: {task.exception()}")
        else:
            logger.debug(f"Background task '{task_name}' completed successfully")
    except asyncio.CancelledError:
        logger.debug(f"Background task '{task_name}' was cancelled")
    except Exception as e:
        logger.error(f"Error handling task exception for '{task_name}': {str(e)}")


async def get_topics_by_objects(
    object_type: str, object_ids: Optional[List[int]] = None, user_id: Optional[int] = None
) -> List[dict]:
    """Get all topics for objects of a specific type"""
    return await chat_data.get_topics_by_objects(object_type, object_ids, user_id)


async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: int) -> dict:
    """Create a new chat topic"""
    topic_id = await chat_data.create_topic(topic_data, user_id)
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    
    # Send websocket notification for topic creation (async, don't wait)
    await _safe_create_task(
        notification_service.handle_topic_created(
            topic_id=topic_id,
            object_type=topic_data.object_type,
            object_ids=topic_data.object_ids,
            creator_user_id=int(user_id)
        ),
        f"topic_created_notification_{topic_id}"
    )

    logger.info(f"Created topic {topic_id} for user {user_id}")
    
    return topics[0] if topics else {}


async def get_topic_by_id(topic_id: int, user_id: int) -> dict:
    """Get a specific topic by ID using the same query structure as object endpoints"""
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    if not topics:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)
    return topics[0]  # Return the first (and only) topic


async def update_topic(topic_id: int, topic_data: chat_models.ChatTopicUpdate, user_id: int) -> dict:
    """Update a chat topic"""
    # Verify topic exists
    # existing_topic = await chat_data.get_topic_by_id(topic_id)
    # if not existing_topic:
    #     raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    await chat_data.update_topic(topic_id, topic_data, user_id)
    topics = await chat_data.get_topics_by_topic_id_with_members(topic_id, user_id)
    return topics[0] if topics else {}


async def delete_topic(topic_id: int, user_id: int) -> bool:
    """Archive a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Send websocket notification for topic deletion BEFORE deletion (async, don't wait)
    # Pass the user_id who is deleting the topic
    await _safe_create_task(
        notification_service.handle_topic_deleted_with_user(topic_id, user_id),
        f"topic_deleted_notification_{topic_id}"
    )

    return await chat_data.delete_topic(topic_id, user_id)


async def get_messages_by_topic(topic_id: int, user_id: int = None) -> List[dict]:
    """Get messages for a specific topic (removed LIMIT/OFFSET)"""
    # Verify topic exists
    # topic = await chat_data.get_topic_by_id(topic_id)
    # if not topic:
    #     raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # If user_id provided, verify membership and reset unread count
    if user_id:
        # is_member = await chat_data.check_user_is_member(topic_id, user_id)
        # if not is_member:
        #     raise InvalidAccessException(chat_constants.USER_NOT_MEMBER)

        # Reset unread count when user opens topic
        await chat_data.reset_unread_count(topic_id, user_id)

    return await chat_data.get_messages_by_topic(topic_id)


async def create_message(message_data: chat_models.ChatMessageCreate, user_id: int) -> dict:
    """Create a new message and broadcast to topic members"""
    message_id = await chat_data.create_message(message_data, user_id)
    message = await chat_data.get_message_by_id(message_id)


    
    # Get topic members for broadcasting
    topic_members = await chat_data.get_topic_members(message_data.topic_id)
    member_user_ids = [member["user_id"] for member in topic_members]
    
    # Broadcast message to all connected topic members (async, don't wait)
    await _safe_create_task(
        _broadcast_message_to_topic(
            topic_id=message_data.topic_id,
            message_data=message,
            sender_id=user_id,
            topic_members=member_user_ids,
            action=chat_constants.MESSAGE_CREATED
        ),
        f"message_broadcast_{message_data.topic_id}_{message['message_id']}"
    )
    
    return message


async def update_message(message_id: int, message_data: chat_models.ChatMessageUpdate, user_id: int) -> dict:
    """Update a message and broadcast to topic members"""
    # Verify message exists and belongs to user
    existing_message = await chat_data.get_message_by_id(message_id)
    if not existing_message:
        raise NotFoundException(chat_constants.MESSAGE_NOT_FOUND)

    if str(existing_message["sender_id"]) != str(user_id):
        raise InvalidAccessException("You can only edit your own messages")

    await chat_data.update_message(message_id, message_data, user_id)
    updated_message = await chat_data.get_message_by_id(message_id)
    
    # Get topic members for broadcasting
    topic_members = await chat_data.get_topic_members(updated_message["topic_id"])
    member_user_ids = [member["user_id"] for member in topic_members]
    
    # Broadcast message update to all connected topic members (async, don't wait)
    await _safe_create_task(
        _broadcast_message_to_topic(
            topic_id=updated_message["topic_id"],
            message_data=updated_message,
            sender_id=user_id,
            topic_members=member_user_ids,
            action=chat_constants.MESSAGE_UPDATED
        ),
        f"message_update_broadcast_{updated_message['topic_id']}_{message_id}"
    )

    return updated_message


async def delete_message(message_id: int, user_id: int) -> bool:
    """Delete a message and broadcast to topic members"""
    # Verify message exists and belongs to user
    existing_message = await chat_data.get_message_by_id(message_id)
    if not existing_message:
        raise NotFoundException(chat_constants.MESSAGE_NOT_FOUND)

    if str(existing_message["sender_id"]) != str(user_id):
        raise InvalidAccessException("You can only delete your own messages")

    # Store message info before deletion for broadcasting
    topic_id = existing_message["topic_id"]
    message_info = {
        "id": message_id,
        "topic_id": topic_id,
        "deleted": True,
        "deleted_by": user_id
    }

    success = await chat_data.delete_message(message_id, user_id)
    if not success:
        raise Exception("Message cannot be deleted")
    
    # Get topic members for broadcasting
    topic_members = await chat_data.get_topic_members(topic_id)
    member_user_ids = [member["user_id"] for member in topic_members]
    
    # Broadcast message deletion to all connected topic members (async, don't wait)
    await _safe_create_task(
        _broadcast_message_to_topic(
            topic_id=topic_id,
            message_data=message_info,
            sender_id=user_id,
            topic_members=member_user_ids,
            action=chat_constants.MESSAGE_DELETED
        ),
        f"message_delete_broadcast_{topic_id}_{message_id}"
    )

    return success


async def _broadcast_message_to_topic(topic_id: int, message_data: dict, sender_id: int, topic_members: List[int], action: str):
    """
    Broadcast a message to all topic members and handle unread counts for offline users.
    
    Args:
        topic_id: The topic ID
        message_data: The message data to broadcast
        sender_id: The user ID who sent the message
        topic_members: List of all member user IDs in the topic
        action: The action type (MESSAGE_CREATED, MESSAGE_UPDATED, MESSAGE_DELETED)
    """
    try:
        # For message creation, broadcast to connected users and update unread counts for offline users
        if action == chat_constants.MESSAGE_CREATED:
            # Broadcast to connected users and get list of offline users
            offline_users = await topic_websocket_manager.broadcast_message_to_topic(
                topic_id=topic_id,
                message_data=message_data,
                sender_id=sender_id,
                topic_members=topic_members
            )
            
            # Update unread counts for offline users
            if offline_users:
                await chat_data.update_unread_counts_for_offline_users(topic_id, offline_users)
                logger.info(f"Message created in topic {topic_id}. Updated unread count for offline users: {offline_users}")
        
        # For message updates and deletions, just broadcast to connected users (no unread count changes)
        else:
            await topic_websocket_manager.broadcast_message_update_to_topic(
                topic_id=topic_id,
                message_data=message_data,
                sender_id=sender_id,
                topic_members=topic_members,
                action=action
            )
            logger.info(f"Message {action} in topic {topic_id}. Broadcasted to connected users.")
            
    except Exception as e:
        logger.error(f"Error broadcasting message {action} to topic {topic_id}: {str(e)}")


async def get_unread_counts_by_user(user_id: int) -> List[dict]:
    """Get unread counts for all topics for a user"""
    return await chat_data.get_unread_counts_by_user(user_id)


async def get_unread_count_by_topic(topic_id: int) -> List[dict]:
    """Get unread counts for all users in a topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    return await chat_data.get_unread_count_by_topic(topic_id)


async def get_total_unread_by_object(object_type: str, object_id: int, user_id: int) -> int:
    """Get total unread count for an object for a user"""
    return await chat_data.get_total_unread_by_object(object_type, object_id, user_id)


async def search_messages(user_id: int, search_term: str, limit: int = 50, offset: int = 0) -> List[dict]:
    """Search messages for a user"""
    if not search_term.strip():
        return []

    return await chat_data.search_messages(user_id, search_term, limit, offset)


async def get_topic_members(topic_id: int) -> List[dict]:
    """Get all members of a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    return await chat_data.get_topic_members(topic_id)


async def add_topic_members(topic_id: int, user_ids: List[int]) -> bool:
    """Add members to a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    result = await chat_data.add_topic_members(topic_id, user_ids)
    
    # Send websocket notification for members added (async, don't wait)
    await _safe_create_task(
        notification_service.handle_members_added(
            topic_id=topic_id,
            added_user_ids=user_ids
        ),
        f"members_added_notification_{topic_id}"
    )
    
    return result


async def remove_topic_member(topic_id: int, user_ids: List[int]) -> List[int]:
    """Remove multiple members from a chat topic"""
    # Verify topic exists
    topic_exists = await chat_data.verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Check if all users are members
    # for user_id in user_ids:
    #     is_member = await chat_data.check_user_is_member(topic_id, user_id)
    #     if not is_member:
    #         raise InvalidAccessException(f"User {user_id} is not a member of this topic")

    # Remove members using single query
    removed_user_ids = await chat_data.remove_topic_members(topic_id, user_ids)

    # Send websocket notification for members removed (async, don't wait)
    if removed_user_ids:
        await _safe_create_task(
            notification_service.handle_members_removed(
                topic_id=topic_id,
                removed_user_ids=removed_user_ids
            ),
            f"members_removed_notification_{topic_id}"
        )

    return removed_user_ids


async def get_topics_by_object_with_members(object_type: str, object_id: int, user_id: int) -> List[dict]:
    """Get topics for a specific object with members for the authenticated user"""
    return await chat_data.get_topics_by_object_with_members(object_type, object_id, user_id)
