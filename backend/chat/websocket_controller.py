from fastapi import API<PERSON>out<PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Path
from fastapi.responses import JSONResponse
from typing import Dict, Any
from chat.websocket_manager import websocket_manager, topic_websocket_manager
from chat.websocket_models import WebSocketConnectionParams, WebSocketStats
from chat import constants as chat_constants
from logger.logger import logger
from datetime import datetime
import json


router = APIRouter(tags=[chat_constants.CHAT_API_TAG])


@router.websocket("/ws/{obj_type}/{user_id}")
async def websocket_endpoint(websocket: WebSocket, obj_type: str, user_id: int):
    """
    WebSocket endpoint for topic updates.
    
    URL pattern: /ws/{obj_type}/{user_id}
    - obj_type: Object type (promo, event, store_group, product)
    - user_id: User ID subscribing to updates
    
    The websocket will receive JSON messages with the following structure:
    {
        "type": "topic_update",
        "obj_type": "promo",
        "data": [...],  // Array of topic data from GET_TOPICS_BY_OBJECTS query
        "timestamp": "2024-01-01T12:00:00"
    }
    """
    
    # Validate object type
    valid_types = ["promo", "event", "store_group", "product"]
    if obj_type not in valid_types:
        await websocket.close(code=1003, reason=f"Invalid object type. Must be one of: {', '.join(valid_types)}")
        return
    
    # Validate user_id
    if user_id <= 0:
        await websocket.close(code=1003, reason="Invalid user_id")
        return
    
    logger.info(f"WebSocket connection attempt: obj_type={obj_type}, user_id={user_id}")
    
    # Subscribe user to websocket notifications using unified interface
    success = await websocket_manager.subscribe(obj_type, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to {obj_type}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Successfully connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_client_message(websocket, obj_type, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: obj_type={obj_type}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        await websocket_manager.disconnect(websocket)
        logger.info(f"WebSocket cleanup completed: obj_type={obj_type}, user_id={user_id}")


@router.websocket("/ws/topic/{topic_id}/{user_id}")
async def topic_websocket_endpoint(websocket: WebSocket, topic_id: int, user_id: int):
    """
    WebSocket endpoint for individual topic message updates.
    
    URL pattern: /ws/topic/{topic_id}/{user_id}
    - topic_id: The specific topic ID to subscribe to
    - user_id: User ID subscribing to topic updates
    
    The websocket will receive JSON messages with the following structure:
    {
        "type": "new_message",
        "topic_id": 123,
        "message": {...},  // Message data
        "sender_id": 456,
        "timestamp": "2024-01-01T12:00:00"
    }
    """
    
    # Validate topic_id
    if topic_id <= 0:
        await websocket.close(code=1003, reason="Invalid topic_id")
        return
    
    # Validate user_id
    if user_id <= 0:
        await websocket.close(code=1003, reason="Invalid user_id")
        return
    
    logger.info(f"Topic WebSocket connection attempt: topic_id={topic_id}, user_id={user_id}")
    
    # Subscribe user to topic websocket notifications using unified interface
    success = await topic_websocket_manager.subscribe(topic_id, user_id, websocket)
    
    if not success:
        logger.error(f"Failed to subscribe user {user_id} to topic {topic_id}")
        return
    
    try:
        # Send initial connection confirmation
        welcome_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Successfully connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (optional - can be used for ping/pong)
                data = await websocket.receive_text()
                
                # Parse client message
                try:
                    client_message = json.loads(data)
                    await _handle_topic_client_message(websocket, topic_id, user_id, client_message)
                except json.JSONDecodeError:
                    error_message = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await websocket.send_text(json.dumps(error_message))
                    
            except WebSocketDisconnect:
                logger.info(f"Topic WebSocket disconnected: topic_id={topic_id}, user_id={user_id}")
                break
            except Exception as e:
                logger.error(f"Error in topic websocket loop for user {user_id}: {str(e)}")
                break
                
    except Exception as e:
        logger.error(f"Error in topic websocket endpoint for user {user_id}: {str(e)}")
    finally:
        # Clean up connection
        await topic_websocket_manager.disconnect(websocket)
        logger.info(f"Topic WebSocket cleanup completed: topic_id={topic_id}, user_id={user_id}")


async def _handle_client_message(websocket: WebSocket, obj_type: str, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "obj_type": obj_type,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "obj_type": obj_type,
            "message": f"Connected to {obj_type} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(websocket_manager.get_connected_users(obj_type))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


async def _handle_topic_client_message(websocket: WebSocket, topic_id: int, user_id: int, message: Dict[str, Any]):
    """
    Handle messages from the topic client.
    
    Supported message types:
    - ping: Respond with pong for connection health check
    - status: Send current connection status
    """
    
    message_type = message.get("type", "").lower()
    
    if message_type == "ping":
        pong_message = {
            "type": "pong",
            "topic_id": topic_id,
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id
        }
        await websocket.send_text(json.dumps(pong_message))
        
    elif message_type == "status":
        status_message = {
            "type": "connection_status",
            "topic_id": topic_id,
            "message": f"Connected to topic {topic_id} updates",
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "connected_users": len(topic_websocket_manager.get_connected_users(topic_id))
        }
        await websocket.send_text(json.dumps(status_message))
        
    else:
        error_message = {
            "type": "error",
            "message": f"Unknown message type: {message_type}",
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))


@router.get("/ws/stats", response_model=WebSocketStats)
async def get_websocket_stats():
    """
    Get current WebSocket connection statistics.
    
    Returns connection counts by object type and total active connections.
    Useful for monitoring and debugging.
    """
    try:
        stats_data = websocket_manager.get_connection_stats()
        
        return WebSocketStats(
            total_active=stats_data.get("total_active", 0),
            by_object_type={k: v for k, v in stats_data.items() if k != "total_active"},
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error getting websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving websocket statistics")


@router.get("/ws/connections/{obj_type}")
async def get_connected_users(obj_type: str = Path(..., description="Object type")):
    """
    Get list of users connected to a specific object type.
    
    Args:
        obj_type: Object type (promo, event, store_group, product)
        
    Returns:
        List of connected user IDs
    """
    # Validate object type
    valid_types = ["promo", "event", "store_group", "product"]
    if obj_type not in valid_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid object type. Must be one of: {', '.join(valid_types)}"
        )
    
    try:
        connected_users = websocket_manager.get_connected_users(obj_type)
        
        return {
            "obj_type": obj_type,
            "connected_users": connected_users,
            "count": len(connected_users),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting connected users for {obj_type}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving connected users")


@router.get("/ws/topic/connections/{topic_id}")
async def get_topic_connected_users(topic_id: int = Path(..., description="Topic ID")):
    """
    Get list of users connected to a specific topic.
    
    Args:
        topic_id: Topic ID
        
    Returns:
        List of connected user IDs
    """
    # Validate topic_id
    if topic_id <= 0:
        raise HTTPException(status_code=400, detail="Invalid topic_id")
    
    try:
        connected_users = topic_websocket_manager.get_connected_users(topic_id)
        
        return {
            "topic_id": topic_id,
            "connected_users": connected_users,
            "count": len(connected_users),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting connected users for topic {topic_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic connected users")


@router.get("/ws/topic/stats")
async def get_topic_websocket_stats():
    """
    Get current topic WebSocket connection statistics.
    
    Returns connection counts by topic ID and total active connections.
    Useful for monitoring and debugging.
    """
    try:
        stats_data = topic_websocket_manager.get_connection_stats()
        
        return {
            "total_active": stats_data.get("total_active", 0),
            "by_topic": {k: v for k, v in stats_data.items() if k != "total_active"},
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting topic websocket stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving topic websocket statistics")


@router.post("/ws/test/{obj_type}")
async def test_broadcast(obj_type: str, test_data: Dict[str, Any]):
    """
    Test endpoint to manually trigger a broadcast to all connected users.
    
    Args:
        obj_type: Object type to broadcast to
        test_data: Test data to broadcast
        
    This endpoint is useful for testing websocket functionality.
    """
    # Validate object type
    valid_types = ["promo", "event", "store_group", "product"]
    if obj_type not in valid_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid object type. Must be one of: {', '.join(valid_types)}"
        )
    
    try:
        connected_users = websocket_manager.get_connected_users(obj_type)
        
        if not connected_users:
            return {
                "message": f"No users connected to {obj_type}",
                "obj_type": obj_type,
                "connected_count": 0
            }
        
        # Create test message for all connected users
        user_data_map = {}
        for user_id in connected_users:
            user_data_map[user_id] = {
                "test": True,
                "message": "Test broadcast message",
                "user_id": user_id,
                **test_data
            }
        
        # Broadcast test message
        await websocket_manager.broadcast_to_users(obj_type, user_data_map, "test", 0, [], 0)
        
        return {
            "message": f"Test broadcast sent to {len(connected_users)} users",
            "obj_type": obj_type,
            "connected_users": connected_users,
            "data_sent": test_data
        }
        
    except Exception as e:
        logger.error(f"Error in test broadcast for {obj_type}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error sending test broadcast")
