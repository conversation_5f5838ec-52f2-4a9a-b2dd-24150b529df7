-- Chat System Database Schema
-- This file contains all the SQL definitions for the chat system

-- =====================================================
-- ENUM TYPES
-- =====================================================

-- Chat Priority Enum
CREATE TYPE price_promo.chat_priority_enum AS ENUM (
    'low',
    'medium',
    'high'
);

-- Chat Status Enum
CREATE TYPE price_promo.chat_status_enum AS ENUM (
    'open',
    'resolved',
    'closed',
    'archived'
);

-- =====================================================
-- TABLES
-- =====================================================

-- Chat Topic Table
-- Stores information about chat topics/conversations
CREATE TABLE price_promo.tb_chat_topic (
    topic_id serial4 NOT NULL,
    name varchar(255) NOT NULL,
    description text NULL,
    object_type varchar(100) NOT NULL,
    object_ids int4[] NOT NULL,
    created_by int4 NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_by int4 NULL,
    updated_at timestamp NULL,
    status price_promo.chat_status_enum DEFAULT 'open' NOT NULL,
    CONSTRAINT tb_chat_topic_pk PRIMARY KEY (topic_id),
    CONSTRAINT tb_chat_topic_created_by_fk FOREIGN KEY (created_by) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_chat_topic_updated_by_fk FOREIGN KEY (updated_by) REFERENCES "global".user_master(user_code)
);

-- Chat Message Table
-- Stores individual messages within chat topics
CREATE TABLE price_promo.tb_chat_message (
    id serial4 NOT NULL,
    topic_id int4 NOT NULL,
    sender_id int4 NOT NULL,
    content text NOT NULL,
    tagged_users int4[] NULL,
    priority price_promo.chat_priority_enum DEFAULT 'low' NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    edited bool DEFAULT false NOT NULL,
    deleted bool DEFAULT false NOT NULL,
    reply_to int4 NULL,
    CONSTRAINT tb_chat_message_pk PRIMARY KEY (id),
    CONSTRAINT tb_chat_message_topic_fk FOREIGN KEY (topic_id) REFERENCES price_promo.tb_chat_topic(topic_id) ON DELETE CASCADE,
    CONSTRAINT tb_chat_message_sender_fk FOREIGN KEY (sender_id) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_chat_message_reply_fk FOREIGN KEY (reply_to) REFERENCES price_promo.tb_chat_message(id)
);

-- Chat Member Table
-- Stores members of chat topics and their preferences
CREATE TABLE price_promo.tb_chat_member (
    id serial4 NOT NULL,
    topic_id int4 NOT NULL,
    user_id int4 NOT NULL,
    is_starred bool DEFAULT false NOT NULL,
    CONSTRAINT tb_chat_member_pk PRIMARY KEY (id),
    CONSTRAINT tb_chat_member_topic_fk FOREIGN KEY (topic_id) REFERENCES price_promo.tb_chat_topic(topic_id) ON DELETE CASCADE,
    CONSTRAINT tb_chat_member_user_fk FOREIGN KEY (user_id) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_chat_member_unique UNIQUE (topic_id, user_id)
);

-- Chat Unread Table
-- Stores unread message counts for users in topics
CREATE TABLE price_promo.tb_chat_unread (
    id serial4 NOT NULL,
    topic_id int4 NOT NULL,
    user_id int4 NOT NULL,
    unread_count int4 DEFAULT 0 NOT NULL,
    last_read_at timestamp NULL,
    CONSTRAINT tb_chat_unread_pk PRIMARY KEY (id),
    CONSTRAINT tb_chat_unread_topic_fk FOREIGN KEY (topic_id) REFERENCES price_promo.tb_chat_topic(topic_id) ON DELETE CASCADE,
    CONSTRAINT tb_chat_unread_user_fk FOREIGN KEY (user_id) REFERENCES "global".user_master(user_code),
    CONSTRAINT tb_chat_unread_unique UNIQUE (topic_id, user_id)
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Chat Topic Indexes
CREATE INDEX idx_tb_chat_topic_object_type ON price_promo.tb_chat_topic USING btree (object_type);
CREATE INDEX idx_tb_chat_topic_status ON price_promo.tb_chat_topic USING btree (status);
CREATE INDEX idx_tb_chat_topic_created_at ON price_promo.tb_chat_topic USING btree (created_at);
CREATE INDEX idx_tb_chat_topic_object_ids ON price_promo.tb_chat_topic USING gin (object_ids);
CREATE INDEX idx_tb_chat_topic_created_by ON price_promo.tb_chat_topic USING btree (created_by);

-- Chat Message Indexes
CREATE INDEX idx_tb_chat_message_topic_id ON price_promo.tb_chat_message USING btree (topic_id);
CREATE INDEX idx_tb_chat_message_created_at ON price_promo.tb_chat_message USING btree (created_at);
CREATE INDEX idx_tb_chat_message_reply_to ON price_promo.tb_chat_message USING btree (reply_to);
CREATE INDEX idx_tb_chat_message_sender_id ON price_promo.tb_chat_message USING btree (sender_id);
CREATE INDEX idx_tb_chat_message_priority ON price_promo.tb_chat_message USING btree (priority);
CREATE INDEX idx_tb_chat_message_deleted ON price_promo.tb_chat_message USING btree (deleted);

-- Chat Member Indexes
CREATE INDEX idx_tb_chat_member_topic_id ON price_promo.tb_chat_member USING btree (topic_id);
CREATE INDEX idx_tb_chat_member_user_id ON price_promo.tb_chat_member USING btree (user_id);
CREATE INDEX idx_tb_chat_member_starred ON price_promo.tb_chat_member USING btree (is_starred);

-- Chat Unread Indexes
CREATE INDEX idx_tb_chat_unread_topic_id ON price_promo.tb_chat_unread USING btree (topic_id);
CREATE INDEX idx_tb_chat_unread_user_id ON price_promo.tb_chat_unread USING btree (user_id);
CREATE INDEX idx_tb_chat_unread_count ON price_promo.tb_chat_unread USING btree (unread_count);

