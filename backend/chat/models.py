from typing import List, Optional
from pydantic import BaseModel, Field, field_validator
from enum import Enum
from exceptions.exceptions import CommonException


class ChatPriorityEnum(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ChatStatusEnum(str, Enum):
    OPEN = "open"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ARCHIVED = "archived"


class ChatTopicCreate(BaseModel):
    name: str
    description: Optional[str] = None
    object_type: str
    object_ids: List[int] = Field(default_factory=list)
    user_ids: List[int] = Field(default_factory=list)

    @field_validator("name")
    @classmethod
    def validate_name(cls, value):
        if not value.strip():
            raise CommonException("Chat topic name cannot be empty")
        return value.strip()

    @field_validator("object_type")
    @classmethod
    def validate_object_type(cls, value):
        valid_types = ["promo", "event", "store_group", "product"]
        if value not in valid_types:
            raise CommonException(f"Invalid object type. Must be one of: {', '.join(valid_types)}")
        return value

    @field_validator("object_ids")
    @classmethod
    def validate_object_ids(cls, value):
        if not value:
            raise CommonException("At least one object_id is required")
        return value


class ChatTopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ChatStatusEnum] = None


class ChatMessageCreate(BaseModel):
    topic_id: int
    content: str
    tagged_users: Optional[List[int]] = Field(default_factory=list)
    priority: ChatPriorityEnum = ChatPriorityEnum.LOW
    reply_to: Optional[int] = None

    @field_validator("content")
    @classmethod
    def validate_content(cls, value):
        if not value.strip():
            raise CommonException("Message content cannot be empty")
        return value.strip()


class ChatMessageUpdate(BaseModel):
    content: str

    @field_validator("content")
    @classmethod
    def validate_content(cls, value):
        if not value.strip():
            raise CommonException("Message content cannot be empty")
        return value.strip()


class ChatRemoveMembersRequest(BaseModel):
    user_ids: List[int] = Field(..., min_items=1)

    @field_validator("user_ids")
    @classmethod
    def validate_user_ids(cls, value):
        if not value:
            raise CommonException("At least one user_id is required")
        return value


class ChatTopicFilter(BaseModel):
    object_type: str
    object_ids: Optional[List[int]] = None
    user_id: Optional[int] = None


class ChatUnreadFilter(BaseModel):
    user_id: int
    topic_id: Optional[int] = None
    object_type: Optional[str] = None
    object_id: Optional[int] = None


class ChatMessageFilter(BaseModel):
    topic_id: int
    user_id: Optional[int] = None


class ChatSearchFilter(BaseModel):
    search_term: str
    limit: int = 50
    offset: int = 0
    user_id: int


class ChatObjectParams(BaseModel):
    object_type: str = Field(..., description="Type of object (promo, event, store_group, product)")
    object_id: int = Field(..., description="ID of the object")

    @field_validator("object_type")
    @classmethod
    def validate_object_type(cls, value):
        valid_types = ["promo", "event", "store_group", "product"]
        if value not in valid_types:
            raise CommonException(f"Invalid object type. Must be one of: {', '.join(valid_types)}")
        return value
