[local]
deployment_env=dev
meta_schema=metaschema
secret_project = balsambrands-20022025
secret_version=latest
secret_id=balsamhill_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = balsambrands
project_id = balsambrands-20022025
project_resource = balsambrands
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-west1-balsambrands-20022025.cloudfunctions.net/pricesmart-generic-report-generator-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-west1-balsambrands-20022025.cloudfunctions.net/downstream-balsam-cf-dev
DOWNSTREAM_BUCKET_NAME = mulesoft-events-dev
SAKS_URL_PREFIX = https://balsamhill.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-balsam-dev
ENVIRONMENT=dev
LOG_TITLE = pricesmart-balsam-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://balsamhill.devs.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate

[pricesmart-dev]
deployment_env=dev
meta_schema=metaschema
secret_project = balsambrands-20022025
secret_version=latest
secret_id=balsamhill_dev
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = balsambrands
project_id = balsambrands-20022025
project_resource = balsambrands
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-west1-balsambrands-20022025.cloudfunctions.net/pricesmart-generic-report-generator-dev
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-west1-balsambrands-20022025.cloudfunctions.net/downstream-balsam-cf-dev
DOWNSTREAM_BUCKET_NAME = mulesoft-events-dev
SAKS_URL_PREFIX = https://balsamhill.devs.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-balsam-dev
ENVIRONMENT=dev
LOG_TITLE = pricesmart-balsam-promo-dev
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://balsamhill.devs.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://balsamhill.devs.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate





[pricesmart-test]
deployment_env=test
meta_schema=metaschema
secret_project = balsambrands-20022025
secret_version=latest
secret_id=balsamhill_test
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = balsambrands
project_id = balsambrands-20022025
project_resource = balsambrands
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://balsamhill.test.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://balsamhill.test.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-west1-balsambrands-20022025.cloudfunctions.net/pricesmart-generic-report-generator-test
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-west1-balsambrands-20022025.cloudfunctions.net/downstream-balsam-cf-test
DOWNSTREAM_BUCKET_NAME = mulesoft-events-test
SAKS_URL_PREFIX = https://balsamhill.test.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-test
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-balsam-test
ENVIRONMENT=test
LOG_TITLE = pricesmart-balsam-promo-test
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://balsamhill.test.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://balsamhill.test.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://balsamhill.test.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://balsamhill.test.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate


[pricesmart-uat]
deployment_env=uat
meta_schema=metaschema
secret_project = balsambrands-20022025
secret_version=latest
secret_id=balsamhill_uat
client_schema = public
promo_schema=price_promo
markdown_schema=price_markdown
promo_opt_schema=price_promo_opt
simulation_schema = simulation
um_schema = um
global_schema = global
client_name = balsambrands
project_id = balsambrands-20022025
project_resource = balsambrands
is_slack_logger =
logger_name = price-smart-logger
log_stash_host = **********
log_stash_port = 5044
is_logstash = False
append_user_and_system_info_to_logger= True
log_level = INFO
elastic_apm_url =
elastic_apm_key=
cache_type_env_variable=
cache_distributed_server=
cache_distributed_port=
client_id=
app_date_time_format = "%m/%d/%Y, %H:%M:%S"
app_date_format = "%m/%d/%Y"
OPTIMISE_API_URL = https://balsamhill.uat.impactsmartsuite.com/pricesmart/promo/optimization/optimize
RESIMULATE_API_URL = https://balsamhill.uat.impactsmartsuite.com/pricesmart/promo/optimization/resimulate
REPORT_GENERATOR_API = https://us-west1-balsambrands-20022025.cloudfunctions.net/pricesmart-generic-report-generator-uat
DOWNSTREAM_CLOUD_FUNCTION_URL = https://us-west1-balsambrands-20022025.cloudfunctions.net/downstream-balsam-cf-uat
DOWNSTREAM_BUCKET_NAME = mulesoft-events-uat
SAKS_URL_PREFIX = https://balsamhill.uat.impactsmartsuite.com
log_function_execution_time = True
GLOBAL_LABELS = "platform=DevPlatform, application=PriceSmartDev"
sso_sign_in_provider = saml.pricesmart-biglots-uat
mojo_base_url = https://app.mojohelpdesk.com
mojo_api_url = /api/v2/
cache_enabled =
login_provider = pricesmart-balsam-uat
ENVIRONMENT=uat
LOG_TITLE = pricesmart-balsam-promo-uat
EMAIL_KEY = "************************************"
REFRESH_API_URL = https://balsamhill.uat.impactsmartsuite.com/pricesmart/promo/optimization/refresh
OPT_CALLBACK_ENDPOINT = https://balsamhill.uat.impactsmartsuite.com/pricesmart/api/v1/server-callback   
OPT_EVENT_EDIT_API_URL = https://balsamhill.uat.impactsmartsuite.com/pricesmart/promo/optimization/event-edit
OPT_EVENT_RESIMULATE_API_URL = https://balsamhill.uat.impactsmartsuite.com/pricesmart/promo/optimization/event-resimulate