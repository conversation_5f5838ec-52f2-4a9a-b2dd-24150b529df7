from decision_dashboard import models as dd_models
from decision_dashboard import queries as dd_queries
from configuration.environment import environment
from pricesmart_common.utils import async_execute_query, get_key_value_str, get_str_repr, get_array_format
from pricesmart_common import constants as common_constants
from promotions import utils as promo_utils
from promotions import constants as promo_constants
from logger.logger import logger
import json


async def get_decision_dashboard_promos_data(
    request_payload: dd_models.GetDecisionDashboardPromosRequest
):
    request_payload=request_payload.model_dump()

    query = dd_queries.GET_DECISION_DASHBOARD_DATA_QUERY.format(
        request_payload=get_str_repr(request_payload)
    )
    logger.info(f"get_decision_dashboard_promos_data query: {query}")
    return await async_execute_query(query)


def get_total_business_report_query(
    total_business_filters: dd_models.TotalBusinessReportModel,
    for_download: bool = False
):
    """
    Generate SQL query for Total Business Report.
    
    Args:
        total_business_filters: Filter parameters for the report
        for_download: Whether this is for download (True) or visualization (False)
        
    Returns:
        str: Formatted SQL query
    """
    fetch_query = dd_queries.GET_TOTAL_BUSINESS_REPORT_DATA.format(
        time_level=total_business_filters.time_level,
        start_date=get_str_repr(total_business_filters.start_date),
        end_date=get_str_repr(total_business_filters.end_date),
        product_hierarchy_levels=get_array_format(total_business_filters.product_hierarchy_levels),
        product_hierarchies=get_str_repr(total_business_filters.product_hierarchies),
        for_download=1 if for_download else 0,
        target_currency_id=get_str_repr(total_business_filters.target_currency_id),
    )
    logger.info(f"get_total_business_report_query: {fetch_query}")
    return fetch_query


async def get_total_business_report_data(
    total_business_filters: dd_models.TotalBusinessReportModel,
):
    """
    Get Total Business Report data for visualization.
    
    Args:
        total_business_filters: Filter parameters for the report
        
    Returns:
        dict: JSON data containing business metrics for chart visualization
    """
    fetch_query = get_total_business_report_query(total_business_filters)
    logger.info(f"get_total_business_report_data query: {fetch_query}")
    final_result = await async_execute_query(query=fetch_query)
    return final_result[0]["json_data"]


def get_total_business_report_download_query(
    total_business_filters: dd_models.DownloadTotalBusinessReport,
):
    """
    Generate SQL query for Total Business Report download.
    
    Args:
        total_business_filters: Filter parameters for the report download
        
    Returns:
        str: Formatted SQL query
    """
    fetch_query = get_total_business_report_query(total_business_filters, for_download=True)
    logger.info(f"get_total_business_report_download_query: {fetch_query}")
    return fetch_query