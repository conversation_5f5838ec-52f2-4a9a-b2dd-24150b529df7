FETCH_PROMOS_DECISION_DASHBOARD_BY_ID = """
    final_eligible_promos_cte AS (
        SELECT 
            promo_id, event_id
        FROM 
            price_promo.promo_master 
        where 
            is_deleted = 0
            and status IN (-1, 4, 8)
            {ids_where_str}
    ),
"""

GET_DECISION_DASHBOARD_DATA_QUERY = """
    SELECT
        promo_id,
        promo_name AS promo_name,
        start_date,
        end_date,
        event_id, 
        event_name,
        country_id,
        country_name,
        is_locked,
        created_by,
        offer_comment,
        status_id,
        status,
        step_count,
        products_count,
        stores_count,
        product_selection_type_id,
        product_selection_type,
        store_selection_type_id,
        store_selection_type,
        exclusion_selection_type_id,
        exclusion_selection_type,
        customer_type_id,
        customer_type,
        offer_distribution_channel_id,
        offer_distribution_channel,
        last_approved_scenario_id,
        recommendation_type_id,
        recommendation_type,
        is_under_processing,
        is_auto_resimulated,
        currency_id,
        currency_name,
        currency_symbol,
        is_overridden,
        override_comment,
        override_reason,
        discount_level_id,
        discount_level,

        actual_performance,
        finalized_performance,
        
        no_of_txn,
        units_per_txn,
        avg_basket_size,
        ---
        actualized_promo_spend,

        jsonb_build_object(
            'margin',finalized_margin,
            'revenue',finalized_revenue,
            'discount',finalized_discount,
            'promo_spend',finalized_promo_spend,
            'sales_units',finalized_sales_units,
            'margin_percent',finalized_margin_percent,
            'contribution_margin',finalized_contribution_margin,
            'contribution_revenue',finalized_contribution_revenue,
            'contribution_margin_percent',finalized_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'finalized_st_percent',finalized_st_percent,
            'order_discount', finalized_order_discount,
            'revenue_per_style', finalized_revenue_per_style
        ) as finalized,

        jsonb_build_object(
            'margin', finalized_baseline_margin,
            'revenue', finalized_baseline_revenue,
            'sales_units', finalized_baseline_sales_units,
            'margin_percent', finalized_baseline_margin_percent
        ) as baseline,  

        jsonb_build_object(
            'sales_units', finalized_incremental_sales_units,
            'revenue', finalized_incremental_revenue,
            'margin', finalized_incremental_margin,
            'margin_percent', finalized_incremental_margin_percent
        ) as finalized_incremental,

        jsonb_build_object(
            'discount',ia_recc_discount
        ) as ia_recommend,
    
        jsonb_build_object(
            'margin',original_margin,
            'revenue',original_revenue,
            'discount',original_discount,
            'promo_spend',original_promo_spend,
            'sales_units',original_sales_units,
            'margin_percent',original_margin_percent,
            'contribution_margin',original_contribution_margin,
            'contribution_revenue',original_contribution_revenue,
            'contribution_margin_percent',original_contribution_margin_percent,
            'order_discount', original_order_discount
        ) as original,

        jsonb_build_object(
            'sales_units', original_baseline_sales_units,
            'revenue', original_baseline_revenue,
            'margin', original_baseline_margin,
            'margin_percent', original_baseline_margin_percent
        ) as original_baseline,

        jsonb_build_object(
            'margin_percent', original_incremental_margin_percent
        ) as original_incremental,

        jsonb_build_object(
            'margin',finalized_stack_margin,
            'revenue',finalized_stack_revenue,
            'promo_spend',finalized_stack_promo_spend,
            'sales_units',finalized_stack_sales_units,
            'margin_percent',finalized_stack_margin_percent,
            'contribution_margin',finalized_stack_contribution_margin,
            'contribution_revenue',finalized_stack_contribution_revenue,
            'contribution_margin_percent',finalized_stack_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'finalized_stack_st_percent',finalized_stack_st_percent,
            'order_discount', finalized_stack_order_discount,
            'revenue_per_style', finalized_stack_revenue_per_style
        ) as finalized_stack,

        jsonb_build_object(
            'margin', finalized_stack_baseline_margin,
            'revenue', finalized_stack_baseline_revenue,
            'sales_units', finalized_stack_baseline_sales_units,
            'margin_percent', finalized_stack_baseline_margin_percent
        ) as stack_baseline,

        jsonb_build_object(
            'sales_units', finalized_stack_incremental_sales_units,
            'revenue', finalized_stack_incremental_revenue,
            'margin', finalized_stack_incremental_margin,
            'margin_percent', finalized_stack_incremental_margin_percent
        ) as finalized_stack_incremental,

        jsonb_build_object(
            'margin',original_stack_margin,
            'revenue',original_stack_revenue,
            'promo_spend',original_stack_promo_spend,
            'sales_units',original_stack_sales_units,
            'margin_percent',original_stack_margin_percent,
            'contribution_margin',original_stack_contribution_margin,
            'contribution_revenue',original_stack_contribution_revenue,
            'contribution_margin_percent',original_stack_contribution_margin_percent,
            'order_discount', original_stack_order_discount
        ) as original_stack,

        jsonb_build_object(
            'sales_units', original_stack_baseline_sales_units,
            'revenue', original_stack_baseline_revenue,
            'margin', original_stack_baseline_margin,
            'margin_percent', original_stack_baseline_margin_percent
        ) as original_stack_baseline,

        jsonb_build_object(
            'margin_percent', original_stack_incremental_margin_percent
        ) as original_stack_incremental,

        jsonb_build_object(
            'margin',actualized_margin,
            'revenue',actualized_revenue,
            'promo_spend',actualized_promo_spend,
            'sales_units',actualized_sales_units,
            'margin_percent',actualized_margin_percent,
            'contribution_margin',actualized_contribution_margin,
            'contribution_revenue',actualized_contribution_revenue,
            'contribution_margin_percent',actualized_contribution_margin_percent,
            'total_inventory',finalized_total_inventory,
            'actualized_st_percent',actualized_st_percent,
            'order_discount', actualized_order_discount,
            'revenue_per_style', actual_revenue_per_style
        ) as actualized,

        jsonb_build_object(
            'sales_units', actualized_incremental_sales_units,
            'revenue', actualized_incremental_revenue,
            'margin', actualized_incremental_margin,
            'margin_percent', actualized_incremental_margin_percent
        ) as actualized_incremental

        
    FROM 
        price_promo.fn_fetch_decision_dashboard_table_data({request_payload}::jsonb, false);
"""

GET_TOTAL_BUSINESS_REPORT_DATA = """
    SELECT price_promo.fn_dd_total_business_report(
        _time_level := {time_level}::integer,
        _start_date := {start_date}::date,
        _end_date := {end_date}::date,
        product_hierarchy_levels := {product_hierarchy_levels}::integer[],
        _product_filters := {product_hierarchies}::jsonb,
        _download_flag := {for_download}::integer,
        _target_currency_id := {target_currency_id}::integer
    ) AS json_data
"""
