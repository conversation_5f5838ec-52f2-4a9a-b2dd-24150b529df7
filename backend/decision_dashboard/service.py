from decision_dashboard import models as dd_models
from decision_dashboard import data as dd_data_module

async def get_decision_dashboard_promos(
    request_payload: dd_models.GetDecisionDashboardPromosRequest
):
    data = await dd_data_module.get_decision_dashboard_promos_data(
        request_payload
    )

    return data


async def get_total_business_report(
    total_business_filters: dd_models.TotalBusinessReportModel,
):
    """
    Get Total Business Report data for visualization.
    
    Args:
        total_business_filters: Filter parameters for the report
        
    Returns:
        dict: JSON data containing business metrics for chart visualization
    """
    return await dd_data_module.get_total_business_report_data(total_business_filters)


def get_total_business_report_query(
    total_business_filters: dd_models.DownloadTotalBusinessReport,
):
    """
    Get Total Business Report query for download.
    
    Args:
        total_business_filters: Filter parameters for the report download
        
    Returns:
        str: SQL query for background task processing
    """
    return dd_data_module.get_total_business_report_download_query(total_business_filters)