from fastapi import APIRouter, BackgroundTasks
from app.dependencies import UserDependency
from decision_dashboard import models as dd_models
from decision_dashboard import service as dd_service
from pricesmart_common import utils as common_utils
from pricesmart_common import constants as global_constants
from promotions import utils as promo_utils

router = APIRouter(tags=["decision dashboard"])


@router.post("/decision-dashboard/promos")
async def get_decision_dashboard_promos(
    request_payload: dd_models.GetDecisionDashboardPromosRequest,
):
    """
    Get promotions for the decision dashboard view.
    
    Args:
        request_payload (GetDecisionDashboardPromosRequest): Request model containing filters and parameters
            for fetching promotions
            
    Returns:
        dict: Response containing:
            - data: List of promotions matching the request criteria
            - message: Success/error message
            - status: HTTP status code
    """
    data = await dd_service.get_decision_dashboard_promos(
        request_payload
    )

    return common_utils.create_response(
        data = data
    )


@router.post("/decision-dashboard/total-business-report")
async def get_total_business_report(
    total_business_filters: dd_models.TotalBusinessReportModel,
):
    """
    Get Total Business Report data for line chart visualization.
    
    This endpoint provides business performance metrics (Units, Revenue, Margin, GM%) 
    across different data types (Actuals TY, Actuals LY, MFP, Forecast) at specified 
    hierarchy and time levels for Decision Dashboard visualization.
    
    Args:
        total_business_filters (TotalBusinessReportModel): Filters for the report including:
            - product_hierarchy_levels: Product hierarchy levels to analyze
            - time_level: Time aggregation (0=Daily, 1=Weekly, 2=Monthly, 3=Quarterly)
            - start_date, end_date: Date range for analysis
            - product_hierarchies: Specific product hierarchy filters
            - target_currency_id: Currency for financial metrics
        
    Returns:
        dict: Response containing:
            - data (dict): JSON data structured for line chart visualization
            - message (str): Success/error message
            - status (bool): Operation status
    """
    data = await dd_service.get_total_business_report(total_business_filters)
    return common_utils.create_response(data=data)


@router.post("/decision-dashboard/download-total-business-report")
async def download_total_business_report(
    request_body: dd_models.DownloadTotalBusinessReport,
    background_tasks: BackgroundTasks,
    user_id: UserDependency
):
    """
    Download Total Business Report in XLSX format.
    
    This endpoint processes the same data as the visualization endpoint but formats 
    it for Excel download. The report includes business performance metrics across 
    multiple dimensions and is processed in the background for optimal user experience.
    
    Args:
        request_body (DownloadTotalBusinessReport): Report download configuration including:
            - All TotalBusinessReportModel fields
            - report_type: Type of report for processing
            - report_name: Name for the downloaded file
            - for_download: Always True for download requests
        background_tasks (BackgroundTasks): Background tasks handler
        user_id (UserDependency): User identifier from authentication
        
    Returns:
        dict: Response containing:
            - message (str): Confirmation message
            - data (dict): Empty dict (actual data sent via background process)
    """
    data = {}
    _message = "Request received. You will be notified once the downloads are ready."
    fetch_query = dd_service.get_total_business_report_query(request_body)
    background_tasks.add_task(
        promo_utils.cloud_function_report_handler,
        fetch_query=fetch_query,
        report_file_name=request_body.report_name,
        report_type=request_body.report_type,
        user_id=user_id,
        is_json_data=True,
        promo_name=None    
    )
    return common_utils.create_response(message=_message, data=data)