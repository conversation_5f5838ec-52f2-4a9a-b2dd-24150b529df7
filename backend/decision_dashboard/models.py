from pricesmart_common import models as common_models
from typing import Optional
from pydantic import model_validator, BaseModel, field_validator
from pricesmart_common import helpers as common_helpers
from datetime import date

class Aggregation(BaseModel):
    marketing: list[str] = None
    timeline: int = -200
    product_hierarchy_levels: list[int] = []
    store_hierarchy_levels: list[int] = []
    target_currency_id: Optional[int] = None


class GetDecisionDashboardPromosRequest(common_models.OptionalBaseFilters):
    event_ids: list[int] = []
    promo_ids: list[int] = []
    aggregation: Optional[Aggregation]

    @model_validator(mode="before")
    def validate_params(cls, values):
        return common_helpers.validate_non_id_fields(values)


class TotalBusinessReportModel(BaseModel):
    """
    Model for Total Business Report with support for different hierarchy and time levels.
    Supports metrics: Units, Revenue, Margin, GM% for Actuals TY, Actuals LY, MFP, and Forecast.
    """
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    product_hierarchies: dict[str, list[int]] = {}
    target_currency_id: Optional[int] = None
    product_hierarchy_levels: list[int] = [0]
    time_level: int = 1  # 0=Daily, 1=Weekly, 2=Monthly, 3=Quarterly

    @field_validator("time_level")
    @classmethod
    def validate_time_level(cls, time_level_value):
        """Validate time_level is within allowed range."""
        valid_levels = {0, 1, 2, 3}  # 0=Daily, 1=Weekly, 2=Monthly, 3=Quarterly
        if time_level_value not in valid_levels:
            raise ValueError(
                f"time_level must be one of {sorted(valid_levels)}. "
                f"0=Daily, 1=Weekly, 2=Monthly, 3=Quarterly. Got: {time_level_value}"
            )
        return time_level_value

    @field_validator("product_hierarchy_levels")
    @classmethod
    def set_default_product_hierarchy_levels(cls, product_hierarchy_levels_list):
        # If the list is empty, set to product hierarchy level 0
        if not product_hierarchy_levels_list:
            return [0]
        return product_hierarchy_levels_list

class DownloadTotalBusinessReport(TotalBusinessReportModel):
    """
    Model for downloading Total Business Report in XLSX format.
    """
    report_type: str 
    report_name: str = "Total Business Report"
    for_download: bool = True

    
