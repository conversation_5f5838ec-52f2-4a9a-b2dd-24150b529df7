UPDATE_PROMO_DETAILS_QUERY = """
    update price_promo.tb_exmd_promo 
    SET 
        folder_id = NULL
    where
        tb_exmd_promo.promo_id = {promo_id};
    
    update price_promo.promo_master
    SET
        name = {promo_name},
        start_date = {promo_start_date},
        end_date = {promo_end_date},
        status = {promo_status},
        last_approved_scenario_id = {last_approved_scenario_id},
        recommendation_type_id = {recommendation_type_id},
        updated_by = {user_id}
    where 
        promo_id = {promo_id};
"""


UPDATE_PROMO_DEATILS_QUERY = """
    -- Fetch current values
    SELECT 
        name, 
        start_date, 
        end_date, 
        event_id,
        status
    INTO 
        v_current_promo_name, 
        v_current_start_date, 
        v_current_end_date, 
        v_current_event_id,
        v_current_status
    FROM 
        price_promo.promo_master
    WHERE 
        promo_id = v_promo_id;

    v_finalized_status_values = array(select remarks::int2[] from metaschema.tb_app_sub_master where name = 'stacked_offers_eligibility');
    
    -- 1. If only promo name changes
    IF v_promo_name IS NOT NULL AND v_promo_name != v_current_promo_name 
        AND (v_start_date IS NULL OR v_start_date = v_current_start_date) 
        AND (v_end_date IS NULL OR v_end_date = v_current_end_date) THEN
        UPDATE price_promo.promo_master
        SET 
            name = v_promo_name,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
    END IF;
    -- 2. If only start date changes
    IF v_start_date IS NOT NULL AND v_start_date != v_current_start_date 
        AND (v_end_date IS NULL OR v_end_date = v_current_end_date)
        AND (v_promo_name IS NULL OR v_promo_name = v_current_promo_name) THEN
        UPDATE price_promo.promo_master
        SET 
            start_date = v_start_date,
            status = 0,
            has_stacked_offers = null,
            last_approved_scenario_id = v_last_approved_scenario_id,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;

    END IF;
    -- 3. If only end date changes
    IF v_end_date IS NOT NULL AND v_end_date != v_current_end_date
        AND (v_start_date IS NULL OR v_start_date = v_current_start_date)
        AND (v_promo_name IS NULL OR v_promo_name = v_current_promo_name) THEN
        UPDATE price_promo.promo_master
        SET 
            end_date = v_end_date,
            status = 0,
            last_approved_scenario_id = v_last_approved_scenario_id,
            has_stacked_offers = null,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;

    END IF;
    -- 4. If both start and end dates change
    IF v_start_date IS NOT NULL AND v_start_date != v_current_start_date 
        AND v_end_date IS NOT NULL AND v_end_date != v_current_end_date 
        AND (v_promo_name IS NULL OR v_promo_name = v_current_promo_name) THEN
        UPDATE price_promo.promo_master
        SET 
            start_date = v_start_date,
            end_date = v_end_date,
            status = 0,
            has_stacked_offers = null,
            last_approved_scenario_id = v_last_approved_scenario_id,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;

    END IF;
    -- 5. If promo name and start date change
    IF v_promo_name IS NOT NULL AND v_promo_name != v_current_promo_name 
        AND v_start_date IS NOT NULL AND v_start_date != v_current_start_date 
        AND (v_end_date IS NULL OR v_end_date = v_current_end_date) THEN
        UPDATE price_promo.promo_master
        SET 
            name = v_promo_name,
            start_date = v_start_date,
            status = 0,
            has_stacked_offers = null,
            last_approved_scenario_id = v_last_approved_scenario_id,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;

    END IF;
    -- 6. If promo name and end date change
    IF v_promo_name IS NOT NULL AND v_promo_name != v_current_promo_name 
        AND v_end_date IS NOT NULL AND v_end_date != v_current_end_date 
        AND (v_start_date IS NULL OR v_start_date = v_current_start_date) THEN
        UPDATE price_promo.promo_master
        SET 
            name = v_promo_name,
            end_date = v_end_date,
            status = 0,
            has_stacked_offers = null,
            last_approved_scenario_id = v_last_approved_scenario_id,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;
    END IF;
    -- 7. If both promo name and dates change
    IF v_promo_name IS NOT NULL AND v_promo_name != v_current_promo_name 
        AND v_start_date IS NOT NULL AND v_start_date != v_current_start_date 
        AND v_end_date IS NOT NULL AND v_end_date != v_current_end_date THEN
        UPDATE price_promo.promo_master
        SET 
            name = v_promo_name,
            start_date = v_start_date,
            end_date = v_end_date,
            status = 0,
            has_stacked_offers = null,
            last_approved_scenario_id = v_last_approved_scenario_id,
            recommendation_type_id = v_recommendation_type_id,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;

        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;
    END IF;
      
    -- 8. if only status change - placeholder to draft 
    IF v_promo_name IS NOT NULL AND v_promo_name = v_current_promo_name 
        AND (v_start_date IS NULL OR v_start_date = v_current_start_date) 
        AND (v_end_date IS NULL OR v_end_date = v_current_end_date) THEN
        UPDATE price_promo.promo_master
        SET 
            status = 0,
            updated_by = v_user_id,
            updated_at = NOW()
        WHERE promo_id = v_promo_id;
        update price_promo.tb_exmd_promo
        set
            folder_id = NULL,
            price_filter_id = NULL
        where
            tb_exmd_promo.promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;
    END IF;

    -- 9. If only event_id changes
    IF v_event_id IS NOT NULL AND v_event_id != v_current_event_id THEN
        UPDATE price_promo.promo_master
        SET 
            event_id = v_event_id,
            status = 0,
            updated_by = v_user_id,
            updated_at = NOW(),
            step_count = 0,
            product_selection_type = null,
            store_selection_type = null,
            exclusion_selection_type = null,
            products_count=0,
            stores_count=0,
            currency_id = (
                select currency_id from global.tb_country_currency_mapping where country_id = (
                    select country_id from price_promo.event_master where event_id = v_event_id
                )
            )
        WHERE promo_id = v_promo_id;
        -- Delete related data from the three tables
        DELETE FROM price_promo.ia_scenario_master WHERE promo_id = v_promo_id;
        DELETE FROM price_promo.ia_ps_scenario_discounts WHERE promo_id = v_promo_id;
        delete from price_promo.ps_rules where promo_id = v_promo_id;
        delete from price_promo.ps_scenario_discounts where promo_id = v_promo_id;
        delete from price_promo.promo_product where promo_id = v_promo_id;
        delete from price_promo.promo_product_hierarchy where promo_id = v_promo_id;
        delete from price_promo.promo_store where promo_id = v_promo_id;
        delete from price_promo.promo_store_hierarchy where promo_id = v_promo_id;
        delete from price_promo.tb_promo_store_groups where promo_id = v_promo_id;
        delete from price_promo.promo_store_sg_hierarchy where promo_id = v_promo_id;
        delete from price_promo.excluded_hierarchy_combination where promo_id = v_promo_id;
        delete from price_promo.excluded_product_groups where promo_id = v_promo_id;
        delete from price_promo.excluded_products where promo_id = v_promo_id;
        delete from price_promo.included_products where promo_id = v_promo_id;
        delete from price_promo.included_product_hierarchy where promo_id = v_promo_id;
        delete from price_promo.included_promo_product_groups where promo_id = v_promo_id;
        delete from price_promo.included_promo_pg_hierarchy where promo_id = v_promo_id;
        perform price_promo.fn_delete_promo_override_forecast(v_promo_id); 
        perform price_promo.fn_update_promo_event(v_promo_id, v_event_id);

        if not v_current_status = any(v_finalized_status_values) then
            call price_promo_opt.pc_delete_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[v_promo_id], NULL::integer[], 1, 1, 1);
        end if;
    END IF;

    perform price_promo.fn_update_promo_stacked_offers_mapping(array[v_promo_id]);


"""


STEP0_EDIT_PROMO_DETAILS_QUERY = """
    DO $$
    DECLARE
        v_promo_id INTEGER := {promo_id};
        v_promo_name TEXT := {promo_name};
        v_start_date DATE := {promo_start_date};
        v_end_date DATE := {promo_end_date};
        v_event_id INTEGER := {event_id};
        v_promo_status INTEGER := {promo_status};
        v_last_approved_scenario_id INTEGER := {last_approved_scenario_id};
        v_recommendation_type_id INTEGER := {recommendation_type_id};
        v_user_id INTEGER := {user_id};
        v_current_promo_name TEXT;
        v_current_start_date DATE;
        v_current_end_date DATE;
        v_current_status INTEGER;
        v_current_event_id INTEGER;
        v_finalized_status_values INTEGER[]; 
    BEGIN
    {delete_query}
    {update_promo_details_query}
    {update_promo_metrics_query}
    {insert_statements}
        
    END $$;
"""


UPDATE_PROMO_METRICS_QUERY = """
    update price_promo.tb_placeholder_targets
    set 
        inventory = {inventory},
        discount = {discount},
        revenue_target = {revenue_target},
        units_target = {units_target},
        gross_margin_target = {gross_margin_target},
        gross_margin_percent_target = {gross_margin_percent_target}
    where 
        promo_id = {promo_id};
"""


STEP1_UPDATE_PROMO_DETAILS_QUERY = """
    update price_promo.promo_master pm
    SET
        status = {promo_status},
        last_approved_scenario_id = {last_approved_scenario_id},
        recommendation_type_id = {recommendation_type_id},
        updated_at = NOW(),
        updated_by = {user_id}
    where 
        pm.promo_id = {promo_id};

    update price_promo.ps_rules pr
    set 
        units_target = null,
        revenue_target = null,
        gross_margin_target = null,
        gross_margin_percent_target = null
    where 
        pr.promo_id = {promo_id};
        
    update price_promo.tb_exmd_promo
    set
        folder_id = NULL,
        price_filter_id = NULL
    where
        tb_exmd_promo.promo_id = {promo_id};
"""
