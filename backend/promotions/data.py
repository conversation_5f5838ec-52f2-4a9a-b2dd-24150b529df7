from datetime import datetime,date
from typing import Optional
import numpy as np
import pandas as pd
import traceback
import pytz
from configuration.environment import environment
from events.data import get_event_details
from exceptions.exceptions import (
    CommonException,
    EmptyExclusionUploadException,
    InvalidTemplateException,
    RuntimeExecutionException,
    UnknownFileException,
)
from fastapi import UploadFile, status
from logger.logger import logger
from pricesmart_common import constants as global_constants
from pricesmart_common import utils as global_utils
from pricesmart_common import models as common_models
from pricesmart_common.data import get_config_value
from pricesmart_common.utils import (
    get_array_format, get_key_value_str, get_str_repr, async_execute_query,
    get_json_format, send_email
)
from common_utils.notifications import NotificationModules,NotificationHeaderText
from product import utils as product_utils
from promotions import constants as promo_constants
from promotions import models as promo_models
from promotions import queries as promo_queries
from promotions import service as promo_service
from promotions import utils as promo_utils
from promotions import types as promo_types
from promotions import exceptions as promo_exceptions
from promotions.constants import WHERE_CONDITION, AND
from promotions.queries_collections import edit_flow_queries as edit_promo_queries
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum, PromoStatusEnum
from events import constants as events_constants
from promotions.enums import DiscountLevelEnum
import json
from server_sent_events.utils import insert_notification
from promotions.enums import OfferTypeEnum
from collections import defaultdict
from client_configuration import constants as client_configuration_constants
from filters.types import HierarchyFiltersInfoType, ExclusionUploadConfigInfoType
from scheduler import utils as sched_utils

today = lambda : datetime.now(pytz.timezone("Asia/Kolkata"))


async def check_promo_existence(
    promo_name, start_date=None, end_date=None, promo_id=None
):
    promo_id_condition = f"and promo_id != {promo_id}" if promo_id else ""
    if start_date and end_date:
        date_condition = (
            f"and start_date <= {get_str_repr(end_date)} and end_date >= {get_str_repr(start_date)}"
        )
    else:
        date_condition = ""
    query = promo_queries.CHECK_PROMO_EXISTENCE_BY_NAME_QUERY.format(
        client_schema=environment.client_schema,
        promo_name=global_utils.get_str_repr(promo_name),
        promo_id_condition=promo_id_condition,
        date_condition=date_condition,
        archive_status = promo_constants.PROMO_STATUS[promo_constants.DELETED]
    )
    print(query)

    result = await async_execute_query(query=query)

    if result and bool(result[0] and result[0]["promo_id"] > 0):
        return True

    return False


async def check_tier_existence_data(promo_id: int, tier_id: Optional[int], tier_name: str):
    query = f"""
        select
        1
        from price_promo.tier_master tm
        where promo_id = {promo_id}
        {f"and tier_id != {tier_id}" if tier_id else ""}
        and tier_name = {get_str_repr(tier_name)}
    """

    tier_name_exists = await async_execute_query(query)
    return bool(tier_name_exists)

async def create_promotion_v3(
    create_promo_request: promo_models.CreatePromoRequest,
    user_id: int
)-> dict:
    query = promo_queries.CREATE_PROMO.format(
        promo_name = get_str_repr(create_promo_request.promo_name),
        promo_start_date = get_str_repr(create_promo_request.start_date),
        promo_end_date = get_str_repr(create_promo_request.end_date),
        promo_status = promo_constants.PROMO_STATUS[
            promo_constants.DRAFT
        ],
        event_id = create_promo_request.event_id,
        user_id = user_id
    )
    data = await async_execute_query(query)
    return data[0] 

def get_blocking_promo_details(
    promo_ids, screen_type, processing_action, ongoing_promo_details
):
    if not promo_ids:
        return ongoing_promo_details

    def remove_conflicting_promos(promos, promo_id, actions_to_remove):
        return [promo for promo in promos if not (promo["promo_id"] == promo_id and promo["processing_action"] in actions_to_remove)]

    processing_promo_details = {
        "promo_id": promo_ids[0],
        "processing_action": processing_action,
    }

    if screen_type == "step3":
        if processing_action in ["resimulate", "scenario_name_edit", "tier_management"]:
            ongoing_promo_details = remove_conflicting_promos(
                ongoing_promo_details, processing_promo_details["promo_id"], ["optimise"]
            )
        elif processing_action in ["optimise", "override_forecast"]:
            ongoing_promo_details = remove_conflicting_promos(
                ongoing_promo_details, processing_promo_details["promo_id"], ["resimulate", "scenario_name_edit"]
            )
    elif screen_type == "tier_management" and processing_action == "optimise":
        ongoing_promo_details = remove_conflicting_promos(
            ongoing_promo_details, processing_promo_details["promo_id"], ["optimise"]
        )

    return ongoing_promo_details



async def check_promo_background_process_status_data(
    promo_ids: list[int], processing_action, screen_type
) -> list:

    query = promo_queries.CHECK_PROMO_BACKGROUND_PROCESS_STATUS_QUERY.format(
        client_schema=environment.client_schema,
        promo_ids=get_array_format(promo_ids)
    )
    ongoing_promo_details = await async_execute_query(query=query)

    return get_blocking_promo_details(
        promo_ids, screen_type, processing_action, ongoing_promo_details
    )


async def get_last_finalised_scenario_id(promo_id):
    query = f"""
            SELECT pm.last_approved_scenario_id
            FROM price_promo.promo_master pm
            WHERE promo_id = {promo_id} and status in (4, 8);
        """
    response = await async_execute_query(query=query)
    return response[0]["last_approved_scenario_id"] if response else None


async def get_all_promo_workbench(
    product_hierarchies: dict,
    store_hierarchies: dict,
    dates_range: dict,
    screen_type: str,
    promo_ids: list,
):
    fetch_query = ""
    date_range_where_clause = ""

    promo_start_date = dates_range.get("start_date")
    promo_end_date = dates_range.get("end_date")

    if promo_start_date and promo_end_date:
        date_range_where_clause = promo_utils.apply_date_range_filter(
            promo_start_date, promo_end_date
        )

    store_hierarchical_having_clause = promo_utils.build_hierarchical_having_clause(
        store_hierarchies
    )

    product_hierarchical_where_clause = WHERE_CONDITION + AND.join(
        [
            f"{get_key_value_str(key, val)}"
            for key, val in product_hierarchies.items()
            if val
        ]
    )

    if screen_type == promo_constants.SCREEN_DECISION_DASHBOARD:
        fetch_query = promo_queries.FETCH_PROMOS_DECISION_DASHBOARD
    elif screen_type == promo_constants.SCREEN_CALENDAR_VIEW:
        fetch_query = promo_queries.FETCH_PROMOS_CALENDAR_VIEW
    elif screen_type == promo_constants.SCREEN_WORKBENCH:
        fetch_query = promo_queries.FETCH_PROMOS_WORKBENCH
        if promo_ids:  # Check if promo IDs are provided
            fetch_query = promo_queries.FETCH_PROMOS_WORKBENCH_BY_PROMO_IDS.format(
                promo_ids = get_str_repr(promo_ids),
                promo_schema=environment.promo_schema,
            )

    if not promo_ids:
        fetch_query = fetch_query.format(
            client_schema=environment.client_schema,
            simulation_schema=environment.simulation_schema,
            meta_schema=environment.meta_schema,
            date_range_where_clause=date_range_where_clause,
            product_hierarchical_where_clause=product_hierarchical_where_clause,
            store_hierarchical_having_clause=store_hierarchical_having_clause,
        )
    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return final_result


async def promo_downloads_data(
    product_hierarchies: dict,
    store_hierarchies: dict,
    show_partially_overlapping_events: bool,
    dates_range: dict,
    promo_id: int,
    aggregation: dict,
    promo_name: str,
    aggregation_level: int,
    report_name: str,
    report_type: str,
    report_file_name: str,
    user_id: int,
    target_currency_id: Optional[int],
    promo_ids: Optional[list[int]] = [],
    event_ids: Optional[list[int]] = [],
    priority_numbers: Optional[common_models.PriorityNumbers] = []
):
    fetch_query = ""

    promo_start_date = dates_range.get("start_date")
    promo_end_date = dates_range.get("end_date")

    request_payload = {
        "product_hierarchies": product_hierarchies,
        "store_hierarchies": store_hierarchies,
        "start_date": promo_start_date,
        "end_date": promo_end_date,
        "aggregation": aggregation,
        "target_currency_id": target_currency_id,
        "show_partially_overlapping_events": show_partially_overlapping_events,
        "promo_ids": promo_ids,
        "event_ids": event_ids,
        "priority_numbers": priority_numbers
    }

    if report_name == promo_constants.DECISION_DASHBOARD_REPORT_EXTENSIVE_DATA:
        product_hierarchy_selection = await promo_utils.build_product_hierarchy_select_clause_for_download(aggregation.get("product_hierarchy_levels"))
        store_hierarchy_selection = await promo_utils.build_store_hierarchy_select_clause_for_download(aggregation.get("store_hierarchy_levels"))
        fetch_query = promo_queries.FETCH_PROMOS_DECISION_DASHBOARD_DOWNLOAD.format(
            request_payload=get_str_repr(request_payload),
            product_hierarchy_selection=product_hierarchy_selection,
            store_hierarchy_selection=store_hierarchy_selection
        )
    elif report_name == promo_constants.CALENDAR_VIEW_REPORT_EXTENSIVE_DATA:
        fetch_query = promo_queries.FETCH_PROMOS_CALENDAR_VIEW_DOWNLOAD.format(
            request_payload=get_str_repr(request_payload)
        )
    elif report_name == promo_constants.WORKBENCH_REPORT_EXTENSIVE_DATA:
        fetch_query = promo_queries.FETCH_PROMOS_WORKBENCH_DOWNLOAD.format(
            request_payload=get_str_repr(request_payload)
        )
    elif report_name == f"{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY}_{promo_constants.PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA}":
        request_payload["target_currency_id"] = get_str_repr(request_payload.get("target_currency_id"))
        fetch_query = await promo_utils.generate_simulator_query(
                request_payload, promo_id, aggregation_level
        )

    if promo_name:
        promo_name = promo_utils.impute_special_characters(promo_name)
    print(fetch_query)
    await promo_utils.cloud_function_report_handler(
        fetch_query=fetch_query,
        report_file_name=report_file_name,
        report_type=report_type,
        user_id=user_id,
        promo_name=promo_name,
    )
    return True


async def get_promo_results_data(
    product_hierarchies: dict,
    store_hierarchies: dict,
    date_range: dict,
    view_by_options: str,
    promo_ids: list,
    event_ids: list,
    target_currency_id: Optional[int],
    show_partially_overlapping_events: bool,
    priority_numbers: Optional[list[int]]
):
    fetch_query = ""
    promo_start_date = date_range.get("start_date")
    promo_end_date = date_range.get("end_date")


    ids_where_str = ""
    if promo_ids or event_ids:
        ids_where_str = f"and pm.promo_id in {get_str_repr(promo_ids)}" if promo_ids else f"and pm.event_id in {get_str_repr(event_ids)}"

    if view_by_options == promo_constants.QUARTER:
        fetch_query = (
            promo_queries.QUARTERLY_PERFORMANCE_GRAPH_QUERY_BY_IDS 
            if (promo_ids or event_ids) 
            else promo_queries.QUARTERLY_PERFORMANCE_GRAPH_QUERY
        )
    if view_by_options == promo_constants.MONTH:
        fetch_query = (
            promo_queries.MONTHLY_PERFORMANCE_GRAPH_QUERY_BY_IDS 
            if (promo_ids or event_ids) 
            else promo_queries.MONTHLY_PERFORMANCE_GRAPH_QUERY
        )
    if view_by_options == promo_constants.WEEK:
        fetch_query = (
            promo_queries.WEEKLY_PERFORMANCE_GRAPH_QUERY_BY_IDS 
            if (promo_ids or event_ids) 
            else promo_queries.WEEKLY_PERFORMANCE_GRAPH_QUERY
        )

    if (promo_ids or event_ids):
        fetch_query = fetch_query.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            ids_where_str=ids_where_str,
            target_currency_id=get_str_repr(target_currency_id),
            priority_number_where_str=f" and pr.priority_number = ANY({get_array_format(priority_numbers)})" if priority_numbers else ""
        )
    else:
        fetch_query = fetch_query.format(
            start_date = get_str_repr(promo_start_date),
            end_date = get_str_repr(promo_end_date),
            product_hierarchies = get_str_repr(product_hierarchies),
            store_hierarchies = get_str_repr(store_hierarchies),
            target_currency_id=get_str_repr(target_currency_id),
            show_partial_overlapping = get_str_repr(show_partially_overlapping_events),
            priority_numbers=get_array_format(priority_numbers)
        )

    print(fetch_query)
    final_result = await async_execute_query(query=fetch_query)
    return final_result


async def get_valid_discounting_level_data(request_payload: promo_models.ValidDiscountingLevel):
    promo_id = request_payload.promo_id
    fetch_query = promo_queries.FETCH_VALID_DISCOUNTING_LEVELS.format(
        promo_id=promo_id, promo_schema=environment.promo_schema
    )
    print(fetch_query)
    result = await async_execute_query(query=fetch_query)
    return result

async def get_discount_levels_data(promo_id: int):
    fetch_query = promo_queries.FETCH_DISCOUNT_LEVELS.format(
        promo_id=promo_id,
    )
    result = await async_execute_query(query=fetch_query)
    return result[0]["discount_levels"] if result else {}


async def tiles_data(
    product_hierarchies: dict,
    store_hierarchies: dict,
    dates_range: dict,
    promo_ids: list,
    event_ids: list,
    screen_type: str,
    target_currency_id: Optional[int],
    show_partially_overlapping_events: bool,
    priority_numbers: Optional[list[int]]
):

    fetch_query = ""

    promo_start_date = dates_range.get("start_date")
    promo_end_date = dates_range.get("end_date")


    ids_where_str = ""
    if screen_type == promo_constants.SCREEN_DECISION_DASHBOARD:
        if promo_ids or event_ids:
            ids_where_str = f"and pm.promo_id in {get_str_repr(promo_ids)}" if promo_ids else f"and pm.event_id in {get_str_repr(event_ids)}"
            fetch_query = promo_queries.FETCH_DD_TILES_ID_BASED
        else:
            fetch_query = promo_queries.FETCH_DD_TILES_FILTER_BASED
    elif screen_type == promo_constants.SCREEN_WORKBENCH:
        if promo_ids or event_ids:
            ids_where_str = f"promo_id in {get_str_repr(promo_ids)}" if promo_ids else f"event_id in {get_str_repr(event_ids)}"
            fetch_query = promo_queries.FETCH_WORKBENCH_TILES_ID_BASED
        else:
            fetch_query = promo_queries.FETCH_WORKBENCH_TILES_FILTER_BASED

    if promo_ids or event_ids:
        fetch_query = fetch_query.format(
            client_schema=environment.client_schema,
            promo_schema=environment.promo_schema,
            simulation_schema=environment.simulation_schema,
            target_currency_id=get_str_repr(target_currency_id),
            ids_where_str=ids_where_str,
            priority_number_where_str=f" and pr.priority_number = ANY({get_array_format(priority_numbers)})" if priority_numbers else " "
        )
    else:
        fetch_query = fetch_query.format(
            start_date = get_str_repr(promo_start_date),
            end_date = get_str_repr(promo_end_date),
            product_hierarchies = get_str_repr(product_hierarchies),
            store_hierarchies = get_str_repr(store_hierarchies),
            target_currency_id=get_str_repr(target_currency_id),
            show_partial_overlapping = get_str_repr(show_partially_overlapping_events),
            priority_numbers=get_array_format(priority_numbers)
        )
    print(fetch_query)
    return await async_execute_query(query=fetch_query)


async def fetch_pricing_with_max_usage(start_date: date,end_date: date):
    """
    Fetch the weighted base price and weighted cost price based on the most used period (month/year) within the date range.

    Args:
    date_range (dict): The date range with 'start_date' and 'end_date'.

    Returns:
    tuple: The weighted base price and weighted cost price.
    """
    month = -1
    year = -1

    if start_date.year == end_date.year and start_date.month < end_date.month:
        month = promo_utils.determine_max_usage_period(start_date, end_date, "month")
        year = start_date.year
    if start_date.year == end_date.year and start_date.month == end_date.month:
        month = start_date.month
        year = start_date.year
    if start_date.year < end_date.year:
        month = promo_utils.determine_max_usage_period(start_date, end_date, "month")
        year = promo_utils.determine_max_usage_period(start_date, end_date, "year")

    fetch_query = promo_queries.FETCH_WBP_WCP_QUERY.format(month=month, year=year)
    response = await async_execute_query(query=fetch_query)

    weighted_base_price = None
    weighted_cost_price = None
    if response:
        weighted_base_price = int([data["weighted_base_price"] for data in response][0])
        weighted_cost_price = int([data["weighted_cost_price"] for data in response][0])
    return weighted_base_price, weighted_cost_price


async def calculate_metrics_data(
    metrics: dict, date_range: dict
) -> promo_types.CalculatedMetrics:

    weighted_base_price, weighted_cost_price = await fetch_pricing_with_max_usage(
        datetime.strptime(date_range["start_date"],global_constants.PYTHON_DATE_FORMAT),
        datetime.strptime(date_range["end_date"],global_constants.PYTHON_DATE_FORMAT)
    )
    if weighted_base_price is None or weighted_cost_price is None:
        logger.info(
            "No data received from tb_placeholder_pricing table. Sending response as null...!!"
        )
        return {
            "sales_units": None,
            "gross_margin": None,
            "gross_margin_percent": None,
        }

    weighted_selling_price = promo_utils.calculate_weighted_selling_price(
        weighted_base_price, metrics["discount"]
    )

    sales_units = promo_utils.calculate_sales_units(
        metrics["revenue_target"], weighted_selling_price
    )
    gross_margin = promo_utils.calculate_gross_margin(
        metrics["revenue_target"], weighted_cost_price, weighted_selling_price
    )
    gross_margin_percent = promo_utils.calculate_gross_margin_percent(
        metrics["revenue_target"], gross_margin
    )

    return {
        "sales_units": sales_units,
        "gross_margin": gross_margin,
        "gross_margin_percent": gross_margin_percent,
    }

async def get_promo_status_types_data(screen_type):
    where_str = ""
    if screen_type == "decision_dashboard":
        where_str = f"where status_id in ({promo_constants.OFFER_STATUS[promo_constants.FINALIZED]}, {promo_constants.OFFER_STATUS[promo_constants.EXECUTION]}, {promo_constants.OFFER_STATUS[promo_constants.PLACEHOLDER]})"
    elif screen_type in ["calendar_view", "workbench"]:
        where_str = ""

    query = f"""
        select
            status_name as label,
            status_id as value
        from price_promo.promo_status_config
        {where_str}
    """
    print(query)
    return await async_execute_query(query=query)

async def save_step1_details_data_v3(
    request_payload: promo_models.InsertPromoStep1Request,
    user_id, 
    extra_query=""
):

    event_details = await get_event_details(
        request_payload.event_id,
        """
        product_inclusion_type,
        store_selection_type,
        has_locked_product_selection,
        has_locked_store_selection
        """
    )
    # Prepare individual queries based on inputs
    request_payload.product_selections.product_selection_type = (
        events_constants.PRODUCT_SELECTION_TYPE_ID_MAPPING[event_details["product_inclusion_type"]]
        if event_details["has_locked_product_selection"]
        else
        request_payload.product_selections.product_selection_type
    )
    request_payload.store_selections.store_selection_type = (
        events_constants.STORE_SELECTION_TYPE_ID_MAPPING[event_details["store_selection_type"]]
        if event_details["has_locked_store_selection"]
        else
        request_payload.store_selections.store_selection_type

    )
    update_promo_details_query = await promo_utils.prepare_update_promo_details_query_v3(
        request_payload,
        user_id
    )

    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    exclusion_query = promo_utils.prepare_exclusion_query_v3(
        request_payload.promo_id,
        request_payload.product_exclusions,
        product_hierarchy_config
    )
    product_queries, updated_product_hierarchy = promo_utils.prepare_product_queries_v3(
        request_payload,
        event_details,
        product_hierarchy_config
    )

    store_queries = promo_utils.prepare_store_queries_v3(
        request_payload,
        event_details
    )

    final_query = promo_utils.compose_final_query(
        promo_id=request_payload.promo_id,
        updated_product_hierarchy=updated_product_hierarchy,
        update_promo_details_query=update_promo_details_query,
        exclusion_query=exclusion_query,
        product_queries=product_queries,
        store_queries=store_queries,
        extra_query=extra_query,
        product_ids=request_payload.product_selections.product_ids,
        store_ids=request_payload.store_selections.store_ids,
        product_group_ids=request_payload.product_selections.product_group_ids,
        store_group_ids=request_payload.store_selections.store_group_ids,
        lifecycle_indicator_ids=(
            request_payload.product_selections.product_hierarchy.get('lifecycle_indicator')
        )
    )

    print(final_query)
    return await async_execute_query(query=final_query)

async def save_discount_rules_data_v3(
    request_payload: promo_models.UpdatePromoStep2Request,
    user_id: int
):
    
    update_query = promo_queries.UPDATE_DISCOUNT_RULES.format(
        promo_id=request_payload.promo_id,
        product_discount_level=get_array_format(request_payload.product_discount_level),
        store_discount_level=get_array_format(request_payload.store_discount_level),
        customer_discount_level=get_array_format(request_payload.customer_discount_level),
        discount_type=get_str_repr(request_payload.discount_type),
        discount_type_id=request_payload.discount_type_id,
        priority_number=request_payload.priority_number,
        promo_schema=environment.promo_schema,
        user_id=user_id,
        vf_type=get_str_repr(request_payload.vendor_funding.vf_type),
        vf_fixed_amount=get_str_repr(request_payload.vendor_funding.vf_fixed_amount),
        vf_per_unit=get_str_repr(request_payload.vendor_funding.vf_per_unit),
        min_upto_percent=get_str_repr(request_payload.upto_percent.min_upto_percent),
        max_upto_percent=get_str_repr(request_payload.upto_percent.max_upto_percent),
        products_on_max_upto_percent=get_str_repr(request_payload.upto_percent.products_on_max_upto_percent)
    )

    logger.info(update_query)
    result = await async_execute_query(update_query)
    return result


async def get_offers_data(promo_id, simulator_action):
    fetch_query = promo_queries.FETCH_RESIMULATE_VALID_OFFER_TYPES.format(
        promo_id=promo_id,
        is_optimisation= 0 if simulator_action == "resimulate" else 1
    )
    print(fetch_query)
    res = await async_execute_query(fetch_query)
    return res[0].get("result")

async def get_ly_targets_data(**kwargs):
    promo_id = kwargs["promo_id"]

    fetch_ly_targets_query = promo_queries.FETCH_LY_TARGETS_QUERY.format(
        promo_id=promo_id
    )

    print(fetch_ly_targets_query)
    return await async_execute_query(query=fetch_ly_targets_query)

async def optimisation_flow_data_v3(
    request_payload: promo_models.OptimisePromoRequest,
    user_id: int
):
    promo_target = request_payload.promo_target
    update_promo_details_query = promo_queries.STEP3_UPDATE_PROMO_DETAILS_QUERY.format(
        promo_id=request_payload.promo_id, step_count=3, user_id=user_id
    )

    save_targets_metrics_query = promo_queries.STEP3_SAVE_TAGETS_METRICS.format(
        promo_id=request_payload.promo_id,
        opt_discount_type_id=get_str_repr(promo_target.opt_discount_type_id),
        discount_type_values=get_array_format(promo_target.discount_type_values),
        min_discount=get_str_repr(promo_target.min_discount),
        max_discount=get_str_repr(promo_target.max_discount),
        gross_margin_target=get_str_repr(promo_target.gross_margin_target),
        gross_margin_lift=get_str_repr(promo_target.gross_margin_lift),
        gross_margin_priority=get_str_repr(promo_target.gross_margin_priority),
        revenue_target=get_str_repr(promo_target.revenue_target),
        revenue_lift=get_str_repr(promo_target.revenue_lift),
        revenue_priority=get_str_repr(promo_target.revenue_priority),
        units_target=get_str_repr(promo_target.units_target),
        units_lift=get_str_repr(promo_target.units_lift),
        units_priority=get_str_repr(promo_target.units_priority),
        gross_margin_percent_target=get_str_repr(promo_target.gross_margin_percent_target),
        gross_margin_percent_lift=get_str_repr(promo_target.gross_margin_percent_lift),
        gross_margin_percent_priority=get_str_repr(promo_target.gross_margin_percent_priority),
        maximization_parameter=get_str_repr(promo_target.maximization_parameter),
        created_by=user_id,
        created_at=str(datetime.now().isoformat())
    )
    delete_query = ""

    final_query = f"""
        BEGIN;
            {update_promo_details_query}
            {save_targets_metrics_query}
            {delete_query}
        END;
    """
    print(final_query)
    await async_execute_query(query=final_query)


async def check_edit_of_last_approved_scenario_v3(
    request_payload: promo_models.SimulatePromoRequest,
):
    last_finalised_scenario_id = await get_last_finalised_scenario_id(request_payload.promo_id)
    if last_finalised_scenario_id is None:
        return False
    for discounts_data in request_payload.discounts_data:
        for scenario_data in discounts_data.scenario_data:
            if scenario_data.scenario_id == last_finalised_scenario_id and scenario_data.updated == True:
                return True
    return False

async def get_scenario_details(promo_id):
    scenario_details_query = promo_queries.GET_PROMO_SCENARIO_DEATILS_QUERY.format(
        promo_schema=environment.promo_schema, promo_id=promo_id
    )
    return await async_execute_query(query=scenario_details_query)


async def sim_edit_flow(promo_id, scenario_id):
    last_recommentation_type_query = f"""
        select
        pm.recommendation_type_id,
        tasm.name as recommendation_type
        from price_promo.promo_master pm
        left join metaschema.tb_app_sub_master tasm on pm.recommendation_type_id= tasm.id
        where promo_id = {promo_id};
    """
    last_recommentation_type_response = await async_execute_query(
        query=last_recommentation_type_query
    )
    last_recommentation_type = [
        item["recommendation_type"] for item in last_recommentation_type_response
    ][0]

    delete_query = ""
    update_promo_details_query = ""

    print(f"last_recommentation_type:: {last_recommentation_type}")

    table_list = [
        "scenario_master",
        "ps_scenario_discounts",
        "ps_recommended_scenarios",
        "ps_recommended_scenarios_agg",
        "ps_recommended_override",
        "ps_recommended_override_agg",
    ]
    for table in table_list:
        delete_query += f"Delete from price_promo.{table} where promo_id = {promo_id} and scenario_id = {scenario_id};"

    delete_query += f"delete from price_promo.tb_promo_override_forecast where promo_id = {promo_id} and scenario_id = {scenario_id};"
    # Fetch last_approved scenario_id
    # If edit_scenario_id == scenario_id: then delete last_approved id from promo_master and delete metrics and revert to draft and update metrics for edited scenario
    # Else: update metrics for edited scenario
    last_approved_scenario_id_query = f"""
        select
            pm.recommendation_type_id,
            tasm.name as recommendation_type,
            pm.last_approved_scenario_id
        from price_promo.promo_master pm
        left join metaschema.tb_app_sub_master tasm on pm.recommendation_type_id= tasm.id
        where promo_id = {promo_id};
    """
    last_approved_scenario_id_res = await async_execute_query(
        query=last_approved_scenario_id_query
    )
    last_approved_scenario_id = [
        row["last_approved_scenario_id"] for row in last_approved_scenario_id_res
    ][0]

    if last_approved_scenario_id == scenario_id:
        # delete last_approved id from promo_master and delete metrics and revert to draft and update metrics for edited scenario
        update_promo_details_query = f"""
            update price_promo.promo_master
                set
                    status = 0,
                    last_approved_scenario_id = NULL,
                    recommendation_type_id = NULL
            where promo_id = {promo_id};
        """
        table_list = [
            "ps_recommended_finalized",
            "ps_recommended_finalized_agg",
        ]
        for table in table_list:
            delete_query += (
                f"Delete from price_promo.{table} where promo_id = {promo_id};"
            )

    return delete_query, update_promo_details_query

async def execution_approve_promotion_data_v3(
    request_payload: promo_models.ExecutionApprovePromosRequest,
    user_id: int,
):
    message = ""
    try:
        print("Downstream integration started")
        await downstream_integration(request_payload.promo_ids, user_id, promo_constants.ACTION_EXECUTE)
        promo_update_query = promo_queries.EXECUTION_APPROVED_PROMO_QUERY.format(
            promo_ids=",".join(str(x) for x in request_payload.promo_ids),
            client_schema=environment.client_schema,
                user_id=user_id,
            )
        await async_execute_query(query=promo_update_query)
        message = await save_notification_after_execution_approve(
            request_payload.promo_ids,
            [],
            request_payload,
            user_id
        )
    except Exception as e:
        logger.error(f"Error in execution_approve_promotion_data_v3: {e}", exc_info=True)
        message = await save_notification_after_execution_approve(
            [],
            request_payload.promo_ids,
            request_payload,
            user_id
        )
    
    return message


async def save_notification_after_execution_approve(
    success_promo_ids: list[int],
    failure_promo_ids: list[int],
    request_payload: promo_models.ExecutionApprovePromosRequest,
    user_id: int
):
    total_promos = len(request_payload.promo_ids)
    status = True
    # Case 1: All promo IDs are successful
    if total_promos == len(success_promo_ids):
        if  total_promos == 1:
            promo_name = await promo_service.get_promo_name_service(request_payload.promo_ids[0])
            message = f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
            identifier = promo_name
        else:
            identifier = f"{total_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            message = f"Execution completed for {identifier}"

    # Case 2: All promo IDs failed
    elif total_promos == len(failure_promo_ids):
        if total_promos == 1:
            promo_name = await promo_service.get_promo_name_service(request_payload.promo_ids[0])
            message = f"Execution failed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}: {promo_name}."
            identifier = promo_name
            status = False
        else:
            identifier = f"{total_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            message = f"Execution failed for {identifier}" 

    # Case 3: Some promo IDs passed, some failed
    else:
        if len(success_promo_ids) == 1 and len(failure_promo_ids) == 1:
            eligible_promo_name = await promo_service.get_promo_name_service(success_promo_ids[0])
            failed_promo_name = await promo_service.get_promo_name_service(failure_promo_ids[0])
            message = (
                f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {eligible_promo_name}. "
                f"and failed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {failed_promo_name}."
            )
            identifier = f"{eligible_promo_name}."
        elif len(success_promo_ids) == 1:
            promo_name = await promo_service.get_promo_name_service(success_promo_ids[0])
            message = (
                f"Execution completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}. "
                f"and failed for {len(failure_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."
            )
            identifier = promo_name
        elif len(failure_promo_ids) == 1:
            promo_name = await promo_service.get_promo_name_service(failure_promo_ids[0])
            message = (
                f"Execution completed for {len(success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s). "
                f"and failed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
            )
            identifier = f"{len(success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."
        else:
            message = (
                f"Execution completed for {len(success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s). "
                f"and failed for {len(failure_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."
            )
            identifier = f"{len(success_promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."


    await insert_notification(
        request=request_payload,
        user_id=user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=promo_constants.EXECUTION_APPROVAL,
        message=message,
        promo_id=None,
        promo_ids = success_promo_ids or failure_promo_ids,
        status=status,
        identifier=identifier,
        header_text=NotificationHeaderText.EXECUTION_APPROVAL.value,
    )
    return message

async def save_notification_after_withdraw(
    promo_ids: list[int],
    success: bool,
    user_id: int
):
    total_promos = len(promo_ids)
    # Case 1: All promo IDs are successful
    if success:
        if  total_promos == 1:
            promo_name = await promo_service.get_promo_name_service(promo_ids[0])
            message = f"Withdrawal completed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
            identifier = promo_name
        else:
            identifier = f"{total_promos} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL}."
            message = f"Withdrawal completed for {identifier}"

    else:
        if len(promo_ids) == 1:
            promo_name = await promo_service.get_promo_name_service(promo_ids[0])
            message = (
                f"Withdrawal failed for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {promo_name}."
            )
            identifier = f"{promo_name}."
        else:
            message = (
                f"Withdrawal failed for {len(promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s). "
            )
            identifier = f"{len(promo_ids)} {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}(s)."


    await insert_notification(
        request={},
        user_id=user_id,
        module=NotificationModules.PROMOTIONS.value,
        action=promo_constants.ACTION_WITHDRAW,
        message=message,
        promo_id=None,
        promo_ids = promo_ids,
        status=success,
        identifier=identifier,
        header_text=NotificationHeaderText.WITHDRAWAL.value,
    )
    return message

    

async def get_offer_types_data(request_payload: promo_models.GetOfferTypesByPriority):
    fetch_query = promo_queries.FETCH_VALID_OFFER_TYPES_BY_PRIORITY_QUERY.format(
        promo_id=request_payload.promo_id,
        product_level_ids=get_array_format(request_payload.product_level_ids),
        store_level_ids=get_array_format(request_payload.store_level_ids),
        priority_number=request_payload.priority_number
    )

    return await async_execute_query(fetch_query)


async def get_tier_valid_offer_types_data():
    fetch_query = promo_queries.FETCH_TIER_VALID_OFFER_TYPES.format(
        meta_schema=environment.meta_schema
    )
    print(fetch_query)
    return await async_execute_query(fetch_query)


async def update_scenario_name_data(**kwargs):
    request_data = kwargs["request"]
    user_id = kwargs["user_id"]
    scenario_id = request_data["scenario_id"]
    scenario_name = request_data["scenario_name"]

    update_query = promo_queries.UPDATE_SCENARIO_NAME_QUERY.format(
        promo_schema=environment.promo_schema,
        scenario_id=scenario_id,
        scenario_name=scenario_name,
        user_id=user_id,
    )

    await async_execute_query(update_query)

async def withdraw_promotion_data_v3(
    request_payload: promo_models.WithdrawPromosRequest,
    user_id: int
):
    try:
        await downstream_integration(request_payload.promo_ids, user_id, promo_constants.ACTION_WITHDRAW)
        promo_update_query = promo_queries.WITHDRAW_PROMO_QUERY.format(
            promo_ids=",".join(str(x) for x in request_payload.promo_ids),
            client_schema=environment.client_schema,
            user_id=user_id,
        )
        await async_execute_query(query=promo_update_query)
        await save_notification_after_withdraw(
            promo_ids=request_payload.promo_ids,
            success=True,
            user_id=user_id
        )
    except Exception as e:
        logger.error(f"Error in withdraw_promotion_data_v3: {e}", exc_info=True)
        await save_notification_after_withdraw(
            promo_ids=request_payload.promo_ids,
            success=False,
            user_id=user_id
        )
        raise e

async def delete_promotion_data_v3(
    request_payload: promo_models.DeletePromosRequest,
    user_id: int
):
    promo_update_query = promo_queries.DELETE_PROMOS_AND_ITS_METRICS_QUERY.format(
        promo_ids=",".join(str(x) for x in request_payload.promo_ids),
        client_schema=environment.client_schema,
        user_id=user_id,
    )

    return await async_execute_query(query=promo_update_query)

async def copy_promotion_data(
    copy_promos_request: promo_models.CopyPromosRequest,
    user_id: int
)->dict:
    query = promo_queries.COPY_PROMO_QUERY.format(
        promo_details = get_str_repr(
            json.dumps(
                [i.model_dump(mode='json') for i in copy_promos_request.promos]
            )
        ),
        user_id = user_id
    )
    logger.info(query)

    query_result = (await async_execute_query(query))[0]["result"]
    return query_result

async def get_promo_step0_basics(promo_id):
    query = promo_queries.FETCH_STEP0_BASICS_QUERY.format(promo_id=promo_id)
    data = await async_execute_query(query)
    return data[0] if data else {}

async def get_promo_step1_basics(promo_id):
    # get_config_value
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    product_select_list = promo_utils.get_product_store_hierarchies_select_list(product_hierarchy_config)
    store_select_list = promo_utils.get_product_store_hierarchies_select_list(store_hierarchy_config)
    query = promo_queries.FETCH_STEP1_BASICS_QUERY.format(
        promo_id=promo_id,
        product_select_list=product_select_list,
        store_select_list=store_select_list
        )
    data = await async_execute_query(query)
    return data[0] if data else {}

async def get_promo_step1_details(promo_id):
    client_product_id_key = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.CLIENT_PRODUCT_ID_KEY)
    query = promo_queries.FETCH_STEP1_DETAILS_QUERY.format(
        promo_id=promo_id,
        client_product_id_key=client_product_id_key
    )
    data = await async_execute_query(query)
    return data[0] if data else {}

async def get_promo_step1_exclusions(promo_id):
    query = promo_queries.FETCH_STEP1_EXCLUSIONS_QUERY.format(
        promo_id=promo_id,
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
    )
    data = await async_execute_query(query)
    return data[0] if data else {}

async def get_promo_step2_basics(promo_id):
    query = promo_queries.FETCH_STEP2_BASICS_QUERY.format(promo_id=promo_id)
    data = await async_execute_query(query)
    return data[0] if data else {}

async def fetch_step3_basics_data(promo_id):
    query = promo_queries.FETCH_STEP3_BASICS_QUERY.format(
        promo_id=promo_id,
    )
    print(query)
    data = await async_execute_query(query)
    process_simulation_data(data)
    return data[0] if data else {}

async def get_promo_step3_optimisation_details(promo_id):
    query = promo_queries.FETCH_STEP3_OPTIMISE_QUERY.format(promo_id=promo_id)
    data = await async_execute_query(query)
    return data[0] if data else {}

async def get_promo_step3_simulation_results(request_payload: promo_models.GetPromoStep3SimulationResults):
    query = promo_queries.FETCH_STEP3_SIMULATION_RESULTS_QUERY.format(
        promo_id=request_payload.promo_id,
        target_currency_id=get_str_repr(request_payload.target_currency_id)
    )
    data = await async_execute_query(query)
    return data[0] if data else {}

def process_simulation_data(data):
    if isinstance(data[0].get("simulation"), (list, tuple)) and \
       data[0].get("simulation")[0] is not None and \
       data[0].get("simulation")[0] != "null":
        for item in data[0]["simulation"]:
            item_id = int(item["id"])
            filtered_scenario_data = [
                scenario for scenario in item["scenario_data"]
                if scenario["product_id"] == item_id
                and scenario["scenario_type"] == "resimulation"
                and scenario["action"] == "edit"
            ]
            other_scenario_data = [
                scenario for scenario in item["scenario_data"]
                if scenario["scenario_type"] != "resimulation"
                or scenario["action"] != "edit"
            ]
            item["scenario_data"] = filtered_scenario_data + other_scenario_data

async def get_bmsm_offer_types_data():
    query = promo_queries.FETCH_BMSM_OFFER_TYPES.format(
        promo_schema=environment.promo_schema
    )

    data = await async_execute_query(query)
    # Convert y_value_types to lists
    for row in data:
        y_value_string = row["y_value_types"]
        # Remove the curly braces and split by comma
        y_value_list = y_value_string.strip("{}").split(",")
        row["y_value_types"] = y_value_list
    return data


async def get_tiers_data(**kwargs):
    request_data = kwargs["request"]
    promo_id = request_data["promo_id"]

    query = promo_queries.FETCH_TIERS_INFO.format(
        promo_schema=environment.promo_schema, promo_id=promo_id
    )
    print(query)

    result = await async_execute_query(query)
    return result


async def delete_tier_data(**kwargs):
    request_data = kwargs["request"]
    tier_id = request_data["tier_id"]
    promo_id = request_data["promo_id"]
    tier_name = request_data["tier_name"]
    promo_name = request_data["promo_name"]

    validate_query = promo_queries.VALIDATE_DELETE_TIER.format(
        promo_schema=environment.promo_schema, promo_id=promo_id, tier_id=tier_id
    )

    tier_data = await async_execute_query(validate_query)
    if tier_data:
        _message = f"Tier - {tier_name} can not be deleted since it is a part of simulation for {client_configuration_constants.PROMO_IDENTIFIER_PRIMARY} - {promo_name}"
        _status = status.HTTP_400_BAD_REQUEST

    else:
        delete_query = promo_queries.DELETE_TIER_INFO.format(
            promo_schema=environment.promo_schema, tier_id=tier_id
        )
        await async_execute_query(delete_query)

        _message = "Successful"
        _status = status.HTTP_200_OK

    return _message, _status

async def tier_management_data(
    request_payload: promo_models.TierManagement
):
    values_list_substr = """
        (
        {offer_x_value}::float, {offer_x_type}::varchar, {offer_y_value}::float, {offer_y_type}::varchar,
        {offer_z_value}::float, {offer_z_type}::varchar, (
            select {promo_schema}.get_offer_description(
                0,
                {tier_offer_type},
                {offer_x_value},
                {offer_x_type},
                {offer_y_value},
                {offer_y_type},
                {offer_z_value},
                {tier_id}
            )::text)
        )
    """
    values_list = []
    for data in request_payload.tier_information:
        values_list.append(
            values_list_substr.format(
                offer_x_value=get_str_repr(data.offer_x_value),
                offer_x_type=get_str_repr(data.offer_x_type),
                offer_y_value=get_str_repr(data.offer_y_value),
                offer_y_type=get_str_repr(data.offer_y_type),
                offer_z_value=get_str_repr(data.offer_z_value),
                offer_z_type=get_str_repr(data.offer_z_type),
                tier_offer_type=get_str_repr(request_payload.tier_offer_type),
                tier_id=get_str_repr(request_payload.tier_id),
                promo_schema=environment.promo_schema,
            )
        )
    values_list_main_str = ",\n".join(values_list)

    if not request_payload.tier_id:
        # Create flow
        query = promo_queries.CREATE_TIER_QUERY.format(
            promo_schema=environment.promo_schema,
            promo_id=request_payload.promo_id,
            tier_name=get_str_repr(request_payload.tier_name),
            sub_tier_count=request_payload.sub_tier_count,
            tier_offer_type=get_str_repr(request_payload.tier_offer_type),
            tier_offer_type_id=request_payload.tier_offer_type_id,
            values_clause=values_list_main_str,
        )

    else:
        # Edit flow
        query = promo_queries.EDIT_TIER_QUERY.format(
            promo_schema=environment.promo_schema,
            promo_id=request_payload.promo_id,
            tier_id=request_payload.tier_id,
            tier_name=get_str_repr(request_payload.tier_name),
            sub_tier_count=request_payload.sub_tier_count,
            tier_offer_type=get_str_repr(request_payload.tier_offer_type),
            tier_offer_type_id=request_payload.tier_offer_type_id,
            values_clause=values_list_main_str,
        )
    logger.info(query)
    tier_data = await async_execute_query(query)

    return tier_data[0]["tier_id"]


async def fetch_eligible_promos(promo_id_list):
    promo_id_str = ", ".join(map(str, promo_id_list))
    eligible_promo_query = promo_queries.FETCH_ELIGIBLE_PROMO_QUERY.format(
        promo_schema=environment.promo_schema, promo_id_str=promo_id_str
    )

    eligible_promo_data = await async_execute_query(eligible_promo_query)
    eligible_promo_id_list = [promo["promo_id"] for promo in eligible_promo_data]
    print(f"eligible_promo_id_list: {eligible_promo_id_list}")

    return eligible_promo_id_list


async def resimulate_offers_data(**kwargs):
    guid = kwargs["guid"]
    action_log_id = kwargs["action_log_id"]
    user_id = kwargs["user_id"]
    eligible_promo_id_list = kwargs["eligible_promo_id_list"]

    try:
        promotions = []
        for promo_id in eligible_promo_id_list:
            scenario_id_query = promo_queries.FETCH_SCENARIO_ID_DATA.format(
                promo_schema=environment.promo_schema, promo_id=promo_id
            )
            scenario_id_data = await async_execute_query(scenario_id_query)
            scenario_id_list = scenario_id_data[0]["scenario_id_list"]

            promotion = {"promo_id": promo_id, "scenario_id": scenario_id_list}
            promotions.append(promotion)

        resimulate_opt_payload = {
            "guid": guid,
            "user_id": user_id,
            "action_log_id": action_log_id,
            "source": "marketing_calendar",
            "action": "resimulate",
            "promotions": promotions,
            "parameters": {
                "callback_endpoint": environment.OPT_CALLBACK_ENDPOINT
            }
        }
        return await promo_service.call_resimulation_service(resimulate_opt_payload)

    except Exception as e:
        logger.error(f"Error in resimulate_offers_data: {e}", exc_info=True)
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=eligible_promo_id_list,
            action_log_status=-1,
            is_processing_status=0,
            auto_resimulated_flag=-1,
            source="marketing_calendar",
            user_id=user_id,
        )

        request_object = {
            "guid": guid,
            "user_id": user_id,
            "action_log_id": action_log_id,
            "source": "marketing_calendar",
            "action": "resimulate",
            "success_promo_ids": [],
            "failure_promo_ids": eligible_promo_id_list,
        }

        await promo_utils.call_server_callback(request_object)

        raise RuntimeExecutionException(str(e)) from e


async def edit_promos_data(**kwargs):
    request = kwargs["request"]
    promos = request["promos"]
    update_str_list = []

    for promo in promos:
        # Base update statement
        update_str = f"update {environment.promo_schema}.promo_master set "

        # List to collect update parts
        update_parts = []

        # Add promo_name if it exists
        if promo.get("promo_name"):
            update_parts.append(f"name = {get_str_repr(promo['promo_name'])}")

        # Add comments if they exist
        if "comments" in promo:
            update_parts.append(f"offer_comment = {get_str_repr(promo['comments'])}")

        # Check if there are update parts to add
        if update_parts:
            # Join all update parts with commas
            update_str += ", ".join(update_parts)

            # Add the where clause
            update_str += f" where promo_id = {promo['promo_id']};"

            # Append the constructed update statement to the list
            update_str_list.append(update_str)

    exec_query = "\n".join(update_str_list)

    query = promo_queries.EDIT_PROMOS_QUERY.format(exec_query=exec_query)

    await async_execute_query(query)


async def get_simulation_results_details_data(**kwargs):
    fetch_query = ""
    data = kwargs["request"]

    promo_id = data.get("promo_id")
    aggregation = data.get("aggregation")

    if aggregation == -200:
        base_query = promo_queries.BASE_OVERALL_SIM_RESULTS_DEATILS_QUERY.format(
            promo_id=promo_id,
            promo_schema=environment.promo_schema,
            target_currency_id=get_str_repr(data.get("target_currency_id"))
        )
        fetch_query = promo_queries.FETCH_OVERALL_SIM_RESULTS_DEATILS_QUERY.format(
            base_query=base_query
        )
    else:
        base_query = promo_queries.BASE_DETAILED_SIMULATION_RESULTS_QUERY.format(
            promo_id=promo_id, 
            aggregation=aggregation,
            target_currency_id=get_str_repr(data.get("target_currency_id"))
        )
        base_query_res = await async_execute_query(base_query)
        base_query = base_query_res[0].get(
            "fn_get_detailed_simulation_results_base_query"
        )
        fetch_query = promo_queries.FETCH_DETAILED_SIMULATION_RESULTS_QUERY.format(
            base_query=base_query
        )

    print(fetch_query)
    data = await async_execute_query(fetch_query)
    print(data)

    return await promo_utils.format_simulation_results_data(data)


async def get_view_by_data(screen_type, type):
    where_str = ""
    fetch_query = ""
    if type == "promo_status":
        if screen_type == "decision_dashboard":
            where_str = f"where status_id in ({promo_constants.OFFER_STATUS[promo_constants.FINALIZED]}, {promo_constants.OFFER_STATUS[promo_constants.EXECUTION]}, {promo_constants.OFFER_STATUS[promo_constants.PLACEHOLDER]})"
        elif screen_type in ["calendar_view", "workbench"]:
            where_str = ""

        fetch_query = f"""
            select
                status_name as label,
                status_id as value
            from price_promo.promo_status_config
            {where_str}
        """
    elif (type == "simulation_results_details" and screen_type == "simulation"):
        fetch_query = """
            select
                discount_level_value as label,
                discount_level_id as value
            from price_promo.discount_level_config
            where
                id_key not in ('pg_id')
                and category = 'product'
                and discount_level_id>=0
        """

    print(fetch_query)
    return await async_execute_query(query=fetch_query)


async def update_is_processing_data(**kwargs):
    promo_id_list = kwargs["promo_id_list"]
    processing_flag = kwargs["processing_flag"]
    update_query = promo_queries.UPDATE_IS_PROCESSING_QUERY.format(
        promo_schema=environment.promo_schema,
        processing_flag=processing_flag,
        promo_id_str=get_array_format(promo_id_list),
    )
    # print("-----")
    # print(update_query)
    return await async_execute_query(update_query)


async def update_is_auto_resimulated_data(**kwargs):
    promo_id_list = kwargs["promo_id_list"]
    auto_resimulated_flag = kwargs["auto_resimulated_flag"]
    update_query = promo_queries.UPDATE_IS_AUTO_RESIMULATED_QUERY.format(
        promo_schema=environment.promo_schema,
        auto_resimulated_flag=auto_resimulated_flag,
        promo_id_str=get_array_format(promo_id_list),
    )

    await async_execute_query(update_query)


async def insert_action_log_data(**kwargs):
    action_name = kwargs["action_name"]
    screen_type = kwargs["screen_type"]
    promo_id_list = kwargs["promo_id_list"]
    status_key = kwargs["status"]
    end_point = kwargs["end_point"]
    payload = kwargs["payload"]
    user_id = kwargs["user_id"]

    promo_id_str = get_array_format(promo_id_list)

    insert_query = promo_queries.INSERT_ACTION_LOG.format(
        promo_schema=environment.promo_schema,
        action_name=action_name,
        screen_type=screen_type,
        promo_id_str=promo_id_str,
        status=status_key,
        end_point=end_point,
        payload=get_str_repr(payload),
        user_id=user_id,
    )

    action_log_id = await async_execute_query(insert_query)
    return action_log_id[0]["action_log_id"]


async def update_action_log_data(**kwargs):
    action_log_id = kwargs["action_log_id"]
    status_key = kwargs["status"]
    user_id = kwargs["user_id"]

    update_query = promo_queries.UPDATE_ACTION_LOG_QUERY.format(
        promo_schema=environment.promo_schema,
        action_log_id=action_log_id,
        status=status_key,
        user_id=user_id,
    )

    await async_execute_query(update_query)


async def get_priority_number_data(request_payload: promo_models.GetPriorityNumber):
    fetch_query = promo_queries.FETCH_PRIORITY_NUMBER_QUERY.format(
        promo_id = request_payload.promo_id,
        product_level_ids = get_array_format(request_payload.product_level_ids),
        store_level_ids = get_array_format(request_payload.store_level_ids)
    )

    return await async_execute_query(fetch_query)


async def get_exclusions_data_from_file(file: UploadFile, exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType]):
    if file.filename.endswith(".csv"):
        df = pd.read_csv(file.file, dtype=str)
    elif file.filename.endswith(".xlsx"):
        df = pd.read_excel(file.file, dtype=str)
    else:
        raise UnknownFileException

    await validate_exclusion_uploaded_file(df, exclusion_upload_configuration)
    return df


async def validate_exclusion_uploaded_file(df: pd.DataFrame, exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType]):
    df.columns = [col.strip().lower() for col in df.columns]

    missing_cols = set(exclusion_upload_configuration.keys()) - set(df.columns)
    additional_cols = set(df.columns) - set(exclusion_upload_configuration.keys())

    if len(missing_cols) or len(additional_cols):
        raise InvalidTemplateException

    if df.empty:
        raise EmptyExclusionUploadException


async def validate_exclusion_upload_data(upload_data_df: pd.DataFrame, product_exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType]):

    # Initial Data Preparation
    upload_data_df = upload_data_df.replace({np.nan: None})
    # upload_data_df.columns = upload_data_df.columns.str.strip().str.replace(" ", "_")
    sku_coloumn = promo_utils.return_sku_column_name_in_excel_data(upload_data_df, product_exclusion_upload_configuration)
    # Separate SKU and non-SKU rows
    sku_rows_df = upload_data_df[upload_data_df[sku_coloumn].notnull()]
    non_sku_rows_df = upload_data_df[upload_data_df[sku_coloumn].isnull()]
    product_ids_list = [str(i) for i in sku_rows_df[sku_coloumn].to_list()]
    non_sku_upload_data_dict = non_sku_rows_df.to_dict(orient="records")

    # Prepare storage containers for results
    invalid_skus, valid_combinations, invalid_combinations, valid_display_combinations = [], [], [], []
    # Validate SKUs
    sku_exclusion_upload_config_info = product_exclusion_upload_configuration.get(sku_coloumn.lower().strip(), {}) if sku_coloumn else None

    await validate_skus(product_ids_list, invalid_skus, valid_combinations, invalid_combinations, sku_exclusion_upload_config_info)

    # Validate Non-SKU Hierarchies
    for data in non_sku_upload_data_dict:
        is_valid_data, display_data = await validate_hierarchy_data(data, product_exclusion_upload_configuration)
        
        if is_valid_data:
            valid_display_combinations.append(display_data)
            valid_combinations.extend(is_valid_data)
        else:
            invalid_combinations.append(data)

    # Return formatted results
    return {
        "valid": valid_combinations,
        "invalid": invalid_combinations,
        "valid_display_combinations": valid_display_combinations,
    }


async def validate_skus(product_ids_list, invalid_skus, valid_combinations, invalid_combinations, sku_exclusion_upload_config_info={}):
    """Check SKU validity and update results lists."""
    if not product_ids_list:
        return

    # Run SKU validity query
    valid_sku_check_query = promo_queries.EXCLUSION_CHECK_VALID_SKU_QUERY.format(
        promo_schema=environment.promo_schema,
        product_ids_list=",".join(f"'{pid}'" for pid in product_ids_list),
        id_column = sku_exclusion_upload_config_info.get("id_column", ""),
        cid_column = sku_exclusion_upload_config_info.get("cid_column", ""),
        value_column = sku_exclusion_upload_config_info.get("value_column", ""),
    )
    logger.info(f"Exclusion upload SKU validity check query ----  {valid_sku_check_query}")
    valid_sku_check_data = await async_execute_query(query=valid_sku_check_query)

    # Parse results
    for sku_data in valid_sku_check_data:
        if sku_data["data"]:
            (valid_combinations if sku_data["is_valid"] else invalid_combinations).extend(sku_data["data"])
            if not sku_data["is_valid"]:
                invalid_skus.extend([data[promo_constants.EXCLUSION_SKU_ID_COLUMN] for data in sku_data["data"]])


async def validate_hierarchy_data(data, exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType]):
    """Validate a single row of hierarchy data and return results if valid."""
    select_list, join_list, main_select_list, coalesce_list = [], [], [], []
    # Map columns for each hierarchy level
    hierarchy_levels = [(key, value["id_column"], f"A.{value["id_column"]} = B.{value["id_column"]}::text",
                         value["cid_column"], value["value_column"]) for key, value in exclusion_upload_configuration.items()]

    for col, key, join_cond, cid, cuq in hierarchy_levels:
        add_hierarchy_data(data, col, key, join_cond, cid, cuq, select_list, join_list, main_select_list, coalesce_list)

    if not select_list:
        return None, None

    # Format and execute query
    select_str, main_select_str, join_str = ",".join(select_list), ",".join(main_select_list), " AND ".join(join_list)
    valid_hierarchy_check_query = promo_queries.EXCLUSION_CHECK_HIERARCHY_VALIDITY_QUERY.format(
        promo_schema=environment.promo_schema, select_str=select_str, main_select_str=main_select_str, join_str=join_str
    )
    
    logger.info(f"Exclusion upload Hierarchy validity check query ---- {valid_hierarchy_check_query}")
    is_valid_check_data = await async_execute_query(query=valid_hierarchy_check_query)
    # Return validity data
    return (is_valid_check_data if all(value is not None for value in is_valid_check_data[0].values()) else None), is_valid_check_data[
        0] if is_valid_check_data else None


def add_hierarchy_data(data, col, key, join_cond, cid, cuq, select_list, join_list, main_select_list, coalesce_list):
    """Append hierarchy-related data for query generation."""
    
    data = {k.lower(): v for k, v in data.items()}
    if col in data and data[col]:
        up_value = data[col]
        select_list.append(f" '{up_value}' as {key} ")
        join_list.append(join_cond)
        main_select_list.append(f"coalesce(B.{key}::text, A.{key}) as {key}, B.{cid}, B.{cuq}")
        coalesce_list.append(f"B.{cid}")

async def is_valid_to_finalize(request_data: promo_models.ToFinalizeModel):
    fetch_query = promo_queries.GET_CONFLICTED_PROMOS.format(
        promo_schema=environment.promo_schema, promo_ids=request_data.promo_ids
    )
    return await async_execute_query(fetch_query)


async def get_exmd_template_id_data():
    fetch_query = promo_queries.FETCH_EXMD_TEMPLATE_ID_QUERY.format(
        promo_schema=environment.promo_schema
    )
    return await async_execute_query(fetch_query)


async def get_exmd_price_filter_data(**kwargs):
    promo_id = kwargs["promo_id"]
    fetch_query = promo_queries.FETCH_EXMD_PRICE_FILTER_QUERY.format(
        promo_schema=environment.promo_schema, promo_id=promo_id
    )
    print(fetch_query)
    data = await async_execute_query(fetch_query)
    return data


async def get_exmd_target_folder_data(**kwargs):
    promo_id = kwargs["promo_id"]
    fetch_query = promo_queries.FETCH_EXMD_TARGET_FOLDER_QUERY.format(
        promo_schema=environment.promo_schema, promo_id=promo_id
    )
    return await async_execute_query(fetch_query)


async def get_exmd_sfcc_ats_check_data():
    fetch_query = promo_queries.FETCH_EXMD_SFCC_ATS_CHECK_QUERY.format(
        promo_schema=environment.promo_schema
    )
    return await async_execute_query(fetch_query)


async def get_exmd_sfcc_dropship_options_data():
    fetch_query = promo_queries.FETCH_EXMD_SFCC_DROPSHIP_OPTIONS_QUERY.format(
        promo_schema=environment.promo_schema
    )
    return await async_execute_query(fetch_query)


async def save_exmd_data(**kwargs):
    request_data = kwargs["request_data"]
    promo_id = request_data["promo_id"]
    template_id = request_data["template_id"]
    price_filter_id = request_data["price_filter_id"]
    folder_id = request_data["folder_id"]
    sfcc_ats_check_id = request_data["sfcc_ats_check_id"]
    sfcc_dropship_id = request_data["sfcc_dropship_id"]
    promo_code = request_data["promo_code"]
    receipt_text_eng = request_data["receipt_text_eng"]
    receipt_text_french = request_data["receipt_text_french"]
    sfcc_pip_text = request_data["sfcc_pip_text"]
    sfcc_tender_type_promo_msg = request_data["sfcc_tender_type_promo_msg"]
    sfcc_pip_customer_group = request_data["sfcc_pip_customer_group"]
    sfcc_customer_group = request_data["sfcc_customer_group"]
    sfcc_pip_rank = request_data["sfcc_pip_rank"]
    sfcc_rank = request_data["sfcc_rank"]

    save_query = promo_queries.SAVE_EXMD_QUERY.format(
        promo_schema=environment.promo_schema,
        promo_id=promo_id,
        template_id=get_str_repr(template_id),
        price_filter_id=get_str_repr(price_filter_id),
        folder_id=get_str_repr(folder_id),
        sfcc_ats_check_id=get_str_repr(sfcc_ats_check_id),
        sfcc_dropship_id=get_str_repr(sfcc_dropship_id),
        promo_code=get_str_repr(promo_code),
        receipt_text_eng=get_str_repr(receipt_text_eng),
        receipt_text_french=get_str_repr(receipt_text_french),
        sfcc_pip_text=get_str_repr(sfcc_pip_text),
        sfcc_tender_type_promo_msg=get_str_repr(sfcc_tender_type_promo_msg),
        sfcc_pip_customer_group=get_str_repr(sfcc_pip_customer_group),
        sfcc_customer_group=get_str_repr(sfcc_customer_group),
        sfcc_pip_rank=get_str_repr(sfcc_pip_rank),
        sfcc_rank=get_str_repr(sfcc_rank),
    )

    await async_execute_query(save_query)


async def get_exmd_promo_data(**kwargs):
    promo_id = kwargs["promo_id"]
    fetch_query = promo_queries.FETCH_EXMD_QUERY.format(
        promo_schema=environment.promo_schema, promo_id=promo_id
    )
    return await async_execute_query(fetch_query)


async def get_promo_id_by_scenario_id_data(scenario_id):
    query = f"""
        SELECT
            promo_id
        FROM price_promo.scenario_master
        WHERE scenario_id = {scenario_id}
    """
    promo_id_res = await async_execute_query(query)

    # Check if the result is not a valid list or is empty
    if not isinstance(promo_id_res, list) or not promo_id_res:
        raise CommonException(f"No Promo ID exists for scenario {scenario_id}")

    return promo_id_res[0]["promo_id"]


async def get_promo_name_data(promo_id):
    query = f"""
        select
            name as promo_name
        from price_promo.promo_master
        where promo_id = {promo_id}
    """

    promo_name_res = await async_execute_query(query)

    # Check if the result is not a valid list or is empty
    if not isinstance(promo_name_res, list) or not promo_name_res:
        raise CommonException(f"No Promo Name exists for promo {promo_id}")

    return promo_name_res[0]["promo_name"]


async def check_exmd_data(promo_ids: list[int]):
    check_query = promo_queries.CHECK_EXMD_QUERY.format(
        promo_schema=environment.promo_schema,
        promo_id_list=get_str_repr(promo_ids)
    )
    return await async_execute_query(check_query)


async def get_previously_synced_promo_id_data(promo_id_list):
    promo_id_str = ",".join(str(promo_id) for promo_id in promo_id_list)
    fetch_query = promo_queries.FETCH_PREVIOUSLY_SYNCED_PROMO_ID_QUERY.format(
        promo_schema=environment.promo_schema, promo_id_list=promo_id_str
    )
    print(fetch_query)
    promo_ids = await async_execute_query(fetch_query)
    return promo_ids[0]["promo_ids"] if promo_ids else []


async def override_forecast_for_a_promo_data(
    override_forecast_request: promo_models.OverrideForecast, user_id: int
) -> list[int]:
    query = promo_queries.OVERRIDE_FORECAST_FOR_A_PROMO.format(
        promo_id=override_forecast_request.promo_id,
        scenario_id=get_str_repr(override_forecast_request.scenario_id),
        new_sales_units=get_str_repr(override_forecast_request.new_sales_units),
        new_baseline_sales_units=get_str_repr(
            override_forecast_request.new_baseline_sales_units
        ),
        old_sales_units=override_forecast_request.old_sales_units,
        old_baseline_sales_units=override_forecast_request.old_baseline_sales_units,
        reason=get_str_repr(override_forecast_request.reason),
        override_method=get_str_repr(override_forecast_request.override_method),
        comment=get_str_repr(override_forecast_request.comment),
        user_id=user_id,
        from_stacking_view = get_str_repr(override_forecast_request.from_stacking_view)
    )
    print(query)
    data = await async_execute_query(query)
    return data[0]["fn_override_forecast_for_a_promo"]


async def get_override_forecast_for_a_promo_data(
    override_forecast_request: promo_models.OverrideForecast,
) -> dict:
    query = promo_queries.GET_OVERRIDE_FORECAST_FOR_A_PROMO.format(
        promo_id=override_forecast_request.promo_id,
        scenario_id=get_str_repr(override_forecast_request.scenario_id),
        new_sales_units=get_str_repr(override_forecast_request.new_sales_units),
        new_baseline_sales_units=get_str_repr(
            override_forecast_request.new_baseline_sales_units
        ),
        old_sales_units=override_forecast_request.old_sales_units,
        old_baseline_sales_units=override_forecast_request.old_baseline_sales_units,
        override_method=get_str_repr(override_forecast_request.override_method),
        from_stacking_view=get_str_repr(override_forecast_request.from_stacking_view),
        target_currency_id=get_str_repr(override_forecast_request.target_currency_id)
    )
    print(query)
    data = await async_execute_query(query)

    return data[0]["override_forecast"] if data else {"overridden": {}, "original": {}}


async def get_override_reason_data():
    query = promo_queries.FETCH_OVERRIDE_REASON.format(
        promo_schema=environment.promo_schema
    )

    return await async_execute_query(query)

async def set_default_for_scenario(set_default_request: promo_models.SetDefaultForScenario, user_id: int):
    query = promo_queries.SET_DEFAULT_FOR_SCENARIO.format(
        promo_id=set_default_request.promo_id,
        scenario_id=set_default_request.scenario_id,
        default=get_str_repr(set_default_request.default),
        user_id=user_id,
        from_stacking_view = get_str_repr(set_default_request.from_stacking_view)
    )
    print(query)
    await async_execute_query(query)

async def get_promo_stacked_offers(promo_id: int):
    query = promo_queries.GET_PROMO_STACKED_OFFERS.format(
        promo_id=promo_id
    )
    data = await async_execute_query(query)
    return (data[0]["fn_get_promo_stacked_offers"] or {}) if data else {}

async def is_finalised_or_execution_approved_promos_present(promo_ids: list[int]) -> bool:
    query = promo_queries.IS_FINALISED_OR_EXECUTION_APPROVED_PROMO_PRESENT.format(
        promo_ids=",".join(str(x) for x in promo_ids),
        promo_schema=environment.promo_schema,
    )
    data = await async_execute_query(query)
    return data[0]["is_finalised_or_execution_approved_promo_present"]

async def update_strategy_disable_simulation_flag(promo_id:int ,is_simulation_disabled: bool):
    query = promo_queries.UPDATE_STRATEGY_SIMULATION_FLAG.format(
        promo_schema=environment.promo_schema,
        promo_id = promo_id,
        is_simulation_disabled=get_str_repr(is_simulation_disabled)
    )
    await async_execute_query(query)

async def check_if_step3_is_disabled(promo_id: int):
    query = promo_queries.CHECK_IF_STEP3_IS_DISABLED.format(
        promo_schema=environment.promo_schema,
        promo_id=promo_id
    )
    data = await async_execute_query(query)
    return data[0]["is_step3_disabled"]

async def get_stacked_offers_of_promo(promo_id: int)->list[int]:
    query = promo_queries.GET_STACKED_OFFERS_OF_PROMO.format(
        promo_id=promo_id
    )
    data = await async_execute_query(query)
    return data[0]["stacked_offers"] if data else []

async def create_placeholder_promo_v3(
    request_payload: promo_models.CreatePlaceholderPromoRequest,
    user_id: int
):
    query = promo_queries.CREATE_PLACEHOLDER_PROMO.format(
        promo_name = get_str_repr(request_payload.promo_name),
        start_date = get_str_repr(request_payload.start_date),
        end_date = get_str_repr(request_payload.end_date),
        metrics = get_str_repr(request_payload.metrics.model_dump()),
        event_id = request_payload.event_id,
        user_id = user_id
    )
    print(query)
    data = await async_execute_query(query)

    return data[0]

async def update_placeholder_promo_v3(
    request_payload: promo_models.UpdatePlaceholderPromoRequest,
    user_id: int
):
    query = promo_queries.UPDATE_PLACEHOLDER_PROMO.format(
        promo_id = request_payload.promo_id,
        promo_name = get_str_repr(request_payload.promo_name),
        start_date = get_str_repr(request_payload.start_date),
        end_date = get_str_repr(request_payload.end_date),
        metrics = get_str_repr(request_payload.metrics.model_dump()),
        event_id = request_payload.event_id,
        user_id = user_id
    )
    print(query)
    data = await async_execute_query(query)

    return data[0]


async def update_promo_step1_data(
    request_payload: promo_models.UpdatePromoStep1Request,
    user_id: int
):
    step_count = -1
    promo_id = request_payload.promo_id

    fetch_discount_level_query = promo_queries.FETCH_DISCOUNT_RULES.format(
        promo_schema=environment.promo_schema, promo_id=request_payload.promo_id
    )
    discount_level_respone = await async_execute_query(query=fetch_discount_level_query)
    discount_level = discount_level_respone[0] if discount_level_respone else {}

    delete_query = ""
    update_promo_details_query = ""

    if (
        discount_level.get("product_discount_level") == [promo_constants.OVERALL] 
        and 
        discount_level.get("store_discount_level") == [promo_constants.OVERALL]
    ):
        update_promo_details_query = (
            edit_promo_queries.STEP1_UPDATE_PROMO_DETAILS_QUERY.format(
                promo_status=0,
                last_approved_scenario_id="NULL",
                recommendation_type_id="NULL",
                user_id=user_id,
                promo_id=promo_id,
            )
        )

        step_count_query = f"select step_count from price_promo.promo_master where promo_id = {promo_id}"
        step_count_respone = await async_execute_query(query=step_count_query)
        if step_count_respone:
            step_count = step_count_respone[0]["step_count"]
            request_payload.step_count = step_count

    else:
        table_list = [
            "ps_rules",
            "scenario_master",
            "ps_scenario_discounts",
        ]
        for table in table_list:
            delete_query += (
                f"Delete from price_promo.{table} tb where tb.promo_id = {promo_id};"
            )

        update_promo_details_query = (
            edit_promo_queries.STEP1_UPDATE_PROMO_DETAILS_QUERY.format(
                promo_id=promo_id,
                promo_status=0,
                last_approved_scenario_id="NULL",
                recommendation_type_id="NULL",
                user_id=user_id,
            )
        )

    table_list = [
        f"promo_product_{promo_id}",
        "promo_product_hierarchy",
        f"promo_store_{promo_id}",
        "promo_store_hierarchy",
        "tb_promo_store_groups",
        "promo_store_sg_hierarchy",
        "excluded_hierarchy_combination",
        "excluded_product_groups",
        f"excluded_products_{promo_id}",
        f"included_products_{promo_id}",
        "included_product_hierarchy",
        "included_promo_product_groups",
        "included_promo_pg_hierarchy",
    ]

    for table in table_list:
        delete_query += (
            f" Delete from price_promo.{table} tb where tb.promo_id = {promo_id};"
            if str(promo_id) not in table
            else f" Delete from price_promo.{table} tb;"
        )

    final_query = f"""
        {delete_query}
        perform price_promo.fn_delete_promo_override_forecast({promo_id});

        _current_promo_status = (select status from price_promo.promo_master pm where pm.promo_id = {promo_id});

        if not _current_promo_status = any(array(select remarks::int2[] from metaschema.tb_app_sub_master where name = 'stacked_offers_eligibility')) then
            call price_promo_opt.pc_delete_promo_metrics(array[{promo_id}], NULL::integer[], 1, 1, 1);
        else
            call price_promo_opt.pc_delete_agg_promo_metrics(array[{promo_id}], NULL::integer[], 1, 1, 1);
        end if;

        {update_promo_details_query}
    """
    logger.info(final_query)

    await save_step1_details_data_v3(
        request_payload,
        user_id,
        extra_query=final_query
    )

async def update_promo_step0_data(
    request_payload: promo_models.UpdatePromoStep0Request,
    user_id: int
):

    final_query = edit_promo_queries.STEP0_EDIT_PROMO_DETAILS_QUERY.format(
        promo_id=request_payload.promo_id,
        promo_name=get_str_repr(request_payload.promo_name),
        promo_start_date=get_str_repr(request_payload.start_date),
        promo_end_date=get_str_repr(request_payload.end_date),
        event_id = request_payload.event_id,
        promo_status=promo_constants.PROMO_STATUS[promo_constants.DRAFT],
        last_approved_scenario_id="NULL",
        recommendation_type_id="NULL",
        user_id=user_id,
        delete_query="",
        update_promo_details_query=edit_promo_queries.UPDATE_PROMO_DEATILS_QUERY,
        update_promo_metrics_query="",
        insert_statements="",
    )
    print(final_query)
    await async_execute_query(query=final_query)

async def update_promo_step2_data(
    request_payload: promo_models.UpdatePromoStep2Request,
    user_id: int
):
    await save_discount_rules_data_v3(
        request_payload, user_id
    )

async def approve_scenario_v3(
    request_payload: promo_models.ApproveScenarioRequest,
    user_id: int
):
    recommendation_type_id = promo_constants.RECOMMENDATION_TYPE_IDS.get(
        request_payload.source,-1
    )

    update_query = promo_queries.APPROVE_SCENARIO_QUERY.format(
        promo_id=request_payload.promo_id,
        scenario_id=request_payload.scenario_id,
        offer_comment=get_str_repr(request_payload.offer_comment),
        recommendation_type_id=recommendation_type_id,
        user_id=user_id
    )
    print(update_query)
    await async_execute_query(update_query)

async def delete_data_on_reoptimisation(
    promo_id: int
):
    query = promo_queries.REOPTIMISATION_PRE_PROCESS.format(
        promo_id = promo_id
    )
    print(query)
    await async_execute_query(query)


async def update_promo_status_and_refresh_stacked_promos_mapping(
    promo_ids: list[int]
):
    query = promo_queries.UPDATE_PROMO_STATUS_AND_REFRESH_STACKED_PROMOS_MAPPING.format(
        status=promo_constants.OFFER_STATUS[promo_constants.FINALIZED],
        promo_ids=get_array_format(promo_ids)
    )
    await async_execute_query(query=query)

async def get_promotions(
    request_payload: promo_models.GetPromos
):
    finalized_condition = (
        f"and status in ({PromoStatusEnum.FINALIZED.value},{PromoStatusEnum.EXECUTION_APPROVED.value},{PromoStatusEnum.PLACEHOLDER.value})" 
        if request_payload.is_finalized 
        else 
        ""
    )

    query = promo_queries.FILTERED_PROMOTIONS.format(
        start_date = get_str_repr(request_payload.start_date),
        end_date = get_str_repr(request_payload.end_date),
        finalized_condition = finalized_condition,
        product_hierarchies = get_str_repr(request_payload.product_hierarchies),
        store_hierarchies = get_str_repr(request_payload.store_hierarchies),
        event_ids = get_array_format(request_payload.event_ids),
        show_partially_overlapping_events = get_str_repr(request_payload.show_partially_overlapping_events),
    )
    logger.info(query)
    return await async_execute_query(query)

async def validate_copy_offers(
    request_payload: promo_models.ValidateCopyOffers
):
    request_obj = request_payload.offer_event_mappings
    offer_event_mappings = json.dumps([d.model_dump() for d in request_obj])
    query = promo_queries.VALIDATE_COPY_OFFER_QUERY.format(
        request_obj = get_str_repr(offer_event_mappings)
    )
    logger.info(query)
    data = await async_execute_query(query)
    data = promo_utils.format_validation_data(data)
    return data

async def get_vendor_funding_types():
    query = promo_queries.GET_VENDOR_FUNDING_TYPES
    data = await async_execute_query(query)
    return data[0] if data else {"vendor_funding_types": []}


async def get_special_offer_types():
    query = promo_queries.GET_SPECIAL_OFFERS_LIST_QUERY
    return await async_execute_query(query)


async def get_special_offer_type_details(offer_identifier: str):
    query = promo_queries.GET_SPECIAL_OFFER_DETAILS_QUERY.format(
        offer_identifier = get_str_repr(offer_identifier)
    )
    result = await async_execute_query(query)
    return result[0] if result else None

async def get_promo_discounts_table_metadata(
    promo_id: int
):
    query = promo_queries.GET_PROMO_DISCOUNTS_TABLE_METADATA.format(
        promo_id=promo_id
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data[0]["response"] if data else {}

async def get_promo_discounts(
    request_payload: promo_models.GetPromoDiscounts,
    user_id: int
):
    query = promo_queries.GET_PROMO_DISCOUNTS.format(
        promo_id=request_payload.promo_id,
        page=request_payload.page,
        limit=request_payload.limit,
        sort_key=get_str_repr(request_payload.sort_key),
        sort_order=get_str_repr(request_payload.sort_order),
        filters=get_json_format([i.model_dump() for i in request_payload.filters]),
        include_temporary_saved_changes=get_str_repr(request_payload.include_temporary_saved_changes),
        user_id=user_id
    )
    logger.info(query)
    return await async_execute_query(query)

async def get_tier_data(
    request_payload: promo_models.GetPromoDiscounts
):
    query = promo_queries.GET_TIER_DATA.format(
        promo_id=request_payload.promo_id
    )
    data = await async_execute_query(query)
    return data[0]["tier_data"] if data else {}

async def get_scenario_data(
    request_payload: promo_models.GetPromoDiscounts
):
    query = promo_queries.GET_SCENARIO_DATA.format(
        promo_id=request_payload.promo_id
    )
    data = await async_execute_query(query)
    return data[0]["scenario_data"] if data else {}

async def get_new_scenario_id():
    query = promo_queries.GET_NEW_SCENARIO_ID
    return (await async_execute_query(query))[0]

def get_scenarios_insertion_query(
    promo_id: int,
    new_scenario_data: list[promo_models.NewScenarioDetails],
    user_id: int
):
    values_list = []
    for scenario in new_scenario_data:
        values_list.append(
            f"""
                (
                {promo_id},{scenario.scenario_id},{get_str_repr(scenario.scenario_name)},
                {scenario.scenario_order_id},{user_id},now() at time zone 'UTC'
                )
            """
        )
    query = promo_queries.CREATE_NEW_SCENARIOS_DATA.format(
        values_list = ",".join(values_list)
    )
    return query

async def validate_missing_discount_values(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int
):
    query = promo_queries.VALIDATE_MISSING_DISCOUNT_VALUES.format(
        promo_id=request_payload.promo_id,
        user_id=user_id,
        include_temporary_saved_changes=get_str_repr(request_payload.include_temporary_saved_changes),
        new_scenario_order_ids=get_array_format([i.scenario_order_id for i in request_payload.new_scenario_details]),
        row_ids_to_skip=get_array_format([i.row_id for i in request_payload.discounts_data])
    )
    logger.info(query)
    has_missing_discount_values = (await async_execute_query(query))[0]["has_missing_discount_values"]
    if has_missing_discount_values:
        raise promo_exceptions.MissingDiscountValuesException

async def validate_upto_discount_values(new_data: dict, old_data: dict = {}) -> bool:
    all_scenario_ids = set(new_data) | set(old_data)
    for scenario_id in all_scenario_ids:
        new_scenario = new_data.get(scenario_id)
        old_scenario = old_data.get(scenario_id)

        for scenario in (new_scenario, old_scenario):
            if scenario and scenario["contains_upto_offer_type"] and not scenario["all_same"]:
                return False
        if new_scenario and old_scenario:
            if not compare_upto_discount_type(new_scenario, old_scenario):
                return False
    return True

def compare_upto_discount_type(new_scenario: dict, old_scenario: dict) -> bool:
    new_has_upto = new_scenario["contains_upto_offer_type"]
    old_has_upto = old_scenario["contains_upto_offer_type"]
    if not new_has_upto and not old_has_upto:
        return True
    if new_has_upto != old_has_upto:
        return False
    if not (new_scenario["all_same"] or old_scenario["all_same"]):
        return False
    return True

def get_processed_data_for_upto_discount(
    request_payload: promo_models.SimulatePromoRequest
):
    result = {}
    scenario_map = defaultdict(set)
    for row in request_payload.discounts_data:
        for scenario in row._scenario_data.values():
            scenario_map[scenario.scenario_id].add(scenario.offer_type_id)

    for scenario_id, offer_type_ids in scenario_map.items():
        has_upto_percent = OfferTypeEnum.UPTO_X_PERCENT_OFF.id_value in offer_type_ids
        all_same = len(offer_type_ids) == 1
        result[str(scenario_id)] = {
            "contains_upto_offer_type": has_upto_percent,
            "all_same": all_same
        }

    return result

async def get_db_discount_data_for_scenarios(request_payload: promo_models.SimulatePromoRequest,row_ids_to_ignore:list, user_id:int):
    table_to_use = "ps_scenario_discounts"
    if request_payload.include_temporary_saved_changes :
        table_to_use = f"tb_user_promo_temp_bulk_edit_data_{request_payload.promo_id}_{user_id}"
    
    query = promo_queries.CHECK_UPTO_DISCOUNT_OFFERTYPE.format(
        promo_id = request_payload.promo_id,
        table_name = table_to_use,
        row_ids_to_ignore = get_array_format(row_ids_to_ignore),
        offer_type_id = OfferTypeEnum.UPTO_X_PERCENT_OFF.id_value
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def update_discount_values(
    request_payload: promo_models.SimulatePromoRequest,
    user_id: int,
    new_scenarios_insertion_query: str = "",
):
    query = promo_queries.UPDATE_DISCOUNT_VALUES.format(
        promo_id=request_payload.promo_id,
        new_scenarios_insertion_query=new_scenarios_insertion_query,
        discount_values=get_json_format([d.model_dump(mode="json") for d in request_payload.discounts_data]),
        updated_scenario_ids = get_array_format(request_payload._updated_scenario_ids),
        user_id = user_id,
        include_temporary_saved_changes = get_str_repr(request_payload.include_temporary_saved_changes),
        has_discount_values = get_str_repr(bool(request_payload.discounts_data))
    )
    logger.info(query)
    await async_execute_query(query)

async def get_promo_scenario_ids(promo_id: int,only_updated: bool = False) -> list[int]:
    updated_scenario_ids_condition = (
        f"""
            and sm.updated_at >= (select coalesce(last_simulation_time,created_at) from price_promo.promo_master pm where pm.promo_id = {promo_id})
        """
        if only_updated
        else ""
    )
    query = promo_queries.GET_PROMO_SCENARIO_IDS.format(
        promo_id=promo_id,
        updated_scenario_ids_condition=updated_scenario_ids_condition
    )
    data = (await async_execute_query(query))
    return data[0]["scenario_ids"] if data else []

async def bulk_edit_discounts(
    request_payload: promo_models.BulkEditDiscountsRequest,
    user_id: int
):
    query = promo_queries.BULK_EDIT_DISCOUNTS.format(
        promo_id = request_payload.promo_id,
        selected_rows = get_array_format(request_payload.selected_rows),
        unselected_rows = get_array_format(request_payload.unselected_rows),
        filters = get_json_format([i.model_dump() for i in request_payload.filters]),
        bulk_edit_data = get_json_format([i.model_dump() for i in request_payload.bulk_edit_data]),
        discounts_data = get_json_format([i.model_dump(mode="json") for i in request_payload.discounts_data]) if request_payload.discounts_data else "null",
        user_id = user_id,
        session_id = get_str_repr(request_payload.session_id),
        include_temporary_saved_changes = get_str_repr(request_payload.include_temporary_saved_changes)
    )

    logger.info(query)
    await async_execute_query(query,transaction_mode=True)

async def get_promo_offer_type_details(promo_id: int) -> dict:
    query = promo_queries.GET_PROMO_OFFER_TYPE_DETAILS.format(
        promo_id = promo_id
    )
    data = await async_execute_query(query)
    return data[0]

async def get_expected_time(request_payload: promo_models.TimeEstimate, user_id: int):
    if request_payload.action == promo_constants.ACTION_SIMULATE:
        data = await get_expected_gif_time_for_simulation(request_payload, user_id)
    else:
        query = promo_queries.GET_EXPECTED_TIME_FOR_OPTIMIZATION_QUERY.format(
            promo_id=request_payload.promo_id
        )
        data = await async_execute_query(query)
        data = data[0]
        data["discount_points"] = data["discount_points"] if data["discount_points"] != 0 else 1
        data["data_points"] = data["product_count"] * data["store_count"] *  data["promo_duration"] *  data["discount_points"]

    return data


async def get_product_details_of_promo(promo_id: int):
    product_hierarchy_config = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    client_product_id_key = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.CLIENT_PRODUCT_ID_KEY)
    query = promo_queries.GET_PRODUCT_DETAILS_OF_PROMO.format(
        promo_id = promo_id,
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        l0_column = product_hierarchy_config.get("l0_ids",{}).get("value_column","null"),
        l2_column = product_hierarchy_config.get("l2_ids",{}).get("value_column","null"),
        l3_column = product_hierarchy_config.get("l3_ids",{}).get("value_column","null"),
        brand_column = product_hierarchy_config.get("brand",{}).get("value_column","null"),
        client_product_id_key = client_product_id_key
    )
    logger.info(query)
    result = await async_execute_query(query=query)
    return result

async def get_store_details_of_promo(promo_id: int):
    query = promo_queries.GET_STORE_DETAILS_OF_PROMO.format(
        promo_id = promo_id,
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query=query)
    return result

async def get_expected_gif_time_for_simulation(request_payload: promo_models.TimeEstimate, user_id: int):
    if not request_payload.include_temporary_saved_changes:
        product_count, store_count = await fetch_product_and_store_count(request_payload, user_id)
        scenario_count = len({
            scenario.scenario_id
            for discount_data in request_payload.discounts_data 
            for scenario in discount_data.scenario_data
            if scenario.updated is True
        })

        if scenario_count == 0:
            scenario_count = len(await get_scenario_details(request_payload.promo_id))
        
    else:
        query = promo_queries.FETCH_PRODUCT_COUNT_QUERY.format(
            where_str = " ",
            table_name = f"tb_user_promo_temp_bulk_edit_data_{request_payload.promo_id}_{user_id}",
            column_name = "count(product_id) as products_count"
        )
        data = await async_execute_query(query)
        product_count = data[0]["products_count"] if data and data[0]["products_count"] else 0
        query = promo_queries.FETCH_STORE_COUNT_QUERY.format(
            where_str = " ",
            table_name = f"tb_user_promo_temp_bulk_edit_data_{request_payload.promo_id}_{user_id}",
            column_name = "count(store_id) as stores_count"
        )
        data = await async_execute_query(query)
        store_count = data[0]["stores_count"] if data and data[0]["stores_count"] else 0
        query = promo_queries.FETCH_SCENARIO_COUNT_FOR_GIF_DATA_QUERY.format(
            promo_id = request_payload.promo_id,
            user_id = user_id
        )
        data = await async_execute_query(query)
        scenario_count = data[0]["scenario_count"]

        if scenario_count == 0:
            scenario_count = len(await get_scenario_details(request_payload.promo_id))

    query = promo_queries.GET_EXPECTED_TIME_FOR_SIMULATION_QUERY.format(
                promo_id=request_payload.promo_id,
                store_count=store_count,
                product_count=product_count,
                scenario_count=scenario_count
            )
    data = await async_execute_query(query)
    data = data[0]
    data["data_points"] = data["product_count"] * data["store_count"] *  data["promo_duration"] *  data["scenario_count"]
    return data


async def fetch_product_and_store_count(request_payload: promo_models.TimeEstimate, user_id: int):
    row_ids = [i.row_id for i in request_payload.discounts_data]
    if not row_ids:
        row_ids = None
    row_ids = get_array_format(row_ids)
    query = promo_queries.FETCH_PRODUCT_AND_STORE_DISCOUNT_LEVEL_QUERY.format(
        promo_id = request_payload.promo_id
    )
    data = await async_execute_query(query)
    product_discount_level = data[0]["product_discount_level"] if data and data[0]["product_discount_level"] else [-200]
    store_discount_level = data[0]["store_discount_level"] if data and data[0]["store_discount_level"] else [-200]
    if product_discount_level == [-200]:
        query = promo_queries.FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY.format(
            column_name = "products_count",
            promo_id = request_payload.promo_id
        )
    else:
        query = promo_queries.FETCH_PRODUCT_COUNT_QUERY.format(
            table_name = "ps_scenario_discounts",
            column_name = "count(product_id) as products_count",
            where_str = f"where id = any({row_ids})"
        )
    data = await async_execute_query(query)
    product_count = data[0]["products_count"] if data and data[0]["products_count"] else 0
    if store_discount_level == [-200]:
        query = promo_queries.FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY.format(
            column_name = "stores_count",
            promo_id = request_payload.promo_id
        )
    else:
        query = promo_queries.FETCH_STORE_COUNT_QUERY.format(
            table_name = "ps_scenario_discounts",
            column_name = "count(store_id) as stores_count",
            where_str = f"where id = any({row_ids})"
        )
    data = await async_execute_query(query)
    store_count = data[0]["stores_count"] if data and data[0]["stores_count"] else 0
    return product_count, store_count

async def approve_ia_scenario(request_payload: promo_models.ApproveIaScenario, user_id: int):
    query = promo_queries.APPROVE_IA_SCENARIO_QUERY.format(
        promo_id = request_payload.promo_id,
        scenario_id = request_payload.scenario_id,
        user_id = user_id
    )
    await async_execute_query(query)

async def copy_discounts(request_payload: promo_models.CopyDiscounts,user_id: int):
    query = promo_queries.COPY_DISCOUNTS.format(
        promo_id = request_payload.promo_id,
        source = request_payload.source,
        source_scenario_id = request_payload.source_scenario_id,
        target = request_payload.target,
        target_scenario_id = request_payload.target_scenario_id,
        selected_rows = get_array_format(request_payload.selected_rows),
        unselected_rows = get_array_format(request_payload.unselected_rows),
        filters = get_json_format([i.model_dump() for i in request_payload.filters]),
        session_id = get_str_repr(request_payload.session_id),
        include_temporary_saved_changes = get_str_repr(request_payload.include_temporary_saved_changes),
        user_id = user_id,
        promo_schema = environment.promo_schema
    )
    logger.info(query)
    await async_execute_query(query)
    
async def update_promo_step3_data(request_payload: promo_models.SimulatePromoRequest,user_id: int):
    query = promo_queries.STEP3_UPDATE_PROMO_DETAILS_QUERY.format(
        promo_id=request_payload.promo_id, step_count=3, user_id=user_id
    )
    await async_execute_query(query)

async def is_promo_product_discount_sku_level(promo_id: int) -> bool:
    query = promo_queries.IS_PROMO_PRODUCT_DISCOUNT_SKU_LEVEL.format(
        promo_id = promo_id
    )
    data = await async_execute_query(query)
    return data[0]["is_sku_discount_level"]

async def is_promo_store_discount_store_level(promo_id: int) -> bool:
    query = promo_queries.IS_PROMO_STORE_DISCOUNT_STORE_LEVEL.format(
        promo_id = promo_id
    )
    data = await async_execute_query(query)
    return data[0]["is_store_discount_level"]

async def downstream_integration(promo_ids: list[int], user_id: int, action: str):
    # Get current timestamp in client timezone
    print("Inside downstream integration")
    client_tz = pytz.timezone(global_constants.CLIENT_TIMEZONE_FOR_PYTHON)
    current_time = datetime.now(client_tz)
    today = current_time.strftime('%A').lower()  # Day of week (e.g., "tuesday")
    
    simple_offers_query = promo_queries.FETCH_SIMPLE_OFFERS_QUERY.format(
        promo_ids = get_array_format(promo_ids)
    )
    print("Simple offers query", simple_offers_query)
    data = await async_execute_query(simple_offers_query)
    promo_ids = data[0]["promo_ids"]
    
    try:
        if not promo_ids:
            logger.info("No promo ids found to process downstream integration")
            raise promo_exceptions.NoPromoIdsFoundForDownstreamIntegrationException(
                message=f"""
                <p>promo_ids: {promo_ids}</p>
                <p>action: {action}</p>
                <p>error_message: No promo ids found to process downstream integration</p>
                """
            )
        print("Promo ids", promo_ids)
        integration_id = await async_execute_query(promo_queries.LOG_DOWNSTREAM_INTEGRATION_ACTION.format(
            action = get_str_repr(action),
            promo_ids = get_array_format(promo_ids),
            user_id = user_id
        ))
        integration_id = integration_id[0]["integration_id"]
        print("Integration id", integration_id)
   
        simple_offers_downstream_query = promo_queries.SIMPLE_OFFERS_DOWNSTREAM_QUERY.format(
            integration_id = integration_id
        )
        print("Simple offers downstream query", simple_offers_downstream_query)
        res = await async_execute_query(simple_offers_downstream_query)
        insertion_status = res[0] if res else {}
        if not insertion_status["success"]:
            raise promo_exceptions.IntegrationInsertionFailedException(
                message=(
                f"Integration id: {integration_id}\n"
                f"promo_ids: {promo_ids}\n"
                f"action: {action}\n"
                f"error_message: {insertion_status["error_message"]}\n"
                )
            )
    except promo_exceptions.NoPromoIdsFoundForDownstreamIntegrationException as e:
        logger.error(f"Error in downstream_integration: {e}", exc_info=True)
        trace = traceback.format_exc()
        await sched_utils.send_failure_email(today,trace,promo_constants.DOWNSTREAM_INTEGRATION_PROCESS_NAME,None)
        return
        
    except Exception as e:
        print("Failed to process simple offers downstream data.")
        trace = traceback.format_exc()
        await sched_utils.send_failure_email(today,trace,promo_constants.DOWNSTREAM_INTEGRATION_PROCESS_NAME,None)
        raise e
    
    downstream_integration_select_query = promo_queries.DOWNSTREAM_INTEGRATION_SELECT_QUERY.format(
        integration_id = integration_id
    )
    downstream_integration_insert_query = promo_queries.INSERT_DOWNSTREAM_QUERY.format(
        query = get_str_repr(downstream_integration_select_query)
    )
    query_id = await async_execute_query(downstream_integration_insert_query)
    query_id = query_id[0]["id"]
    print("Query id", query_id)
    print("Downstream integration select query", downstream_integration_select_query)
    await promo_utils.downstream_integration_util(
        query_id,
        integration_id,
        promo_ids,
        action,
        user_id
    )
    
async def get_scenario_name(scenario_id: int):
    query = promo_queries.GET_SCENARIO_NAME.format(
        scenario_id = scenario_id
    )
    data = await async_execute_query(query)
    return data[0]["scenario_name"]


async def validate_promos_not_yet_started_with_buffer(promo_ids: list[int], error_message: str):
    """
    Validates that promos have not started yet (considering buffer days).
    Raises an exception if any promo has already started.
    """
    query = promo_queries.GET_PROMOS_NOT_YET_STARTED_QUERY.format(
        promo_ids=get_array_format(promo_ids),
        client_timezone=get_str_repr(global_constants.get_client_timezone()),
        tb_tool_module=ConfigModuleEnum.PROMOTION.value,
        tb_tool_config_name=ConfigKeyEnum.EXECUTION_APPROVE_PROMOS_BUFFER_DAYS.value
    )

    logger.info(f"GET_PROMOS_NOT_YET_STARTED_QUERY: {query}")
    
    result = await async_execute_query(query)
    
    # Get list of promo IDs that haven't started
    not_started_promo_ids = [row['promo_id'] for row in result] if result else []
    
    # If the lists don't match, it means some promos have started
    if set(promo_ids) != set(not_started_promo_ids):
        raise promo_exceptions.PromoAlreadyStartedException(error_message)


async def get_min_max_price_value(request_payload: promo_models.GetMinMaxPriceValue,user_id: int):
    query = promo_queries.GET_MIN_MAX_PRICE_VALUE.format(
        promo_id = request_payload.promo_id,
        selected_rows = get_array_format(request_payload.selected_rows),
        unselected_rows = get_array_format(request_payload.unselected_rows),
        filters = get_json_format([i.model_dump() for i in request_payload.filters]),
        user_id = user_id,
        include_temporary_saved_changes = get_str_repr(request_payload.include_temporary_saved_changes),
    )
    data = await async_execute_query(query)
    return data[0] if data else {}