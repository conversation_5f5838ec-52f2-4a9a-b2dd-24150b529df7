from typing import Optional
from app.dependencies import UserDependency
from configuration.environment import environment
from common_utils.custom_route_handler import CustomRouteH<PERSON>ler
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from exceptions.exceptions import (
    BusinessLogicException,
    RuntimeExecutionException,
)
from fastapi import BackgroundTasks, Depends, Request, UploadFile, status
from fastapi.routing import APIRouter
from pricesmart_common.constants import PROMOTION_API_TAG
from pricesmart_common import utils as common_utils
from promotions import constants as promo_constants
from promotions import models as promo_models
from promotions import service as promo_service
from promotions import utils as promo_utils
from pricesmart_common import constants as global_constants
from client_configuration import constants as client_configuration_constants
from promotions.decorators import log_action_decorator
from logger.logger import logger
from events import service as event_service

promo_router = APIRouter(route_class=CustomRouteHandler,tags = [PROMOTION_API_TAG])
promo_v3_router = APIRouter(tags=[PROMOTION_API_TAG])

@promo_router.post(
    "/upload-exclusion",
    tags=[PROMOTION_API_TAG],
)
async def get_exclusion_from_file(file: UploadFile):
    exclusion_data = await promo_service.get_exclusions_from_file_service(file)
    return common_utils.create_response(data=exclusion_data)

@promo_v3_router.post(
    "/promos"
)
async def get_promos(
    request_payload: promo_models.GetPromos
):
    data = await promo_service.get_promotions(
        request_payload
    )
    return common_utils.create_response(
        data = data
    )

@promo_v3_router.post("/promo")
async def create_promotion(
    create_promo_request: promo_models.CreatePromoRequest,
    user_id: UserDependency,
):
    data = await promo_service.create_promotion_v3(
        create_promo_request,
        user_id
    )

    return common_utils.create_response(
        message=f"{client_configuration_constants.PROMO_IDENTIFIER_STANDARD_PLURAL.capitalize()} successfully created!",
        data=data,
    )

@promo_v3_router.put("/promo/step-0")
async def update_promo_step0_data(
    request_payload: promo_models.UpdatePromoStep0Request,
    user_id: UserDependency
):

    await promo_service.update_promo_step0_data(
        request_payload,
        user_id
    )
    return common_utils.create_response()

@promo_v3_router.post("/promo/step-1")
async def insert_promo_step1_data(
    request_payload: promo_models.InsertPromoStep1Request,
    user_id: UserDependency,
):
    await promo_service.insert_promo_step1_data(
        request_payload,
        user_id
    )

    return common_utils.create_response()

@promo_v3_router.put("/promo/step-1")
async def update_promo_step1_data(
    request_payload: promo_models.UpdatePromoStep1Request,
    user_id: UserDependency
):
    await promo_service.update_promo_step1_data(
        request_payload,
        user_id
    )

    return common_utils.create_response()

@promo_v3_router.put("/promo/step-2")
async def update_promo_step2_data(
    request_payload: promo_models.UpdatePromoStep2Request,
    user_id: UserDependency
):
    await promo_service.update_promo_step2_data(
        request_payload,
        user_id
    )

    return common_utils.create_response()

@promo_v3_router.post("/simulate-promo")
async def simulate_promo(
    simulate_promo_request: promo_models.SimulatePromoRequest,
    user_id: UserDependency,
):
    data = await promo_service.simulate_promo_caller(
        simulate_promo_request,
        user_id
    )

    return common_utils.create_response(data=data)

@promo_router.put("/promo/bulk-edit-discounts")
async def bulk_edit_discounts(
    request_payload: promo_models.BulkEditDiscountsRequest,
    user_id: UserDependency
):
    await promo_service.bulk_edit_discounts(
        request_payload,
        user_id
    )
    return common_utils.create_response()

@promo_router.get("/new-scenario-id")
async def get_new_scenario_id(
):
    data = await promo_service.get_new_scenario_id()
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.put("/reoptimise-promo")
async def reoptimise_promo(
    optimise_promo_request: promo_models.OptimisePromoRequest,
    user_id: UserDependency
):
    await promo_service.reoptimise_promo(
        optimise_promo_request,
        user_id
    )
    return common_utils.create_response()

@promo_v3_router.post("/optimise-promo")
async def optimise_promo(
    optimise_promo_request: promo_models.OptimisePromoRequest,
    user_id: UserDependency
):
    await promo_service.optimise_promo_caller(
        optimise_promo_request,
        user_id
    )
    return common_utils.create_response()


@promo_v3_router.post(
    "/promo/approve-scenario"
)
async def approve_scenario(
    request_payload: promo_models.ApproveScenarioRequest,
    user_id: UserDependency,
    backgound_tasks: BackgroundTasks
):
    await promo_service.validate_event_lock(
        [request_payload.promo_id],
        ConfigModuleEnum.EVENT,
        ConfigKeyEnum.ALLOW_PROMO_APPROVAL_AFTER_EVENT_LOCK,
        f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} under a locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be approved"
    )
    is_finalised_promo = await promo_service.is_finalised_or_execution_approved_promos_present(
        [request_payload.promo_id]
    )
    if is_finalised_promo:
        backgound_tasks.add_task(
            log_action_decorator(
                get_promo_ids_func= lambda i: i.promo_id,
                action_name=promo_constants.ACTION_APPROVE,
                screen_name=promo_constants.SCREEN_STEP3,
                update_task_completion=False
            )(promo_service.approve_scenario_v3),
            request_payload,
            user_id,
            is_finalised_promo=is_finalised_promo
        )
        return common_utils.create_response(
            message = "Approval of the scenario is in progress. You will be notified once the operation is completed",
        )



    await log_action_decorator(
        get_promo_ids_func= lambda i: i.promo_id,
        action_name=promo_constants.ACTION_APPROVE,
        screen_name=promo_constants.SCREEN_STEP3
    )(promo_service.approve_scenario_v3)(
        request_payload,
        user_id,
        is_finalised_promo=is_finalised_promo
    )
    promo_name = await promo_service.get_promo_name_service(request_payload.promo_id)
    return common_utils.create_response(
        message=f"{promo_name} successfully approved!",
    )



@promo_v3_router.post("/placeholder-promo")
async def create_placeholder_promo(
    create_placeholder_promo_request: promo_models.CreatePlaceholderPromoRequest,
    user_id: UserDependency,
):
    data = await promo_service.create_placeholder_promotion_v3(
        create_placeholder_promo_request,
        user_id
    )

    return common_utils.create_response(
        data=data,
        message=f"Placeholder {client_configuration_constants.PROMO_IDENTIFIER_STANDARD} successfully created!",
        status=status.HTTP_200_OK
    )

@promo_v3_router.put("/placeholder-promo")
async def update_placeholder_promo(
    request_payload: promo_models.UpdatePlaceholderPromoRequest,
    user_id: UserDependency
):
    data = await promo_service.update_placeholder_promotion_v3(
        request_payload,
        user_id
    )

    return common_utils.create_response(
        data=data,
        message=f"Placeholder {client_configuration_constants.PROMO_IDENTIFIER_STANDARD} successfully updated!",
        status=status.HTTP_200_OK
    )

@promo_v3_router.post(
    "/finalise-promo"
)
async def finalise_promo(
    finalise_promo_request: promo_models.FinalisePromoRequest,
    user_id: UserDependency,
):
    data = await promo_service.finalise_promo_v3(
        finalise_promo_request,
        user_id
    )

    return common_utils.create_response(
        data=data,
        message=f"Finalisation of {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} are in progress. You will be notified once the operation is completed",
        status=status.HTTP_200_OK
    )

@promo_v3_router.post(
    "/execution-approve-promos"
)
async def execution_approve_promo(
    execution_approve_promo_request: promo_models.ExecutionApprovePromosRequest,
    user_id: UserDependency,
    background_tasks: BackgroundTasks
):
    # Check if promos have started (considering buffer)
    await promo_service.validate_promo_start_date_with_buffer(
        execution_approve_promo_request.promo_ids,
        f"One or more {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} have already started and are no longer eligible for execution."
    )
    
    background_tasks.add_task(
        promo_service.execution_approve_promotion_service_v3,
        execution_approve_promo_request,
        user_id=user_id,
    )
    
    return common_utils.create_response(
        message="Execution approval is in progress. You will be notified once the operation is completed"
    )

@promo_v3_router.post(
    "/withdraw-promos"
)
async def withdraw_promo(
    withdraw_promo_request: promo_models.WithdrawPromosRequest,
    user_id: UserDependency,
    background_tasks: BackgroundTasks
):
    background_tasks.add_task(
        promo_service.withdraw_promotion_service_v3,
        withdraw_promo_request,
        user_id,
    )

    return common_utils.create_response(
        message="Withdrawal is in progress. You will be notified once the operation is completed",
        status=status.HTTP_200_OK
    )

@promo_v3_router.post(
    "/delete-promos"
)
async def delete_promos(
    delete_promos_request: promo_models.DeletePromosRequest,
    user_id: UserDependency,
    background_tasks: BackgroundTasks
):
    non_past_promo_ids = await promo_utils.get_non_past_promos(delete_promos_request.promo_ids)
    if non_past_promo_ids:
        await promo_service.validate_event_lock(
            non_past_promo_ids,
            ConfigModuleEnum.EVENT,
            ConfigKeyEnum.ALLOW_PROMO_DELETE_AFTER_EVENT_LOCK,
            f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} under a locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be deleted"
        )

    message = await promo_service.delete_promos_v3(
        delete_promos_request,
        user_id,
        background_tasks = background_tasks
    )

    return common_utils.create_response(
        message=message
    )


@promo_v3_router.post(
    "/copy-promos"
)
async def copy_promos(
    copy_promos_request: promo_models.CopyPromosRequest,
    user_id: UserDependency
):
    data = await promo_service.copy_promotion_service(
        copy_promos_request,
        user_id
    )
    return common_utils.create_response(
        data={"promo_ids": data},
        message=f"{client_configuration_constants.PROMO_IDENTIFIER_STANDARD.capitalize()} successfully copied!" if data else None,
        status=status.HTTP_200_OK,
        user_id=user_id
    )

def extract_action_name(request_payload_dict):
    simulations = request_payload_dict.get("simulations")
    if simulations:
        return simulations.get("simulator_action")
    return (request_payload_dict.get("simulator_action") or request_payload_dict.get("action"))


@promo_router.post(path="/promo_results", tags=[PROMOTION_API_TAG])
async def promo_results(
    request_payload: promo_models.PromoResults,
):
    data = await promo_service.get_promo_results(
        request_payload=request_payload
    )
    return common_utils.create_response(data=data)


@promo_router.post(path="/valid-discounting-levels", tags=[PROMOTION_API_TAG])
async def valid_discounting_level_service(
    request_payload: promo_models.ValidDiscountingLevel,
):
    data = await promo_service.get_valid_discounting_level_service(
        request_payload=request_payload
    )

    return common_utils.create_response(data=data)


@promo_v3_router.get(
    "/discount-levels",
    tags=[PROMOTION_API_TAG]
)
async def get_discount_levels(
    promo_id: int
):
    data = await promo_service.get_discount_levels_service(promo_id)
    return common_utils.create_response(data=data)



@promo_router.get(path="/promo-status-types", tags=[PROMOTION_API_TAG])
async def get_promo_status_types(
    screen_type: Optional[str] = None,
):
    data = await promo_service.get_promo_status_types_service(screen_type=screen_type)
    return common_utils.create_response(data=data)


@promo_router.post(
    path="/tiles",
    tags=[PROMOTION_API_TAG],
)
async def tiles(request_payload: promo_models.Tiles):

    tiles_details = await promo_service.tiles_service(
        request_payload=request_payload
    )

    return common_utils.create_response(data=tiles_details)


@promo_router.post(
    path="/calculate-gross-margin",
    tags=[PROMOTION_API_TAG],
)
async def calculate_gross_margin(
    request_payload: promo_models.CalculateGrossMargin,
):
    request_payload_dict = request_payload.model_dump()

    # Execute the service operation and retrieve the result
    gross_margin = await promo_service.calculate_gross_margin_service(
        request=request_payload_dict
    )

    return common_utils.create_response(data=gross_margin)

@promo_router.get(path="/valid-offers", tags=[PROMOTION_API_TAG])
async def get_valid_offers(
    params: promo_models.OfferParams = Depends(),
):
    data = await promo_service.get_valid_offers_service(
        promo_id=params.promo_id, simulator_action=params.simulator_action
    )

    return common_utils.create_response(data=data)

@promo_router.get(path="/ly-targets", tags=[PROMOTION_API_TAG])
async def get_promo_ly_and_target_details(
    promo_id: int,
):
    data = await promo_service.get_ly_targets_service(promo_id=promo_id)

    if data is None or not isinstance(data, list) or len(data) == 0:
        return common_utils.create_response(
            message="No data found",
            status=status.HTTP_404_NOT_FOUND
        )

    return common_utils.create_response(data=data[0])


@promo_router.post(path="/valid-offer-types", tags=[PROMOTION_API_TAG])
async def get_valid_offer_types(
    request_payload: promo_models.GetOfferTypesByPriority
):
    data = await promo_service.get_valid_offer_types_service(
        request_payload=request_payload
    )
    return common_utils.create_response(data=data)

@promo_router.get(path="/valid-tier-offer-types", tags=[PROMOTION_API_TAG])
async def get_tier_valid_offer_types():
    data = await promo_service.get_tier_valid_offer_types_service()
    return common_utils.create_response(data=data)


@promo_router.post(path="/update-scenario-name", tags=[PROMOTION_API_TAG])
async def update_scenario_name(
    request_payload: promo_models.UpdateScenarioName,
    user_id: UserDependency,
):
    request_payload_dict = request_payload.model_dump()

    # Extract promo_ids from the request
    promo_id = await promo_service.get_promo_id_by_scenario_id_service(
        request_payload.scenario_id
    )

    # Ensure no conflicts and log the action for auditing or tracking purposes
    action_log_id = await promo_utils.validate_no_conflict_and_log_action(
        request_payload_dict=request_payload_dict,
        promo_ids=[promo_id],
        action_name="scenario_name_edit",
        screen_type=promo_constants.SCREEN_STEP3,
        user_id=user_id,
    )

    try:
        # Execute the service operation and retrieve the result
        await promo_service.update_scenario_name_service(
            request=request_payload_dict, user_id=user_id
        )

        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=[promo_id],
            action_log_status=0,
            is_processing_status=0,
            user_id=user_id,
        )
    except BusinessLogicException as e:

        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=[promo_id],
            action_log_status=-1,
            is_processing_status=0,
            user_id=user_id,
        )

        raise BusinessLogicException(str(e)) from e
    except Exception as e:
        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=[promo_id],
            action_log_status=-1,
            is_processing_status=0,
            user_id=user_id,
        )
        raise RuntimeExecutionException(str(e)) from e

    return common_utils.create_response(
        message="Scenario name successfully updated!!"
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step0/basics"
)
async def get_promo_step0_basics(
    promo_id: int,
):
    data = await promo_service.get_promo_step0_basics(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step1/basics"
)
async def get_promo_step1_basics(
    promo_id: int,
):
    data = await promo_service.get_promo_step1_basics(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step1/details"
)
async def get_promo_step1_details(
    promo_id: int,
):
    data = await promo_service.get_promo_step1_details(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step1/exclusions"
)
async def get_promo_step1_exclusions(
    promo_id: int,
):
    data = await promo_service.get_promo_step1_exclusions(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step2/basics"
)
async def get_promo_step2_basics(
    promo_id: int,
):
    data = await promo_service.get_promo_step2_basics(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_router.get(
    "/vendor-funding-types"
)
async def get_vendor_funding_types():
    data = await promo_service.get_vendor_funding_types()
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.get(
    "/promo/{promo_id}/step3/basics"
)
async def get_promo_step3_basics(
    promo_id: int,
):
    data = await promo_service.get_promo_step3_basics(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.post(
    "/get-promo-discounts"
)
async def get_promo_discounts(
    request_payload: promo_models.GetPromoDiscounts,
    user_id: UserDependency
):
    data = await promo_service.get_promo_discounts(request_payload,user_id)
    return common_utils.create_response(
        data=data
    )

@promo_router.get(
    "/promo/{promo_id}/discounts/table-metadata"
)
async def get_promo_discounts_table_metadata(
    promo_id: int
):
    data = await promo_service.get_promo_discounts_table_metadata(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.get(
    "/promo/{promo_id}/step3/optimisation-details"
)
async def get_promo_step3_optimisation_details(
    promo_id: int,
):
    data = await promo_service.get_promo_step3_optimisation_details(promo_id)
    return common_utils.create_response(
        data=data
    )

@promo_v3_router.post(
    "/promo/step3/simulation-results"
)
async def get_promo_step3_simulation_results(
    request_payload: promo_models.GetPromoStep3SimulationResults,
):
    data = await promo_service.get_promo_step3_simulation_results(request_payload)
    return common_utils.create_response(
        data=data
    )

@promo_router.post(path="/validate_edit", tags=[PROMOTION_API_TAG])
async def check_for_bg_tasks(
    request_payload: promo_models.CheckBGProcess
):
    

    event_ids = await event_service.get_event_ids_by_promo_ids(request_payload.promo_id)
    has_locked_events = await event_service.has_locked_events(event_ids)
    if has_locked_events:
        return common_utils.create_response(
            data = {
                "is_valid": False,
                "message": f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} under a locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be edited",
                "is_processing": False
            }
        )
    
    is_processing_action = (
        await promo_service.check_promo_background_process_status_service(
            promo_ids=request_payload.promo_id,
            processing_action="validate_edit",
            screen_type="step2",
        )
    )


    return common_utils.create_response(
        data=is_processing_action
    )


@promo_router.get(path="/get-bmsm-offer-types", tags=[PROMOTION_API_TAG])
async def get_bmsm_offer_types():
    data = await promo_service.get_bmsm_offer_types_service()
    return common_utils.create_response(data=data)


@promo_router.post(path="/get-tiers", tags=[PROMOTION_API_TAG])
async def get_tiers(request_payload: promo_models.GetTiers):
    request_payload_dict = request_payload.model_dump()
    data = await promo_service.get_tiers_service(request=request_payload_dict)
    return common_utils.create_response(data=data)


@promo_router.post(path="/delete-tier", tags=[PROMOTION_API_TAG])
async def delete_tier(
    request_payload: promo_models.DeleteTier,
    user_id: UserDependency
):
    request_payload_dict = request_payload.model_dump()
    promo_ids = [request_payload_dict.get("promo_id")]

    # Ensure no conflicts and log the action for auditing or tracking purposes
    action_log_id = await promo_utils.validate_no_conflict_and_log_action(
        request_payload_dict=request_payload_dict,
        promo_ids=promo_ids,
        action_name=promo_constants.ACTION_DELETE,
        screen_type=promo_constants.SCREEN_TIER_MANAGEMENT,
        user_id=user_id,
    )

    try:
        await promo_service.delete_tier_service(request=request_payload_dict)

        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=promo_ids,
            action_log_status=0,
            is_processing_status=0,
            user_id=user_id,
        )
    except Exception as e:
        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=promo_ids,
            action_log_status=-1,
            is_processing_status=0,
            user_id=user_id,
        )

        raise RuntimeExecutionException(str(e)) from e

    return common_utils.create_response(message="Tier deleted successfully")


@promo_router.post(path="/tier-management", tags=[PROMOTION_API_TAG])
async def tier_management(
    request_payload: promo_models.TierManagement,
):
    if await promo_service.check_tier_existence_service(
        request_payload.promo_id,
        request_payload.tier_id,
        request_payload.tier_name
    ):
        raise BusinessLogicException(
            f"Tier Creation failed: A tier named '{request_payload.tier_name}' already exists."
        )

    tier_id = await promo_service.tier_management_service(
        request_payload=request_payload,
    )

    return common_utils.create_response(
        data={"tier_id": tier_id}
    )

@promo_router.post(path="/resimulate-offers", tags=[PROMOTION_API_TAG])
async def resimulate_offers(
    request_payload: promo_models.ResimulateOffers,
    background_task: BackgroundTasks,
    user_id: UserDependency
):
    request_payload_dict = request_payload.model_dump()
    _message = "Success"
    _status = status.HTTP_200_OK

    # Extract promo_ids from the request
    promo_ids = request_payload_dict.get("promo_id_list")
    eligible_promo_id_list = await promo_service.fetch_eligible_promos_service(
        promo_id_list=promo_ids
    )

    if not eligible_promo_id_list:
        raise BusinessLogicException(
            f"Resimulation failed: No eligible {client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL} found."
        )

    # Ensure no conflicts and log the action for auditing or tracking purposes
    action_log_id = await promo_utils.validate_no_conflict_and_log_action(
        request_payload_dict=request_payload_dict,
        promo_ids=eligible_promo_id_list,
        action_name="auto-simulate",
        screen_type=promo_constants.SCREEN_CALENDAR_VIEW,
        user_id=user_id,
        auto_resimulated_flag=1,
        source="marketing_calendar",
    )

    try:
        # Execute the service operation and retrieve the result
        _message, _status = await promo_service.resimulate_offers_service(
            action_log_id=action_log_id,
            guid=request_payload_dict["guid"],
            user_id=user_id,
            background_task=background_task,
            eligible_promo_id_list=eligible_promo_id_list,
            promo_id_list=promo_ids
        )

    except Exception as e:
        logger.error(f"Error in resimulate_offers: {e}", exc_info=True)
        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=promo_ids,
            action_log_status=-1,
            is_processing_status=0,
            auto_resimulated_flag=-1,
            source="marketing_calendar",
            user_id=user_id,
        )

        raise RuntimeExecutionException(str(e)) from e

    return common_utils.create_response(_message, _status, user_id, {})


@promo_router.post(path="/edit-promos-info")
async def edit_promos_info(
    request_payload: promo_models.EditPromosInfo,
    user_id: UserDependency
):

    request_payload_dict = request_payload.model_dump()

    # Extract promo_ids from the request
    promo_ids = [request_payload_dict["promos"][0].get("promo_id")]

    # Ensure no conflicts and log the action for auditing or tracking purposes
    action_log_id = await promo_utils.validate_no_conflict_and_log_action(
        request_payload_dict=request_payload_dict,
        promo_ids=promo_ids,
        action_name=promo_constants.ACTION_GET,
        screen_type=promo_constants.SCREEN_DECISION_DASHBOARD,
        user_id=user_id,
    )

    try:
        # Execute the service operation and retrieve the result
        await promo_service.edit_promos_info_service(request=request_payload_dict)

        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=promo_ids,
            action_log_status=0,
            is_processing_status=0,
            user_id=user_id,
        )
    except Exception as e:
        # Update the action log and processing status to reflect the completion of the operation
        await promo_utils.update_promo_action_log_processing_auto_simulate_flags(
            action_log_id=action_log_id,
            promo_ids=promo_ids,
            action_log_status=-1,
            is_processing_status=0,
            user_id=user_id,
        )

        raise RuntimeExecutionException(str(e)) from e

    return common_utils.create_response()


@promo_router.post(path="/simulation-results-details", tags=[PROMOTION_API_TAG])
async def get_simulation_results_details(
    request_payload: promo_models.SimulationResultsDetails,
    user_id: UserDependency
):
    request_payload_dict = request_payload.model_dump()

    data = await promo_service.get_simulation_results_details_service(
        request=request_payload_dict, user_id=user_id
    )

    return common_utils.create_response(data=data)


@promo_router.get(path="/view-by", tags=[PROMOTION_API_TAG])
async def get_view_by(
    type: Optional[str] = None, screen_type: Optional[str] = None
):
    data = await promo_service.get_view_by_service(
        screen_type=screen_type, type=type
    )

    return common_utils.create_response(data=data)


@promo_router.post(path="/priority-number", tags=[PROMOTION_API_TAG])
async def get_priority_number(
    request_payload: promo_models.GetPriorityNumber
):
    data = await promo_service.get_priority_number_service(
        request_payload=request_payload
    )

    return common_utils.create_response(data=data)


@promo_router.post(path="/promo-downloads", tags=[PROMOTION_API_TAG])
async def promo_downloads(
    request_payload: promo_models.Promotions,
    background_task: BackgroundTasks,
    user_id: UserDependency
):
    _message = "Request received. You will be notified once the downloads are ready."
    _status = status.HTTP_200_OK

    request_payload_dict = request_payload.model_dump()
    print(request_payload_dict)
    # Execute the service operation and retrieve the result
    background_task.add_task(
        promo_service.promo_downloads_service,
        request=request_payload_dict,
        user_id=user_id,
    )

    return common_utils.create_response(_message, _status, user_id, {})


@promo_router.get(
    "/promos/{promo_id}/sku-price-data-download"
)
async def download_promo_sku_price_data(promo_id: int,user_id: UserDependency):
    await promo_service.download_promo_sku_price_data(promo_id,user_id)
    return common_utils.create_response(
        message=global_constants.REPORT_DOWNLOAD_MESSAGE
    )

@promo_router.post(path="/is-valid-to-finalize", tags=[PROMOTION_API_TAG])
async def is_valid_to_finalize(
    request_data: promo_models.ToFinalizeModel
):
    data = await promo_service.is_valid_to_finalize(request_data=request_data)
    if not data[0].get("source_promo_id"):
        data = []
    return common_utils.create_response(data=data)


@promo_router.get(path="/exmd-template-id", tags=[PROMOTION_API_TAG])
async def get_exmd_template_id():
    data = await promo_service.get_exmd_template_id_service()
    return common_utils.create_response(data=data)


@promo_router.get(path="/exmd-price-filter", tags=[PROMOTION_API_TAG])
async def get_exmd_price_filter(promo_id: int):
    data = await promo_service.get_exmd_price_filter_service(promo_id=promo_id)
    return common_utils.create_response(data=data)


@promo_router.get(path="/exmd-target-folder", tags=[PROMOTION_API_TAG])
async def get_exmd_target_folder(promo_id: int):
    data = await promo_service.get_exmd_target_folder_service(promo_id=promo_id)
    return common_utils.create_response(data=data)


@promo_router.get(path="/exmd-sfcc-ats-check", tags=[PROMOTION_API_TAG])
async def get_exmd_sfcc_ats_check():
    data = await promo_service.get_exmd_sfcc_ats_check_service()
    return common_utils.create_response(data=data)


@promo_router.get(path="/exmd-sfcc-dropship-options", tags=[PROMOTION_API_TAG])
async def get_exmd_sfcc_dropship_options():
    data = await promo_service.get_exmd_sfcc_dropship_options_service()
    return common_utils.create_response(data=data)


@promo_router.post(path="/save-exmd", tags=[PROMOTION_API_TAG])
async def save_exmd(request_payload: promo_models.SaveExmd):
    request_payload_dict = request_payload.model_dump()
    await promo_service.save_exmd_service(request_data=request_payload_dict)
    return common_utils.create_response()


@promo_router.get(path="/exmd-promo", tags=[PROMOTION_API_TAG])
async def get_exmd_promo(promo_id: int):
    data = await promo_service.get_exmd_promo_service(promo_id=promo_id)
    return common_utils.create_response(data=data)


@promo_router.post("/promo/override-forecast", tags=[PROMOTION_API_TAG])
async def override_forecast_for_a_promo(
    override_request: promo_models.OverrideForecast,
    background_tasks: BackgroundTasks,
    user_id: UserDependency
):
    await promo_service.check_if_optimisation_is_running(
        override_request
    )
    background_tasks.add_task(
        promo_service.override_forecast_for_a_promo_service_caller,
        override_request, user_id
    )

    return common_utils.create_response(
        message="Override is processing in background"
    )


@promo_router.post("/promo/get-override-forecast", tags=[PROMOTION_API_TAG])
async def get_override_forecast_for_a_promo(
    override_request: promo_models.OverrideForecast
):
    data = await promo_service.get_override_forecast_for_a_promo_service(
        override_request
    )

    return common_utils.create_response(data=data)


@promo_router.get(path="/get-override-reason", tags=[PROMOTION_API_TAG])
async def get_override_reason():
    data = await promo_service.get_override_reason_service()

    return common_utils.create_response(
        data=data
    )

@promo_router.post("/promo/set-as-default")
async def set_default_for_scenario(
    set_default_for_scenario_request: promo_models.SetDefaultForScenario,
    user_id: UserDependency
):
    data = await promo_service.set_default_for_scenario(
        set_default_for_scenario_request,
        user_id
    )

    return common_utils.create_response(
        message="Successfully Set as Default #", data=data
    )

@promo_router.get("/promo/{promo_id}/stacked-offers")
async def get_promo_stacked_offers(
    promo_id: int
):
    data = await promo_service.get_promo_stacked_offers(
        promo_id
    )
    return common_utils.create_response(data=data)


@promo_router.post(
    "/server-callback",
)
async def opt_server_callback(
    request_payload: promo_models.OptServerCallback,
    background_tasks: BackgroundTasks
):
    logger.info(
        f"{environment.LOG_TITLE}: Received request at BE server-callback - payload: {request_payload.model_dump()}"
    )
    await promo_service.handle_opt_server_callback(
        request_payload,
        background_tasks
    )
    
    return common_utils.create_response(
        message = f"Received callback hit from opt server for {request_payload.action}"
    )

@promo_v3_router.post(
    "/validate-copy-offers"
)
async def validate_copy_offers(
    request_payload: promo_models.ValidateCopyOffers
):
    data = await promo_service.validate_copy_offers(request_payload)
    
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.get("/special-offer-types")
async def get_special_offer_types():
    """
    Get special offer types list. 
    Returns all special offer types in label-value format.
    """
    data = await promo_service.get_special_offer_types()
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.get("/special-offer-types/{offer_identifier}")
async def get_special_offer_type_details(offer_identifier: str):
    data = await promo_service.get_special_offer_type_details(offer_identifier)
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.post("/get-time-estimates")
async def get_expected_time(
    request_payload: promo_models.TimeEstimate,
    user_id: UserDependency
):
    data = await promo_service.get_expected_time(request_payload,user_id)
    return common_utils.create_response(
        data=data
    )


@promo_v3_router.get("/promos/{promo_id}/products")
async def get_product_details_of_promo(promo_id: int):
    """
    Get product list details based on promo Id 
    """
    data = await promo_service.get_product_details_of_promo(promo_id)
    return common_utils.create_response(data=data)


@promo_v3_router.get("/promos/{promo_id}/stores")
async def get_store_details_of_promo(promo_id: int):
    """
    Get store list details based on promo Id 
    """
    data = await promo_service.get_store_details_of_promo(promo_id)
    return common_utils.create_response(data=data)


@promo_v3_router.post("/promo/approve-ia-scenario")
async def approve_ia_scenario(
    request_payload: promo_models.ApproveIaScenario,
    user_id: UserDependency,
    backgound_tasks: BackgroundTasks
):
    await promo_service.validate_event_lock(
        [request_payload.promo_id],
        ConfigModuleEnum.EVENT,
        ConfigKeyEnum.ALLOW_PROMO_APPROVAL_AFTER_EVENT_LOCK,
        f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS_PLURAL.capitalize()} under a locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be approved"
    )
    is_finalised_promo = await promo_service.is_finalised_or_execution_approved_promos_present(
        [request_payload.promo_id]
    )

    if is_finalised_promo:
        backgound_tasks.add_task(
            log_action_decorator(
                get_promo_ids_func= lambda i: i.promo_id,
                action_name=promo_constants.ACTION_APPROVE,
                screen_name=promo_constants.SCREEN_STEP3,
                update_task_completion=False
            )(promo_service.approve_ia_scenario),
            request_payload,
            user_id,
            is_finalised_promo=is_finalised_promo
        )
        return common_utils.create_response(
            message = "IA Approval of the scenario is in progress. You will be notified once the operation is completed",
        )

    await log_action_decorator(
        get_promo_ids_func= lambda i: i.promo_id,
        action_name=promo_constants.ACTION_APPROVE,
        screen_name=promo_constants.SCREEN_STEP3
    )(promo_service.approve_ia_scenario)(
        request_payload,
        user_id,
        is_finalised_promo=is_finalised_promo
    )
    scenario_name = await promo_service.get_scenario_name(request_payload.scenario_id)
    return common_utils.create_response(
        message=f"Approval of IA recommended into {scenario_name} is completed.",
    )


@promo_v3_router.post("/promo/copy-discounts")
async def copy_discounts(
    request_payload: promo_models.CopyDiscounts,
    user_id: UserDependency
):
    await promo_service.copy_discounts(request_payload,user_id)
    return common_utils.create_response(
        message="Discounts copied successfully"
    )

@promo_router.post("/promos/{promo_id}/min-max-price-value")
async def get_min_max_price_value(
    request_payload: promo_models.GetMinMaxPriceValue,
    user_id: UserDependency
):
    data = await promo_service.get_min_max_price_value(request_payload,user_id)
    return common_utils.create_response(data=data)