import datetime
import json
from datetime import date, datetime
from typing import Optional, Union

from fastapi import BackgroundTasks,status
import google.oauth2.id_token
import httpx
from pydantic import BaseModel
import pandas as pd
import pytz
from configuration.environment import environment
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from exceptions.exceptions import ConflictException
from logger.logger import logger
from pricesmart_common import constants as global_constants
from pricesmart_common.data import get_config_value
from pricesmart_common.utils import async_execute_query,get_str_repr, get_array_format
from pricesmart_common import models as common_models
from promotions import constants as promo_constants
from promotions import queries as promo_queries
from promotions import service as promo_services
from promotions import models as promo_models
from promotions.constants import APPLICATION_VARIABLE, AND
from product import service as product_service
from client_configuration import constants as client_configuration_constants
from store import service as store_service
from filters.types import HierarchyFiltersInfoType, ExclusionUploadConfigInfoType

from pricesmart_common.utils import send_email
import traceback


def apply_date_range_filter(start_date, end_date, show_partially_overlapping_events=True):
    if not (start_date and end_date):
        return ""
        
    if show_partially_overlapping_events:
        return promo_constants.DATE_RANGE_CONDITION.format(
            start_date=get_str_repr(start_date), end_date=get_str_repr(end_date)
        )
    else:
        return promo_constants.DATE_RANGE_CONDITION_INCLUSIVE.format(
            start_date=get_str_repr(start_date), end_date=get_str_repr(end_date)
        )

async def validate_no_conflict_and_log_action(
    request_payload_dict,
    promo_ids,
    action_name,
    screen_type,
    user_id,
    pg_sg_edit=False,
    auto_resimulated_flag=None,
    source=None,
):
    is_processing_action = (
        await promo_services.check_promo_background_process_status_service(
            promo_ids=promo_ids,
            processing_action=action_name,
            screen_type=screen_type,
        )
    )

    if is_processing_action and is_processing_action.get("is_processing"):
        raise ConflictException(f"{is_processing_action.get('message')}")

    action_log_id = await promo_services.insert_action_log_service(
        action_name=get_str_repr(action_name),
        screen_type=get_str_repr(screen_type),
        promo_id_list=promo_ids,
        status=1,
        end_point="NULL",
        payload=request_payload_dict,
        user_id=user_id,
    )

    await promo_services.update_is_processing_service(
        promo_id_list=promo_ids, processing_flag=1
    )

    # If pg_sg_edit is True or certain conditions apply (e.g., source check), update the auto_resimulate flag
    if pg_sg_edit or source == "marketing_calendar":
        if auto_resimulated_flag is not None:  # Ensure flag is provided
            await promo_services.update_is_auto_resimulated_service(
                promo_id_list=promo_ids, auto_resimulated_flag=1
            )

    return action_log_id


async def update_promo_action_log_processing_auto_simulate_flags(
    action_log_id,
    promo_ids,
    action_log_status,
    is_processing_status,
    user_id,
    auto_resimulated_flag=None,
    pg_sg_edit=False,
    source=None,
):
    await promo_services.update_action_log_service(
        action_log_id=action_log_id, status=action_log_status, user_id=user_id
    )
    await promo_services.update_is_processing_service(
        promo_id_list=promo_ids, processing_flag=is_processing_status
    )

    # If pg_sg_edit is True or certain conditions apply (e.g., source check), update the auto_resimulate flag
    if pg_sg_edit or source == "marketing_calendar":
        if auto_resimulated_flag is not None:  # Ensure flag is provided
            await promo_services.update_is_auto_resimulated_service(
                promo_id_list=promo_ids, auto_resimulated_flag=auto_resimulated_flag
            )

    return True

def build_hierarchical_having_clause(hierarchies):
    having_parts = []
    for key, value in hierarchies.items():
        if value:
            value_str = str(value).replace("[", "(").replace("]", ")")
            having_part = f"COUNT(CASE WHEN hierarchy_level_id = '{key}' AND hierarchy_value_id IN {value_str} THEN 1 END) > 0"
            having_parts.append(having_part)

    having_str = ""
    if having_parts:
        having_str = "HAVING\n\t" + AND.join(having_parts)

    return having_str

def days_used_in_year(input_date: date) -> int:
    """
    Calculate the number of days from the start of the year to the given date.

    Args:
    date (date): The date for which to calculate the days used in the year.

    Returns:
    int: The number of days from the start of the year to the given date.
    """
    start_of_year = datetime(input_date.year, 1, 1)
    return (input_date - start_of_year).days + 1  # Include the current day


def determine_max_usage_period(
    start_date: date, end_date: date, return_key: str
) -> int:
    """
    Determine which period (month or year) has the most used days based on the given dates.

    Args:
    start_date (date): The start date.
    end_date (date): The end date.
    return_key (str): The period to compare ('month' or 'year').

    Returns:
    int: The month or year with the most used days.
    """
    if return_key == "month":
        return start_date.month if start_date.day >= end_date.day else end_date.month
    elif return_key == "year":
        days_used_start_year = days_used_in_year(start_date)
        days_used_end_year = days_used_in_year(end_date)
        return (
            start_date.year
            if days_used_start_year >= days_used_end_year
            else end_date.year
        )
    else:
        raise ValueError("Invalid return_key. Use 'month' or 'year'.")


def calculate_weighted_selling_price(
    weighted_base_price: Union[int, float], discount: Union[int, float]
) -> float:
    return weighted_base_price * (100 - discount) / 100


def calculate_gross_margin(
    revenue: Union[int, float],
    weighted_cost_price: Union[int, float],
    weighted_selling_price: Union[int, float],
) -> float:

    if weighted_selling_price == 0:
        return 0
    gross_margin = revenue * (1 - weighted_cost_price / weighted_selling_price)
    return round(gross_margin, 2)


def calculate_sales_units(
    revenue: Union[int, float], weighted_selling_price: Union[int, float]
) -> Union[int, float]:
    if weighted_selling_price == 0:
        return 0
    return revenue / weighted_selling_price


def calculate_gross_margin_percent(
    revenue: Union[int, float], gross_margin: Union[int, float]
) -> float:
    if revenue == 0:
        return 0
    return (gross_margin * 100) / revenue


def format_product_input_values(promo_id, key, values, status, is_deleted):
    """
    Formats input values for a given key and list of values.

    Args:
    - promo_id (int): The promotion ID.
    - key (str): The product hierarchy key.
    - values (list): The list of values associated with the key.
    - status (int): The status flag.
    - is_deleted (int): The deletion flag.

    Returns:
    - str: A formatted string of input values.
    """
    input_values = ""
    for item in values:
        input_values += f"({promo_id}, '{key}', {item}, {status}, {is_deleted}),\n"
    return input_values


def format_store_input_values(promo_id, key, values, status, is_deleted):
    """
    Formats input values for a given key and list of values for store hierarchies.

    Args:
    - promo_id (int): The promotion ID.
    - key (str): The store hierarchy key.
    - values (list): The list of values associated with the key.
    - status (int): The status flag.
    - is_deleted (int): The deletion flag.

    Returns:
    - str: A formatted string of input values.
    """
    input_values = ""
    for index, item in enumerate(values):
        if key == "store_id" and index == len(values) - 1:
            input_values += f"({promo_id}, '{key}', {item}, {status}, {is_deleted})"
        else:
            input_values += f"({promo_id}, '{key}', {item}, {status}, {is_deleted}),\n"
    return input_values


def format_promo_details_response(promos_details, screen_type):
    def get_performance(performance_value_str):
        if performance_value_str is None:
            return None
        elif performance_value_str < -promo_constants.PERFORMANCE_COMPARISON_VALUE:
            return "Dilutive"
        elif -promo_constants.PERFORMANCE_COMPARISON_VALUE <= performance_value_str <= promo_constants.PERFORMANCE_COMPARISON_VALUE:
            return "Average"
        else:
            return "Good"

    def populate_fields(promos_dict, fields_dict):
        data = {field: promos_dict.get(key) for key, field in fields_dict.items()}
        for key in fields_dict:
            promos.pop(key) if key in promos else None
        return data

    # Define fields for each part of the response based on the screen type
    def screen_fields(screen_type_str):
        common_fields = {
            "finalized": {
                "finalized_sales_units": "sales_units",
                "finalized_revenue": "revenue",
                "finalized_margin": "margin",
                "finalized_margin_percent": "margin_percent",
                "finalized_promo_spend": "promo_spend",
                "finalized_contribution_revenue": "contribution_revenue",
                "finalized_contribution_margin": "contribution_margin",
                "finalized_contribution_margin_percent": "contribution_margin_percent",
                "finalized_discount": "discount"
            },
            "ia_recommend": {
                "ia_recc_discount": "discount"
            },
            "baseline": {
                "baseline_sales_units": "sales_units",
                "baseline_revenue": "revenue",
                "baseline_margin": "margin"
            },
            "original": {
                "original_sales_units": "sales_units",
                "original_revenue": "revenue",
                "original_margin": "margin",
                "original_margin_percent": "margin_percent",
                "original_promo_spend": "promo_spend",
                "original_contribution_revenue": "contribution_revenue",
                "original_contribution_margin": "contribution_margin",
                "original_contribution_margin_percent": "contribution_margin_percent",
                "original_discount": "discount"
            },
            "stack_baseline": {
                "stack_baseline_sales_units": "baseline_sales_units",
                "stack_baseline_revenue": "baseline_revenue",
                "stack_baseline_margin": "baseline_margin"
            },
            "finalized_stack": {
                "finalized_stack_sales_units": "sales_units",
                "finalized_stack_revenue": "revenue",
                "finalized_stack_margin": "margin",
                "finalized_stack_margin_percent": "margin_percent",
                "finalized_stack_promo_spend": "promo_spend",
                "finalized_stack_contribution_revenue": "contribution_revenue",
                "finalized_stack_contribution_margin": "contribution_margin",
                "finalized_stack_contribution_margin_percent": "contribution_margin_percent"
            },
            "original_stack": {
                "original_stack_sales_units": "sales_units",
                "original_stack_revenue": "revenue",
                "original_stack_margin": "margin",
                "original_stack_margin_percent": "margin_percent",
                "original_stack_promo_spend": "promo_spend",
                "original_stack_contribution_revenue": "contribution_revenue",
                "original_stack_contribution_margin": "contribution_margin",
                "original_stack_contribution_margin_percent": "contribution_margin_percent"
            },
        }

        # Define screen-specific field overrides or additions
        screen_specific_fields = {
            promo_constants.SCREEN_DECISION_DASHBOARD: {
                "finalized_incremental": {
                    "finalized_incremental_sales_units": "sales_units",
                    "finalized_incremental_revenue": "revenue",
                    "finalized_incremental_margin": "margin"
                },
                "actualized_incremental": {
                    "actualized_incremental_sales_units": "sales_units",
                    "actualized_incremental_revenue": "revenue",
                    "actualized_incremental_margin": "margin"
                },
                "actualized": {
                    "actualized_sales_units": "sales_units",
                    "actualized_revenue": "revenue",
                    "actualized_margin": "margin",
                    "actualized_margin_percent": "margin_percent",
                    "actualized_contribution_revenue": "contribution_revenue",
                    "actualized_contribution_margin": "contribution_margin",
                    "actualized_contribution_margin_percent": "contribution_margin_percent",
                    "actualized_promo_spend": "promo_spend"
                },
                "finalized_stack_incremental": {
                    "finalized_stack_incremental_sales_units": "incremental_sales_units",
                    "finalized_stack_incremental_revenue": "incremental_revenue",
                    "finalized_stack_incremental_margin": "incremental_margin"
                }
            },
            promo_constants.SCREEN_CALENDAR_VIEW: {
                "incremental": {
                    "incremental_sales_units": "sales_units",
                    "incremental_revenue": "revenue",
                    "incremental_margin": "margin"
                }
            },
            promo_constants.SCREEN_WORKBENCH: {}
        }

        # Merge common fields with screen-specific fields
        fields_dict = common_fields.copy()
        fields_dict.update(screen_specific_fields.get(screen_type_str, {}))
        return fields_dict

    # Populate the response based on screen-specific fields
    screen_type_fields = screen_fields(screen_type)

    for promos in promos_details:
        performance_value = promos.get("performance")
        promos["performance"] = get_performance(performance_value)

        # Populate dictionaries for each part using the specified fields
        for key, fields in screen_type_fields.items():
            promos[key] = populate_fields(promos, fields)

    return promos_details


def where_using_str_formation(data):
    where_str = "WHERE\n"
    count = 1
    for key, value in data.items():
        if value:
            if count > 1:
                where_str += f"\nAND {key} = ANY (${count})"
            else:
                where_str += f"{key} = ANY (${count})"

            count += 1

    using_str = "USING"
    count_non_empty = sum(1 for key, value in data.items() if value)
    print(count_non_empty)
    count = 1
    for key, value in data.items():
        if value:
            if count < count_non_empty:
                using_str += f"\n(SELECT array_agg(DISTINCT val::INT) FROM jsonb_array_elements_text(coalesce(product_hierarchy->'{key}', '[]'::jsonb)) AS vals(val)),"
            else:
                using_str += f"\n(SELECT array_agg(DISTINCT val::INT) FROM jsonb_array_elements_text(coalesce(product_hierarchy->'{key}', '[]'::jsonb)) AS vals(val));"

            count += 1

    return where_str, using_str


async def call_server_callback(request_object):
    payload = {
        "guid": request_object["guid"],
        "user_id": request_object["user_id"],
        "action_log_id": request_object["action_log_id"],
        "source": request_object.get("source"),
        "action": request_object["action"],
        "promo_id": request_object.get("promo_id"),
        "scenario_id": request_object.get("scenario_id"),
        "status": request_object.get("status"),
        "message": request_object.get("message"),
        "new_promo_ids": request_object.get("new_promo_ids"),
        "success_promo_ids": request_object.get("success_promo_ids"),
        "failure_promo_ids": request_object.get("failure_promo_ids"),
    }

    try:
        # Convert payload dictionary to ServerCallback instance
        server_callback_request = promo_models.OptServerCallback(**payload)
    except Exception as e:
        logger.info(f"Error creating ServerCallback instance: {e}")
        return False

    # Call the server_callback function and await the result
    try:
        background_tasks = BackgroundTasks()
        response = await promo_services.handle_opt_server_callback(
            request_payload=server_callback_request,
            background_tasks=background_tasks
        )
        print(response)
    except Exception as e:
        logger.info(f"Error calling server_callback function: {e}")
        return False

    return True


async def cloud_function_report_handler(**kwargs):
    escaped_promo_name = ""
    fetch_query = kwargs["fetch_query"]
    report_file_name = kwargs["report_file_name"]
    report_type = kwargs["report_type"]
    user_id = kwargs["user_id"]
    promo_name = kwargs["promo_name"]
    is_json_data = kwargs.get("is_json_data", False)
    json_key = "json_data"
    fetch_query_str = str(fetch_query)
    fetch_query_str = fetch_query_str.replace("'", "''")
    report_file_name_str = str(get_str_repr(kwargs.get("report_file_name_str", "Sheet1")))
    apply_formatter = kwargs.get("apply_formatter", False)
    formatter_identifier = kwargs.get("formatter_identifier", None)
    download_query = promo_queries.INSERT_TB_REPORT_QUERY.format(
        markdown_schema=environment.markdown_schema,
        insert_query=fetch_query_str,
        sheet_name=report_file_name_str,
    )

    query_id_data = await async_execute_query(query=download_query)
    query_id = query_id_data[0]["id"]

    callback_url = (
        environment.SAKS_URL_PREFIX
        + global_constants.API_PREFIX
        + global_constants.USER_NOTIFY_TAG
    )

    download_url = environment.REPORT_GENERATOR_API
    print(promo_name)

    if report_file_name == promo_constants.PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA:
        if promo_name:
            escaped_promo_name = promo_name.replace("'", "''")
        download_payload = {
            "model_id": 1,
            "queries_list": [query_id],
            "download_config": {"format": report_type},
            "user_id": user_id,
            "parameters": {
                "report_name": promo_name,
                "is_json_data": is_json_data,
                "json_key": json_key,
                "success_message": f"Detailed simulation results are ready for download for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {escaped_promo_name}.",
                "failure_message": f"Download for {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} {escaped_promo_name} failed. Retry later or contact support.",
            },
            "callback_url": callback_url,
            "application": "promo",
        }
    else:
        download_payload = {
            "model_id": 1,
            "queries_list": [query_id],
            "download_config": {"format": report_type},
            "user_id": user_id,
            "parameters": {
                "report_name": report_file_name,
                "is_json_data": is_json_data,
                "json_key": json_key,
                "apply_formatter": apply_formatter,
                "formatter_identifier": formatter_identifier,
            },
            "callback_url": callback_url,
            "application": "promo",
        }

    print(download_url)
    print(json.dumps(download_payload))

    creds, _ = google.auth.default()
    if not creds.valid or creds.token is None:
        auth_req = google.auth.transport.requests.Request()
        creds.refresh(auth_req)
    token = creds.token

    async with httpx.AsyncClient() as client:
        res = await client.post(
            url=download_url,
            json=download_payload,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": APPLICATION_VARIABLE,
            },
            timeout=300,
        )
        print(repr(res))

    return True


def impute_special_characters(value: str):
    for special_char, replacement in promo_constants.SPECIAL_CHARACTER_MAPPING.items():
        value = value.replace(special_char, replacement)
    return value


async def format_simulation_results_data(data):
    response_data = []

    # Group data by promo_id, promo_name, recommendation_type_id, recommendation_type, last_approved_scenario_id, product_id, and product_name
    grouped_data = {}
    for item in data:
        key = (
            item['promo_id'],
            item['promo_name'],
            item['recommendation_type_id'],
            item['recommendation_type'],
            item['last_approved_scenario_id'],
            item['product_id'],
            item['product_name'],
            item['currency_id'],
            item['currency_name'],
            item['currency_symbol']
        )

        if key not in grouped_data:
            grouped_data[key] = []

        # Append scenario data to the corresponding product and promo grouping
        scenario_data = {
            "scenario_type": item["scenario_type"],
            "scenario_id": item["scenario_id"],
            "scenario_order_id": item["scenario_order_id"],
            "scenario_name": item["scenario_name"],
            "sales_units": float(item["sales_units"]) if item.get("sales_units") else None,
            "baseline_sales_units": float(item["baseline_sales_units"]) if item.get("baseline_sales_units") else None,
            "incremental_sales_units": float(item["incremental_sales_units"]) if item.get("incremental_sales_units") else None,
            "sales_units_lift": float(item["sales_units_lift"]) if item.get("sales_units_lift") else None,
            "revenue": float(item["revenue"]) if item.get("revenue") else None,
            "baseline_revenue": float(item["baseline_revenue"]) if item.get("baseline_revenue") else None,
            "incremental_revenue": float(item["incremental_revenue"]) if item.get("incremental_revenue") else None,
            "revenue_lift": float(item["revenue_lift"]) if item.get("revenue_lift") else None,
            "affinity_revenue": float(item["affinity_revenue"]) if item.get("affinity_revenue") else None,
            "cannibalization_revenue": float(item["cannibalization_revenue"]) if item.get("cannibalization_revenue") else None,
            "pull_forward_revenue": float(item["pull_forward_revenue"]) if item.get("pull_forward_revenue") else None,
            "margin": float(item["margin"]) if item.get("margin") else None,
            "baseline_margin": float(item["baseline_margin"]) if item.get("baseline_margin") else None,
            "incremental_margin": float(item["incremental_margin"]) if item.get("incremental_margin") else None,
            "margin_lift": float(item["margin_lift"]) if item.get("margin_lift") else None,
            "affinity_margin": float(item["affinity_margin"]) if item.get("affinity_margin") else None,
            "cannibalization_margin": float(item["cannibalization_margin"]) if item.get("cannibalization_margin") else None,
            "pull_forward_margin": float(item["pull_forward_margin"]) if item.get("pull_forward_margin") else None,
            "promo_spend": float(item["promo_spend"]) if item.get("promo_spend") else None,
            "aur": float(item["aur"]) if item.get("aur") else None,
            "aum": float(item["aum"]) if item.get("aum") else None,
            "gm_percent": float(item["gm_percent"]) if item.get("gm_percent") else None,
            "total_inventory": item["total_inventory"] if item.get("total_inventory") else None,
            "finalized_st_percent": float(item["finalized_st_percent"]) if item.get("finalized_st_percent") else None
        }

        grouped_data[key].append(scenario_data)

    # Construct the final response data
    for key, scenarios in grouped_data.items():
        (
            promo_id,
            promo_name,
            recommendation_type_id,
            recommendation_type,
            last_approved_scenario_id,
            product_id,
            product_name,
            currency_id,
            currency_name,
            currency_symbol
        ) = key

        promo_entry = {
            "promo_id": promo_id,
            "promo_name": promo_name,
            "recommendation_type_id": recommendation_type_id,
            "recommendation_type": recommendation_type,
            "last_approved_scenario_id": last_approved_scenario_id,
            "product_id": product_id,
            "product_name": product_name,
            "currency_id": currency_id,
            "currency_name": currency_name,
            "currency_symbol": currency_symbol,
            "scenario_data": sorted(scenarios, key=lambda x: x["scenario_order_id"])  # Sort by scenario_order_id
        }

        response_data.append(promo_entry)

    return response_data


def prepare_exclusion_query_v3(
    promo_id: int,
    product_exclusions: Optional[promo_models.ProductExclusion],
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType]
):
    if not product_exclusions:
        return f"UPDATE price_promo.promo_master pm SET exclusion_selection_type = NULL WHERE pm.promo_id = {promo_id};"

    exclusion_type = product_exclusions.product_exclusion_type
    json_data, array_data = {}, []

    if exclusion_type == promo_constants.TYPE_PRODUCT_HIERARCHY and product_exclusions.product_hierarchy:
        product_hierarchy = product_exclusions.product_hierarchy if product_exclusions.product_hierarchy else {}
        json_data = {
            product_hierarchy_config[key]["id_column"]: value
            for key,value in
            product_hierarchy.items()
            if product_hierarchy_config[key]["id"] is not None
        }

    elif exclusion_type == promo_constants.TYPE_UPLOAD:
        product_exclusions = product_exclusions.model_dump()
        json_data = product_exclusions.get("upload_details", {})
    elif exclusion_type == promo_constants.TYPE_COPY_PASTE:
        array_data = product_exclusions.product_ids 
    elif exclusion_type == promo_constants.TYPE_PRODUCT_GROUP:
        array_data = product_exclusions.product_group_ids
    return (
        f"PERFORM price_promo.fn_save_exclusion_hierarchy({promo_id}, {get_str_repr(exclusion_type)}, "
        f"'{json.dumps(json_data)}'::json, "
        f"array[{','.join(str(a) for a in array_data)}]::int[]);"
    )

def prepare_product_queries_v3(
    request_payload: promo_models.InsertPromoStep1Request,
    event_details: dict,
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType]
):
    product_selection_type = request_payload.product_selections.product_selection_type
    queries, updated_hierarchy = {}, {}

    if event_details.get("has_locked_product_selection"):
        queries["event_based_production_insertion_query"] = f"""
            perform price_promo.fn_insert_products_for_promo_based_on_event(
                {request_payload.promo_id},
                {request_payload.event_id}
            );
        """

    elif product_selection_type == 1:
        queries["site_wide"] = f"PERFORM price_promo.fn_create_promo_product_partition_table({request_payload.promo_id});"
    elif product_selection_type == 2:
        product_hierarchy = request_payload.product_selections.product_hierarchy if request_payload.product_selections.product_hierarchy else {}
        updated_hierarchy = {
                product_hierarchy_config[key]["id_column"]: value
                for key,value in
                product_hierarchy.items()
                if product_hierarchy_config[key]["id"] is not None
            }
        where_str, using_str = where_using_str_formation(updated_hierarchy)
        queries[
            "product_hierarchy"] = promo_queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_HIERARCHY_INPUTS_QUERY.format(
            where_str=where_str, using_str=using_str,
            STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY=promo_queries.STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY,
            STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY=promo_queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY
        )
    elif product_selection_type in (4, 5, 6):
        lifecycle_indicators = request_payload.product_selections.product_hierarchy.get('lifecycle_indicator')
        if lifecycle_indicators:
            queries["lifecycle_indicator"] = promo_queries.STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY
        queries["product_ids"] = promo_queries.STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_IDS_INPUTS_QUERY
    elif product_selection_type in (3, 7):
        lifecycle_indicators = request_payload.product_selections.product_hierarchy.get('lifecycle_indicator')
        if lifecycle_indicators:
            queries["lifecycle_indicator"] = promo_queries.STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY
        queries["product_group_ids"] = promo_queries.STEP1_SAVE_PROMO_PRODUCT_GROUP_BASED_ON_PG_INPUTS_QUERY

    return queries, updated_hierarchy


def compose_final_query(promo_id, updated_product_hierarchy, update_promo_details_query, exclusion_query,
                        product_queries, store_queries, extra_query, product_ids, store_ids, product_group_ids,
                        store_group_ids, lifecycle_indicator_ids):
    new_product_hierarchy = json.dumps(updated_product_hierarchy)
    update_product_store_count_query = promo_queries.UPDATE_PROMO_PRODUCT_STORE_COUNT_QUERY.format(promo_id=promo_id)

    return promo_queries.STEP1_SAVE_DETAILS_QUERY.format(
        promo_id=promo_id,
        product_hierarchy=f"'{new_product_hierarchy}'" if updated_product_hierarchy else "NULL",
        product_ids=f"ARRAY{product_ids}" if product_ids else "NULL",
        store_ids=f"ARRAY{store_ids}" if store_ids else "NULL",
        product_group_ids=f"ARRAY{product_group_ids}" if product_group_ids else "NULL",
        store_group_ids=f"ARRAY{store_group_ids}" if store_group_ids else "NULL",
        lifecycle_indicator_ids=f"ARRAY{lifecycle_indicator_ids}" if lifecycle_indicator_ids else "NULL",
        update_promo_details_query=update_promo_details_query,
        update_product_store_count_query=update_product_store_count_query,
        save_product_details_based_on_event_query = product_queries.get("event_based_production_insertion_query",""),
        save_product_details_based_on_site_wide_query=product_queries.get("site_wide", ""),
        save_product_details_based_on_product_hierarchy_inputs_query=product_queries.get("product_hierarchy", ""),
        save_product_details_based_on_product_ids_inputs_query=product_queries.get("product_ids", ""),
        stores_insertion_based_on_event = store_queries.get("stores_insertion_based_on_event",""),
        save_store_details_based_on_all_store_query=store_queries.get("all_store", ""),
        save_store_details_based_on_bnm_store_query=store_queries.get("bnm_store", ""),
        save_store_details_based_on_ecom_store_query=store_queries.get("ecom_store", ""),
        save_store_details_based_on_store_ids_inputs_query=store_queries.get("store_ids", ""),
        save_product_groups_based_on_pg_inputs_query=product_queries.get("product_group_ids", ""),
        save_store_groups_based_on_sg_inputs_query=store_queries.get("store_groups", ""),
        extra_query=extra_query,
        exclusion_query=exclusion_query,
    )


def prepare_store_queries_v3(
    request_payload: promo_models.InsertPromoStep1Request,
    event_details: dict):
    queries = {}

    if (
        event_details.get("has_locked_store_selection")
    ):
        queries["stores_insertion_based_on_event"] = f"""
            perform price_promo.fn_insert_stores_for_promo_based_on_event(
                {request_payload.promo_id},
                {request_payload.event_id}
            );
        """
        return queries

    selection_type = request_payload.store_selections.store_selection_type
    if selection_type == 2:
        queries["bnm_store"] = promo_queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_BNM_STORE_QUERY
    elif selection_type == 3:
        queries["ecom_store"] = promo_queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_ECOM_STORE_QUERY
    elif selection_type in (4, 5, 6):
        queries["store_ids"] = promo_queries.STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_STORE_IDS_INPUTS_QUERY
    elif selection_type == 7:
        queries["store_groups"] = promo_queries.STEP1_SAVE_PROMO_STORE_GROUP_BASED_ON_SG_INPUTS_QUERY

    return queries


async def update_promo_details(
    promo_id,
    step_count,
    product_selection_type,
    customer_type,
    offer_distribution_channel,
    store_selection_type,
    user_id,
    product_exclusion_type,
):
    update_query = promo_queries.STEP1_UPDATE_PROMO_DETAILS_AT_QUERY.format(
        promo_id=promo_id,
        step_count=step_count,
        product_selection_type=get_str_repr(product_selection_type),
        customer_type=customer_type,
        offer_distribution_channel=offer_distribution_channel,
        store_selection_type=store_selection_type,
        user_id=user_id,
        product_exclusion_type=get_str_repr(product_exclusion_type),
    )

    return update_query


async def prepare_update_promo_details_query_v3(request_data: promo_models.InsertPromoStep1Request,user_id):
    return await update_promo_details(
        promo_id=request_data.promo_id,
        step_count=request_data.step_count,
        product_selection_type=request_data.product_selections.product_selection_type,
        customer_type=request_data.customer_selections.customer_type,
        offer_distribution_channel=request_data.customer_selections.offer_distribution_channel,
        store_selection_type=request_data.store_selections.store_selection_type,
        user_id=user_id,
        product_exclusion_type=request_data.product_exclusions.product_exclusion_type
    )

async def log_action(request_payload_dict, promo_ids, action_name, screen_type, user_id, auto_resimulated_flag=0):
    return await validate_no_conflict_and_log_action(
        request_payload_dict=request_payload_dict,
        promo_ids=promo_ids,
        action_name=action_name,
        screen_type=screen_type,
        user_id=user_id,
        auto_resimulated_flag=auto_resimulated_flag,
    )

async def log_action_v2(
    request_payload: BaseModel,
    promo_ids: list[int],
    action_name: str,
    screen_type: str,
    user_id: int,
    auto_resimulated_flag = 0
):
    return await validate_no_conflict_and_log_action(
        request_payload_dict=request_payload.model_dump(),
        promo_ids=promo_ids,
        action_name=action_name,
        screen_type=screen_type,
        user_id=user_id,
        auto_resimulated_flag=auto_resimulated_flag,
    )


async def update_promo_log(action_log_id, promo_ids, action_log_status, user_id, is_processing_status=0, auto_resimulated_flag=None):
    await update_promo_action_log_processing_auto_simulate_flags(
        action_log_id=action_log_id,
        promo_ids=promo_ids,
        action_log_status=action_log_status,
        is_processing_status=is_processing_status,
        user_id=user_id,
        auto_resimulated_flag=auto_resimulated_flag,
    )

async def is_finalized_scenario(promo_id: int, scenario_id: int) -> bool:
    query = promo_queries.IS_FINALIZED_SCENARIO_QUERY.format(promo_id=promo_id, scenario_id=scenario_id)
    result = await async_execute_query(query)
    return bool(result)

def format_validation_data(data):
    formatted_data = []
    summary_message = None

    for item in data:
        if item["promo_id"] is None:
            summary_message = item["validation_message"]
        else:
            formatted_data.append(item)

    if not formatted_data and (summary_message is None or summary_message == ""):
        return {"data": []}

    return {
        "promo_data": formatted_data,
        "summary_message": summary_message
    }

async def generate_simulator_query(request_payload: dict, promo_id: int, aggregation_level: int):

    product_hierarchy_levels = request_payload["aggregation"]["product_hierarchy_levels"]
    store_hierarchy_levels = request_payload["aggregation"]["store_hierarchy_levels"]

    column_selections = ""
    selections_from_cte = ""
    join_str = " "
    group_by_str = ""
    group_by_from_ctes = ""
    agg_selections = ""
    column_selections_in_unions = ""
    group_by_in_unions = ""

    store_downloads_select_columns: str = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.STORE_DOWNLOADS_SELECT_COLUMNS
    )

    if len(product_hierarchy_levels) > 0:
        product_hierarchy_config = await product_service.get_product_hierarchy_id_mapping()
        join_str += " join price_promo.product_master pm on prsa.product_id = pm.product_id "
        column_selections = "".join(
            f",{product_hierarchy_config[level]["name_column"]}"
            for level in product_hierarchy_levels 
            if level in product_hierarchy_config
        )
        selections_from_cte += "".join(
            f",fmc.{product_hierarchy_config[level]['name_column']}" 
            for level in product_hierarchy_levels 
            if level in product_hierarchy_config
        )

        agg_selections += "".join(
            f",fom.{product_hierarchy_config[level]['name_column']}" 
            for level in product_hierarchy_levels 
            if level in product_hierarchy_config
        )
        if column_selections:
            group_by_str = column_selections
            group_by_from_ctes = selections_from_cte
            group_by_in_unions = column_selections

    if len(store_hierarchy_levels) > 0:
        store_hierarchy_config = await store_service.get_store_hierarchy_id_mapping()
        join_str += f" join (select {store_downloads_select_columns} , ps_reco_level from global.tb_store_master) t on t.ps_reco_level = store_hierarchy "
        column_selections += "".join(
            f",{store_hierarchy_config[level]['name_column']}" 
            for level in store_hierarchy_levels 
            if level in store_hierarchy_config
        )

        selections_from_cte += "".join(
            f",fmc.{store_hierarchy_config[level]['name_column']}" 
            for level in store_hierarchy_levels 
            if level in store_hierarchy_config
        )
        agg_selections += "".join(
            f",fom.{store_hierarchy_config[level]['name_column']}" 
            for level in store_hierarchy_levels 
            if level in store_hierarchy_config
        )
        if column_selections:
            group_by_str = column_selections
            group_by_from_ctes = selections_from_cte
            group_by_in_unions = column_selections

    timeline_value = int(request_payload.get('aggregation', {}).get('timeline', -200))
    column_selections_in_unions = column_selections
    
    if timeline_value == 0:
        group_by_str += ",tfdm.date, tfdm.fiscal_week , tfdm.fiscal_year "
        group_by_from_ctes += ",fmc.date, fmc.fiscal_week , fmc.fiscal_year "
        group_by_in_unions += ",date, fiscal_week , fiscal_year "
        selections_from_cte += ",fmc.fiscal_date, fmc.fiscal_week , fmc.fiscal_year "
        column_selections += ",tfdm.date, tfdm.fiscal_week , tfdm.fiscal_year "
        column_selections_in_unions += ",date, fiscal_week , fiscal_year "
        agg_selections += ",fiscal_date, fiscal_week , fiscal_year "
    elif timeline_value == 1:
        group_by_str += ",tfdm.fiscal_week , tfdm.fiscal_year "
        group_by_from_ctes += ",fmc.fiscal_week , fmc.fiscal_year "
        group_by_in_unions += ",fiscal_week , fiscal_year "
        selections_from_cte += ",fmc.fiscal_week , fmc.fiscal_year "
        column_selections += ",tfdm.fiscal_week , tfdm.fiscal_year "
        column_selections_in_unions += ",fiscal_week , fiscal_year "
        agg_selections += ",fiscal_week , fiscal_year "

    join_str +=  (
        " JOIN global.tb_fiscal_date_mapping tfdm ON tfdm.date = prsa.recommendation_date "
        if timeline_value != -200 
        else 
        " "
    )
    

    if product_hierarchy_levels == [promo_constants.OVERALL] and store_hierarchy_levels == [promo_constants.OVERALL] and timeline_value == -200: 
        query = promo_queries.OVERALL_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY.format(
            promo_id = promo_id,
            promo_schema = environment.promo_schema,
            column_selections = column_selections,
            join_str = join_str,
            group_by_str = group_by_str,
            selections_from_cte=selections_from_cte,
            group_by_from_ctes=group_by_from_ctes,
            agg_selections=agg_selections,
            target_currency_id=request_payload.get("target_currency_id")
        )
    else:
        product_hierarchy_value_config = {value["name_column"]: value for value in product_hierarchy_config.values()}
        final_select_columns = []
        original_override_join_condition = []
        stacked_join_condition = []
        stacked_override_join_condition = []
        for column in column_selections_in_unions.split(","):
            column = column.strip()
            if column == "":
                continue
            original_override_join_condition.append(
                f"fom.{column} = fomc.{column}"
            )
            stacked_join_condition.append(
                f"fom.{column} = fsmc.{column}"
            )
            stacked_override_join_condition.append(
                f"fom.{column} = fsomc.{column}"
            )
            if column in product_hierarchy_value_config:
                final_select_columns.append(f',{product_hierarchy_value_config[column]["name_column"]} as "{product_hierarchy_value_config[column]["label"]}"')
            else:
                final_select_columns.append(f',{column}')
            
        query = promo_queries.AGGR_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY.format(
            promo_id = promo_id,
            promo_schema = environment.promo_schema,
            column_selections = column_selections,
            final_select_columns = "".join(final_select_columns),
            join_str = join_str,
            group_by_str = group_by_str,
            selections_from_cte=selections_from_cte,
            group_by_from_ctes=group_by_from_ctes,
            agg_selections=agg_selections,
            column_selections_in_unions=column_selections_in_unions,
            group_by_in_unions=group_by_in_unions,
            target_currency_id=request_payload.get("target_currency_id"),
            original_override_join_condition=AND.join(original_override_join_condition) if original_override_join_condition else "true",
            stacked_join_condition=AND.join(stacked_join_condition) if stacked_join_condition else "true",
            stacked_override_join_condition=AND.join(stacked_override_join_condition) if stacked_override_join_condition else "true",
        )

    return query

def get_offer_description(scenario: promo_models.ScenarioDetails) -> Optional[str]:
    """
    Generate a description for a promotion offer based on the offer type and values.
    """
    if scenario.special_offer_data:
        return _format_special_offer(scenario.special_offer_data)

    if scenario.offer_type == 'bmsm':
        return _format_bmsm_offer(scenario)
    
    offer_formatters = {
        'percent_off': lambda s: f"{s.offer_x_value:g}% off" if s.offer_x_value is not None else None,
        'upto_x_percent_off': lambda s: f"{s.offer_x_value:g}% off" if s.offer_x_value is not None else None,
        'extra_amount_off': lambda s: f"${s.offer_x_value:g} off" if s.offer_x_value is not None else None,
        'fixed_price': (
            lambda s: f"At ${s.offer_x_value:g}" 
            if s.offer_x_value is not None 
            else None
        ),
        'bxgy': (
            lambda s: f"Buy {int(s.offer_x_value)} Get {int(s.offer_y_value)} Free" 
            if s.offer_x_value is not None and s.offer_y_value is not None 
            else None
        ),
        'bxgy_percent_off': (
            lambda s: f"Buy {int(s.offer_x_value)} Get {int(s.offer_y_value)} At {s.offer_z_value}% off" 
            if s.offer_x_value is not None and s.offer_y_value is not None and s.offer_z_value is not None 
            else None
        ),
        'tiered_offer': lambda _: f"Tiered {client_configuration_constants.PROMO_IDENTIFIER_ALIAS}"
    }
    
    return offer_formatters.get(scenario.offer_type, lambda _: f"Invalid {client_configuration_constants.PROMO_IDENTIFIER_ALIAS} type")(scenario)

def _format_special_offer(special_offer: promo_models.SpecialOfferTypeData) -> Optional[str]:
    if special_offer.discount_value is None:
        return None

    formatters = {
        'percent_off': lambda v: f"{v:g}% off",
        'dollar_off': lambda v: f"${v:g} off",
        'fixed_price': lambda v: f"At ${v:g}"
    }
    suffix = formatters.get(special_offer.discount_type, lambda v: str(v))(special_offer.discount_value)
    return f"{special_offer.name} - {suffix}"

def _format_bmsm_offer(scenario: promo_models.ScenarioDetails) -> Optional[str]:
    if scenario.offer_x_value is None or scenario.offer_y_value is None:
        return None
    formatters = {
        ('unit', 'percent_off'): lambda s: f"Buy {int(s.offer_x_value)} At {s.offer_y_value}% off",
        ('unit', 'dollar_off'): lambda s: f"Buy {int(s.offer_x_value)} Get ${s.offer_y_value} off",
        ('unit', 'dollar'): lambda s: f"Buy {int(s.offer_x_value)} At ${s.offer_y_value}",
        ('dollar', 'percent_off'): lambda s: f"Buy ${s.offer_x_value} At {s.offer_y_value}% off",
        ('dollar', 'dollar_off'): lambda s: f"Buy ${s.offer_x_value} Get ${s.offer_y_value} off"
    }
    return formatters.get(
        (scenario.offer_x_type, scenario.offer_y_type), # type: ignore
        lambda _: "Invalid BMSM combination"
    )(scenario)


async def get_non_past_promos(promo_ids: list[int]):
    if not promo_ids:
        return []
    query = promo_queries.GET_NON_PAST_PROMOS_QUERY.format(
        promo_ids=get_array_format(promo_ids),
        client_timezone=get_str_repr(global_constants.get_client_timezone())
    )
    result = await async_execute_query(query)
    return [row.get("promo_id") for row in result] if result else []


def get_product_store_hierarchies_select_list(hierarchy_config: dict[str,HierarchyFiltersInfoType]):
    select_list = []
    for hierarchy_key, cfg in hierarchy_config.items():
        if cfg.get('id') is not None:
            id = cfg["id"]
            select_list.append(
                f"'{hierarchy_key}', COALESCE(jsonb_agg(CASE WHEN hierarchy_level_id = {id} THEN jsonb_build_object('label', hierarchy_value_name, 'value', hierarchy_value_id) END) FILTER (WHERE hierarchy_level_id = {id}), '[]'::jsonb)"
            )
    return ",\n    ".join(select_list)


def return_sku_column_name_in_excel_data(upload_data_df: pd.DataFrame, exclusion_upload_configuration: dict[str, ExclusionUploadConfigInfoType] ): 
    # Find the key with value_column == 'product_id'
    product_id_key = next((key for key, value in exclusion_upload_configuration.items() if value["cid_column"] == "product_id"), None)

    # Normalize result for comparison
    product_id_key = product_id_key if product_id_key else None

    # Step 2: Check if result matches any item in the list (case-insensitive)
    sku_column_name = next((item for item in upload_data_df if item == product_id_key), None)

    return sku_column_name

async def build_product_hierarchy_select_clause_for_download(product_hierarchies: list[int]):
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    select_list = []
    for cfg in product_hierarchy_config.values():
        if cfg.get("is_linked_to_downloads") is False or cfg.get("id") not in product_hierarchies:
            continue
        select_list.append(
            f""",product_master_data->>'{cfg.get("value_column")}' as "{cfg.get('label')}" """
        )
    return "".join(select_list)


async def build_store_hierarchy_select_clause_for_download(store_hierarchies: list[int]):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    select_list = []
    for cfg in store_hierarchy_config.values():
        if cfg.get("is_linked_to_downloads") is False or cfg.get("id") not in store_hierarchies:
            continue
        select_list.append(
            f""",store_master_data['{cfg.get('value_column')}'] as "{cfg.get('label')}" """
        )
    return "".join(select_list)


async def downstream_integration_util(
    query_id: int,
    integration_id: int,
    promo_ids: list[int],
    action: str,
    user_id: int
):
    # Get current timestamp in client timezone
    client_tz = pytz.timezone(global_constants.CLIENT_TIMEZONE_FOR_PYTHON)
    current_time = datetime.now(client_tz)
    
    today = current_time.strftime('%A').lower()  # Day of week (e.g., "tuesday")
    today_str = current_time.strftime('%d %B %Y')  # Date format (e.g., "17 June 2025")
    request: dict = {
        "query_id": query_id,
        "bucket_name": environment.DOWNSTREAM_BUCKET_NAME,
        "file_format": promo_constants.CSV_FILE_FORMAT,
        "operation": "upload",
        "folder_name": promo_constants.DOWNSTREAM_FOLDER_NAME,
        "promo_ids": promo_ids,
        "integration_id": integration_id,
        "action": action,
        "user_id": user_id
    }
    print("Request", request)
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=environment.DOWNSTREAM_CLOUD_FUNCTION_URL,
                json=request,
                headers={
                    "Content-Type": "application/json",
                },
                timeout=600,
            )
            response = response.json()
            logger.info(f"Downstream Integration Response: {response}")
            if response["status"] == "success":
                subject = f"[Success] [{environment.client_name.upper()}] [{environment.deployment_env.upper()}] [{today_str}] [{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.upper()}]- Downstream Integration Successful!"
                message = (
                    f"""<p><b><i>Job Time: {str(today)}</i></b></p>
                        <p>{response["message"]}</p>
                        <p>integration ID: {integration_id}</p>
                        <p>action: {action}</p>
                        <p>promo_ids: {promo_ids}</p>
                        <p>File uploaded to S3 bucket:</p>
                        <p>{
                            ",".join(
                                f"{file_name}"
                                for file_name in response["file_names"]
                            )
                        }</p>
                    """
                )
                print(subject)
                print(message)
                await send_email(subject=subject, message=message)
            else:
                logger.info("Downstream Integration Failed")
                subject = f"[Error] [{environment.client_name.upper()}] [{environment.deployment_env.upper()}] [{today_str}] [{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.upper()}]- Downstream Integration failed!"
                message = (
                    f"""<p><b><i>Job Time: {str(today)}</i></b></p>
                        <p>Downstream Integration failed for integration ID: {integration_id}</p>
                        <p>{response["message"]}<p>
                        <p>Integration ID: {integration_id}</p>
                        <p>promo_ids: {promo_ids}</p>
                    """
                )
                print(subject)
                print(message)
                await send_email(subject=subject, message=message)
    except httpx.HTTPError:
        print("Downstream Integration Failed.")
        trace = traceback.format_exc()
        subject = f"[Error] [{environment.client_name.upper()}] [{environment.deployment_env.upper()}] [{today_str}] [{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.upper()}]- Downstream Integration failed!"
        message = (
            f"""<p> <b><i>Downstream Integration failed for integration ID: {integration_id} .Job Time : {str(today)}</i></b> </p> <pre>{trace}<pre> """
        )
        await send_email(subject=subject, message=message)