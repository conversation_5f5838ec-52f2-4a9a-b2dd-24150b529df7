from datetime import date, datetime
from pricesmart_common import models as common_models
from typing import Any, List, Literal, Optional, Union, Annotated
from pricesmart_common import constants as common_constants
from pricesmart_common.staticclass import StaticClass
from product import models as product_models
from promotions import constants as promo_constants
from enums.Enums import MaximizationParameterEnum
from pydantic import BaseModel, field_validator, Field, PydanticUserError, BeforeValidator, model_validator
from typing_extensions import TypedDict, NotRequired
from events.models import EventIdType
from pydantic import PrivateAttr, model_serializer
from decision_dashboard import models as decision_dashboard_models
from exceptions.exceptions import CommonException
from promotions.enums import OfferTypeEnum, ScreenTypeEnum

class HeroSKU(BaseModel):
    is_hero: int
    product_h5_id: int
    is_active: Optional[int]

    @field_validator("is_hero")
    def is_hero_match(cls, value):
        if value not in [0, 1]:
            raise ValueError("Values for is_hero must be in [0, 1]")
        return value


class HeroProductHierarchy(product_models.ProductHierarchy):
    product_h5: Optional[List[HeroSKU]]


class AllStoresHierarchy(product_models.StoreHierarchy):
    all_stores: bool


def string_format_date_time(calendar_date: str) -> Union[datetime, str]:
    if calendar_date:
        month, today_date, year = calendar_date.split("/")
        new_date = datetime(year=int(year), month=int(month), day=int(today_date))
        return new_date
    return calendar_date


default_start_date, default_end_date = StaticClass.get_quarter_dates()


class MarketingFilters(BaseModel):
    event_id: Optional[List[int]]
    channel_type: Optional[List[int]]
    ad_type: Optional[List[int]]


class VendorFunding(BaseModel):
    vf_type: Optional[str] = None
    vf_per_unit: Optional[float] = 0
    vf_fixed_amount: Optional[float] = 0

class UptoPercentDiscountType(BaseModel):
    min_upto_percent: Optional[int] = None
    max_upto_percent: Optional[int] = None
    products_on_max_upto_percent: Optional[int] = None


class PromoDateRange(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None

    @field_validator(*["start_date", "end_date"], mode="before")
    def check_and_reformat_date_format(cls, calendar_date):
        if isinstance(calendar_date, str) and '/' in calendar_date:
            return string_format_date_time(calendar_date)
        return calendar_date
    


class PlaceholderMetrics(BaseModel):
    revenue_target: Union[int, float]
    inventory: int
    discount: Union[int, float]


class ProductSelection(BaseModel):
    product_selection_type: int
    product_hierarchy: Optional[dict[str,list[int]]]
    product_ids: Optional[List[int]]
    product_group_ids: Optional[List[int]]


class ExclusionUploadedFileDetails(BaseModel):
    l0_id: Optional[Union[str, int]] = None
    l0_cid: Optional[Union[str, int]] = None
    l0_cuq: Optional[str] = None
    l1_id: Optional[Union[str, int]] = None
    l1_cid: Optional[Union[str, int]] = None
    l1_cuq: Optional[str] = None
    l2_id: Optional[Union[str, int]] = None
    l2_cid: Optional[Union[str, int]] = None
    l2_cuq: Optional[str] = None
    l3_id: Optional[Union[str, int]] = None
    l3_cid: Optional[Union[str, int]] = None
    l3_cuq: Optional[str] = None
    l4_id: Optional[Union[str, int]] = None
    l4_cid: Optional[Union[str, int]] = None
    l4_cuq: Optional[str] = None
    l5_id: Optional[Union[str, int]] = None
    l5_cid: Optional[Union[str, int]] = None
    l5_cuq: Optional[str] = None
    mfg_no: Optional[Union[str, int]] = None
    brand_cid: Optional[Union[str, int]] = None
    mfg_name: Optional[str] = None


class ProductExclusion(BaseModel):
    product_exclusion_type: Optional[int] = None
    product_hierarchy: Optional[dict[str,list[int]]]
    product_ids: List[int] = []
    product_group_ids: List[int] = []
    upload_details: Optional[List[ExclusionUploadedFileDetails]] = None


class CustomerSelection(BaseModel):
    customer_type: int
    offer_distribution_channel: int


class StoreSelection(BaseModel):
    store_selection_type: int
    store_hierarchy: Optional[product_models.StoreHierarchy]
    store_ids: Optional[List[int]]
    store_group_ids: Optional[List[int]]


class PromoTarget(BaseModel):
    rule_id: Optional[int]
    gross_margin_target: Optional[float]
    gross_margin_priority: Optional[int]
    revenue_target: Optional[float]
    revenue_priority: Optional[int]
    units_target: Optional[int]
    units_priority: Optional[int]
    gross_margin_percent_target: Optional[float]
    gross_margin_percent_priority: Optional[int]

    opt_discount_type_id: Optional[int]
    discount_type_values: Optional[List[float]]
    min_discount: Optional[float]
    max_discount: Optional[float]

    revenue_lift: Optional[float]
    gross_margin_lift: Optional[float]
    gross_margin_percent_lift: Optional[float]
    units_lift: Optional[float]
    
    reoptimise_flag: Optional[int]
    maximization_parameter: Optional[MaximizationParameterEnum] = None


class SpecialOfferTypeData(BaseModel):
    customer_reach: float
    customer_redemption_rate: float
    discount_type: str
    discount_type_id: int
    discount_value: float
    name: str
    special_offer_type_id: int

class ScenarioDetails(BaseModel):
    scenario_id: int
    scenario_name: Optional[str] = None
    scenario_order_id: int
    scenario_type: Optional[str] = None
    offer_type_id: int
    offer_type: str
    offer_value: Optional[str] = None
    offer_x_value: Optional[float] = None
    offer_x_type: Optional[str] = None
    offer_y_value: Optional[float] = None
    offer_y_type: Optional[str] = None
    offer_z_value: Optional[float] = None
    offer_z_type: Optional[str] = None
    tier_id: Optional[int] = None
    special_offer_data: Optional[SpecialOfferTypeData] = None
    updated: bool = Field(default=False,exclude=True)
    updated_at: Optional[common_models.CustomDateTime] = None
    updated_by: Optional[int] = None
    created_at: Optional[common_models.CustomDateTime] = None


class ScenarioData(BaseModel):
    scenario_id: Optional[int] = None
    scenario_name: Optional[str] = None
    scenario_order_id: Optional[int] = None
    scenario_details: List[ScenarioDetails]


class Promotions(
    PromoDateRange
):
    class Promo(TypedDict):
        promo_id: int
        promo_name: str
        start_date: datetime
        end_date: datetime

    class Simulation(BaseModel):
        simulator_action: Literal[tuple(promo_constants.SIMULATOR_ACTIONS)]
        promo_id: int
        hierarchy_level: object
        reoptimise_flag: int
        scenario_data: Optional[List[ScenarioData]]

    guid: Optional[str] = None
    action: Literal[tuple(promo_constants.ACTION_VALUES)]
    simulator_action: Optional[Literal[tuple(promo_constants.SIMULATOR_ACTIONS)]] = None
    screen_type: Optional[Literal[tuple(promo_constants.SCREEN_VALUES)]] = None
    promo_id: Optional[int] = None
    promo_name: Optional[str] = None
    step_count: Optional[int] = None
    promo_status: Optional[str] = None
    metrics: Optional[PlaceholderMetrics] = None
    view_by_options: Optional[Literal[tuple(promo_constants.VIEW_BY_OPTIONS)]] = None
    product_selections: Optional[ProductSelection] = None
    product_exclusions: Optional[ProductExclusion] = None
    customer_selections: Optional[CustomerSelection] = None
    store_selections: Optional[StoreSelection] = None
    promo_target: Optional[PromoTarget] = None
    source: Optional[str] = None
    scenario_id: Optional[int] = None
    offer_comment: Optional[str] = None
    list_of_promos: Optional[List[int]] = None
    discount_level_id: Optional[int] = None
    discount_type: Optional[str] = None
    priority_number: Optional[int] = None
    report_file_name: Optional[str] = None
    report_type: Optional[str] = None
    report_name: Optional[str] = None
    pg_sg_edit: Optional[bool] = None
    aggregation: Optional[decision_dashboard_models.Aggregation] = None
    aggregation_level: Optional[int] = None
    promo_ids: Optional[List[int]] = []
    target_currency_id: Optional[int] = None
    event_ids: Optional[List[int]] = []
    priority_numbers: Optional[common_models.PriorityNumbers] = []

    # Fields from PromosCopy
    promos: Optional[List[Promo]] = None
    simulations: Optional[Simulation] = None

    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    show_partially_overlapping_events: bool = True


class PromoResults(
    PromoDateRange
):
    view_by_options: Literal[tuple(promo_constants.VIEW_BY_OPTIONS)]
    promo_ids: list[int] = []
    event_ids: list[int] = []
    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    target_currency_id: Optional[int] = None
    show_partially_overlapping_events: bool = True
    priority_numbers: common_models.PriorityNumbers 


class ValidDiscountingLevel(
    product_models.ProductHierarchy, product_models.StoreHierarchy
):
    promo_id: int


class Tiles(
    PromoDateRange
):
    promo_ids: list[int] = []
    screen_type: Optional[ScreenTypeEnum] = None
    event_ids: list[int] = []
    target_currency_id: Optional[int] = None
    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    show_partially_overlapping_events: bool = True
    priority_numbers: common_models.PriorityNumbers 


class CalculateGrossMargin(PromoDateRange):
    guid: Optional[str] = None
    metrics: Optional[PlaceholderMetrics] = None


class DeletePromotion(BaseModel):
    promo_id: List[int]


class InlineEditData(BaseModel):
    promo_id: int
    promo_name: Optional[str] = None
    event_id: int
    discount_level: str
    offer_type: str
    offer_value: str


class ResimulateAndSave(BaseModel):
    promo_data: List[InlineEditData]


class InValidPromoProducts(BaseModel):
    event_id: int
    event_ad_type: int
    promos: List[int]



class UpdatePromoAndIsHero(BaseModel):
    event_id: int
    promo_id: int
    promo_name: Optional[str] = None
    is_hero_items: Optional[List[int]] = None
    is_hero_promo: Optional[int] = None


class EventChangeAudit(BaseModel):
    channel_type: List[int]
    ad_type: List[int]
    start_date: date
    end_date: date
    product_hierarchy: product_models.ProductHierarchy

    @field_validator(*["start_date", "end_date"], mode="before")
    def check_valid_date_format(cls, calendar_date):
        if isinstance(calendar_date, str):
            return string_format_date_time(calendar_date)
        return calendar_date



class CheckBGProcess(BaseModel):
    promo_id: list[int]


class SaveDiscountRules(BaseModel):
    promo_id: int
    discount_level_id: int
    discount_type: str
    priority_number: Optional[int] = None


class GetDiscountRules(BaseModel):
    promo_id: int


class UpdateScenarioName(BaseModel):
    scenario_id: int
    scenario_name: str


class GetTiers(BaseModel):
    promo_id: int


class DeleteTier(BaseModel):
    tier_id: int
    tier_name: str
    promo_id: int
    promo_name: str


class TierManagement(BaseModel):
    class TierDetails(BaseModel):
        offer_x_value: Optional[Union[int, float]] = None
        offer_x_type: str
        offer_y_value: Optional[Union[int, float]] = None
        offer_y_type: str
        offer_z_value: Optional[Union[int, float]] = None
        offer_z_type: Optional[str] = None

    promo_id: int
    tier_id: Optional[int] = None
    tier_name: str
    sub_tier_count: int
    tier_offer_type: str
    tier_offer_type_id: int
    tier_information: List[TierDetails]


class ResimulateOffers(BaseModel):
    guid: str
    promo_id_list: List[int]


class PromoInfo(BaseModel):
    promo_id: int
    promo_name: Optional[str] = None
    comments: Optional[str] = None


class EditPromosInfo(BaseModel):
    promos: List[PromoInfo]


class SimulationResultsDetails(BaseModel):
    promo_id: int
    aggregation: int
    target_currency_id: Optional[int] = None


class AcknowledgeNotification(BaseModel):
    notification_ids: List[int]
    mark_all_as_read: bool


class ToFinalizeModel(BaseModel):
    promo_ids: List[int] = []


class SaveExmd(BaseModel):
    promo_id: int
    template_id: str
    price_filter_id: int
    folder_id: int
    sfcc_ats_check_id: Optional[str] = None
    sfcc_dropship_id: Optional[str] = None
    promo_code: Optional[str] = None
    receipt_text_eng: Optional[str] = None
    receipt_text_french: Optional[str] = None
    sfcc_pip_text: Optional[str] = None
    sfcc_tender_type_promo_msg: Optional[str] = None
    sfcc_pip_customer_group: Optional[str] = None
    sfcc_customer_group: Optional[str] = None
    sfcc_pip_rank: Optional[int] = None
    sfcc_rank: Optional[int] = None


class OfferParams(BaseModel):
    promo_id: Optional[int] = None
    simulator_action: Optional[str] = None


class OverrideForecast(BaseModel):
    promo_id: int
    scenario_id: int = 0
    old_sales_units: float
    old_baseline_sales_units: float
    new_sales_units: Optional[float] = None
    new_baseline_sales_units: Optional[float] = None
    override_method: str = "multiplier"
    reason: Optional[int] = None
    comment: Optional[str] = None
    screen_type: Optional[str] = "step3"
    from_stacking_view: Optional[bool] = False
    target_currency_id: Optional[int] = None

class SetDefaultForScenario(BaseModel):
    promo_id: int
    scenario_id: int
    default: Literal['overridden','original']
    from_stacking_view: Optional[bool] = False


class InvokeOptModel(TypedDict):
    action_log_id: NotRequired[int]
    promo_ids: NotRequired[List[Any]]
    promo_id: NotRequired[int]
    scenario_id: NotRequired[int]
    user_id: int
    guid: NotRequired[str]
    action: str
    parameters: dict


class MessageSet(TypedDict):
    success: str
    failure: str


class CopyPromoDetails(BaseModel):
    promo_id: int
    start_date: common_models.CustomDate
    end_date: common_models.CustomDate
    promo_name: str
    event_id: EventIdType
    new_promo_name: str


class CopyPromosRequest(BaseModel):
    promos: list[CopyPromoDetails]


class CreatePromoRequest(BaseModel):
    promo_name: str
    start_date: common_models.CustomDate
    end_date: common_models.CustomDate
    event_id: EventIdType

    model_config = {
        "validate_default": True
    }
    

class UpdatePromoStep0Request(CreatePromoRequest):
    promo_id: int


class CreatePlaceholderPromoRequest(CreatePromoRequest):
    promo_name: str
    start_date: common_models.CustomDate
    end_date: common_models.CustomDate
    metrics: PlaceholderMetrics

class UpdatePlaceholderPromoRequest(CreatePlaceholderPromoRequest):
    promo_id: int


class InsertPromoStep1Request(BaseModel):
    promo_id: int
    event_id: int
    product_selections: ProductSelection
    store_selections: StoreSelection
    product_exclusions: ProductExclusion
    customer_selections: CustomerSelection
    step_count:int = 1


class UpdatePromoStep1Request(InsertPromoStep1Request):
    pass

class UpdatePromoStep2Request(BaseModel):
    promo_id: int
    product_discount_level: list[int] = Field(default_factory=lambda: [promo_constants.OVERALL])
    store_discount_level: list[int] = Field(default_factory=lambda: [promo_constants.OVERALL])
    customer_discount_level: list[int] = Field(default_factory=list)
    discount_type: str
    discount_type_id: int
    priority_number: int
    vendor_funding: VendorFunding = Field(default_factory=VendorFunding)
    upto_percent: UptoPercentDiscountType = Field(default_factory=UptoPercentDiscountType)
    
    @model_validator(mode='before')
    def validate_upto_percent(cls, values):
        if values.get('discount_type_id') == OfferTypeEnum.UPTO_X_PERCENT_OFF.id_value:
            upto_percent = values.get('upto_percent')
            if upto_percent is None:
                raise CommonException(f"Upto Percent Details are required for discount_type_id {OfferTypeEnum.UPTO_X_PERCENT_OFF.display_name}")                
            
            for field, display_name in [
                    ("min_upto_percent", "Minimum upto percentage"),
                    ("max_upto_percent", "Maximum upto percentage"),
                    ("products_on_max_upto_percent", "Products on maximum upto percentage")
                ]:
                value = upto_percent.get(field)
                if value < 0 or value > 100:
                    raise CommonException(f"{display_name} must be between 0 and 100 inclusive, got {value}")                
        return values

class DiscountsData(BaseModel):
    row_id: int
    scenario_data: list[ScenarioDetails]
    _scenario_data: dict[int,ScenarioDetails] = PrivateAttr(default_factory=dict)

    @model_serializer
    def serialize_model(self) -> dict[str,Any]:
        return {
            "row_id": self.row_id,
            "scenario_data": self._scenario_data
        }



class NewScenarioDetails(BaseModel):
    scenario_id: int
    scenario_name: str
    scenario_order_id: int
    
class SimulatePromoRequest(BaseModel):
    promo_id: int
    discounts_data: list[DiscountsData]
    new_scenario_details: list[NewScenarioDetails] = []
    include_temporary_saved_changes: bool = False
    _updated_scenario_ids: list[int] = PrivateAttr(default_factory=list)


class ApproveScenarioRequest(BaseModel):
    promo_id: int
    scenario_id: int
    offer_comment: str
    source: str 

class OptimisePromoRequest(BaseModel):
    promo_id: int
    promo_target: PromoTarget

class FinalisePromoRequest(BaseModel):
    promo_ids: list[int]

class DeletePromosRequest(BaseModel):
    promo_ids: list[int]

class WithdrawPromosRequest(BaseModel):
    promo_ids: list[int]

class ExecutionApprovePromosRequest(BaseModel):
    promo_ids: list[int]


class EventPromoDetails(BaseModel):
    event_id: int
    promo_ids: list[int]

class OptServerCallback(BaseModel):
    user_id: int
    action_log_id: int
    source: Optional[str] = None
    action: str
    promo_id: Optional[int] = None
    status: Optional[int] = None
    new_promo_ids: Optional[list[int]] = None
    success_promo_ids: list[int]= []
    failure_promo_ids: list[int] = []
    message: Optional[str] = None
    scenario_id: Optional[list[int]] = None
    promo_ids: Optional[list[int]] = None
    event_id: Optional[int] = None
    refreshed_promo_ids: Optional[list[int]] = None
    event_details: list[EventPromoDetails] = Field(default_factory=list)



class GetPromos(common_models.BaseFilters):
    is_finalized:bool = False


class CopyOfferEventMapping(BaseModel):
    promo_id: int
    event_id: int

class ValidateCopyOffers(BaseModel):
    offer_event_mappings: list[CopyOfferEventMapping]

class TimeEstimate(BaseModel):
    promo_id: int
    discounts_data: list[DiscountsData]
    include_temporary_saved_changes: bool = False
    action: str

class GetPromoDiscounts(BaseModel):
    promo_id: int
    limit: int = 100
    page: int = 1
    sort_key: str = "id"
    filters: list[common_models.TableFilters] = []
    sort_order: Literal["asc", "desc"] = "asc"

    """
      will be true after doing a bulk edit
      (
      temporarily saved values after bulk edit will be sent in the response
      instead of original values
      )
      will be false if no bulk edit is performed
      (
      no temporarily saved values will be considered when sending the response
      )
    """
    include_temporary_saved_changes: bool = False

    """
      all the rows which were updated manually before clicking on save or
       simulate will be sent in unsaved_scenario_data key.
    """
    unsaved_scenario_data: list[DiscountsData] = []

    """
       new scenario details will be sent if user has clicked on add new scenario
       and hasn't simulated yet
    """
    new_scenario_details: list[NewScenarioDetails] = [] 


class BaseStep3Filters(BaseModel):
    """
      a new session id will be created whenever user lands in step3 page and 
      will be used until he simulates.
      once the user moves away from the page the session id won't be valid anymore 
      a new session_id should be created if he comes back to step 3.
    """
    session_id: str

    promo_id: int
    selected_rows: list[int] = []
    unselected_rows: list[int] = []
    filters: list[common_models.TableFilters] = []
    include_temporary_saved_changes: bool = False




class BulkEditDiscountsRequest(BaseStep3Filters):
    discounts_data: Optional[list[DiscountsData]] = None
    bulk_edit_data: list[ScenarioDetails]
    

    
class ApproveIaScenario(BaseModel):
    promo_id: int
    scenario_id: int

    
class CopyDiscounts(BaseStep3Filters):
    source: int
    source_scenario_id: int
    target: int
    target_scenario_id: int
    

class GetPromoStep3SimulationResults(BaseModel):
    promo_id: int
    target_currency_id: Optional[int] = None


class GetPriorityNumber(BaseModel):
    promo_id: int
    product_level_ids: list[int]
    store_level_ids: list[int]
    
class GetOfferTypesByPriority(BaseModel):
    promo_id: int
    product_level_ids: list[int]
    store_level_ids: list[int]
    priority_number: int

class GetMinMaxPriceValue(BaseStep3Filters):
    pass
    
    