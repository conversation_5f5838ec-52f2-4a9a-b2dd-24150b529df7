DATE_RANGE_CONDITION = "start_date <= TO_DATE({end_date}, 'YYYY-MM-DD') and end_date >= TO_DATE({start_date}, 'YYYY-MM-DD')"

DATE_RANGE_CONDITION_INCLUSIVE = "start_date >= TO_DATE({start_date}, 'YYYY-MM-DD') and end_date <= TO_DATE({end_date}, 'YYYY-MM-DD')"

# list of general actions performed
ACTION_GET = "get"
ACTION_CREATE = "create"
ACTION_EDIT = "edit"
ACTION_APPROVE = "approve"
ACTION_FINALIZE = "finalize"
ACTION_EXECUTION_APPROVED = "execution_approved"
ACTION_DELETE = "delete"
ACTION_WITHDRAW = "withdraw"
ACTION_COPY = "copy"
ACTION_SIMULATE = "simulation"
ACTION_OPTIMISE = "optimise"
ACTION_OVERRIDE = "override"
ACTION_REFRESH_METRICS = "refresh_metrics"
ACTION_APPROVE_SCENARIO = "approve_scenario"    # used for opt call only
ACTION_EVENT_RESIMULATE = "event_resimulate"
ACTION_EVENT_EDIT = "event_edit"
ACTION_EVENT_EDIT_OFFER_REFRESH = "event_edit_offer_refresh"
ACTION_EVENT_RESIMULATE_OFFER_REFRESH = "event_resimulate_offer_refresh"
ACTION_VALUES = [
    ACTION_CREATE,
    ACTION_EDIT,
    ACTION_APPROVE,
    ACTION_DELETE,
    ACTION_GET,
    ACTION_COPY,
    ACTION_FINALIZE,
    ACTION_EXECUTION_APPROVED,
    ACTION_WITHDRAW,
]

# for downstream integration
ACTION_EXECUTE = "execute"
ACTION_ARCHIVE = "Archive"

DECISION_DASHBOARD_REPORT_EXTENSIVE_DATA = "decision_dashboard_report_extensive_data"
CALENDAR_VIEW_REPORT_EXTENSIVE_DATA = "calendar_view_report_extensive_data"
WORKBENCH_REPORT_EXTENSIVE_DATA = "workbench_report_extensive_data"
PROMO_SIMULATOR_REPORT_EXTENSIVE_DATA = "simulator_report_extensive_data"
PROMO_SKU_PRICE_DATA = "SKU Price Data"

SCREEN_DECISION_DASHBOARD = "decision_dashboard"
SCREEN_CALENDAR_VIEW = "calendar_view"
SCREEN_WORKBENCH = "workbench"
SCREEN_MARKETING_CALENDAR = "marketing_calendar"
SCREEN_STEP0 = "Step0"
SCREEN_STEP1 = "Step1"
SCREEN_STEP2 = "Step2"
SCREEN_STEP3 = "Step3"

SCREEN_TIER_MANAGEMENT = "tier_management"

SCREEN_VALUES = [SCREEN_DECISION_DASHBOARD, SCREEN_CALENDAR_VIEW, SCREEN_WORKBENCH]

# list of view by options on promotion results (graph)
WEEK = "week"
MONTH = "month"
QUARTER = "quarter"
VIEW_BY_OPTIONS = [WEEK, MONTH, QUARTER]

# list of simulation actions
SIMULATE = "resimulate"  # REMOVE THIS
OPTIMISE = "optimise"
BULK_RESIMULATE = "bulk_resimulate"
SIMULATOR_ACTIONS = [SIMULATE, OPTIMISE, BULK_RESIMULATE]

RESIMULATION = USER_SCENARIO = "resimulation"

RECOMMENDATION_TYPE_IDS = {
    RESIMULATION : 30,
    OPTIMISE: 31
}

# promotion statues
DRAFT = "draft"
PLACEHOLDER = "placeholder"
SIM_ENABLED = "simulation_enabled"
COMPLETED = "completed"
SENT_FOR_APPROVAL = "sent_for_approval"
APPROVED = "approved"
REJECTED = "rejected"
DELETED = "deleted"

PROMO_STATUS = {
    PLACEHOLDER: -1,
    DRAFT: 0,
    SIM_ENABLED: 1,
    COMPLETED: 2,
    SENT_FOR_APPROVAL: 3,
    APPROVED: 4,
    REJECTED: 5,
    DELETED: 6,
}

DRAFT_COPIED = "Draft/Copied"
TO_FINALIZE = "To Finalize"
FINALIZED = "Finalized"
ARCHIVED = "Archived"
EXECUTION = "Execution"
EXECUTION_APPROVAL = "Execution Approval"

OFFER_STATUS = {
    DRAFT_COPIED: 0,
    TO_FINALIZE: 2,
    FINALIZED: 4,
    ARCHIVED: 6,
    EXECUTION: 8,
    PLACEHOLDER: -1,
}


PERFORMANCE_COMPARISON_VALUE = 5

PRODUCT_HIERARCHY_CID_MAPPING = {
    "l0_ids": "l0_cid",
    "l1_ids": "l1_cid",
    "l2_ids": "l2_cid",
    "l3_ids": "l3_cid",
    "l4_ids": "l4_cid",
    "l5_ids": "l5_cid",
    "brand": "brand_cid",
    "brand_ids": "brand_cid",
    "lifecycle_indicator": "lifecycle_indicator_id",
}

# Promo Product Exclusion constants
EXCLUSION_DEPT_ID_COLUMN = "DEPARTMENT_ID"
EXCLUSION_DIV_ID_COLUMN = "DIVISION_ID"
EXCLUSION_GROUP_ID_COLUMN = "GROUP_ID"
EXCLUSION_CLASS_ID_COLUMN = "CLASS_ID"
EXCLUSION_SUBCLASS_ID_COLUMN = "SUBCLASS_ID"
EXCLUSION_SKU_ID_COLUMN = "SVS_ID"
EXCLUSION_MFG_ID_COLUMN = "MFG_ID"
EXCLUSION_LIFECYCLE_COLUMN = "LIFECYCLE_INDICATOR"
EXCLUSION_UPLOAD_FILE_COLUMNS_LIST = [
    EXCLUSION_DEPT_ID_COLUMN,
    EXCLUSION_DIV_ID_COLUMN,
    EXCLUSION_GROUP_ID_COLUMN,
    EXCLUSION_CLASS_ID_COLUMN,
    EXCLUSION_SUBCLASS_ID_COLUMN,
    EXCLUSION_SKU_ID_COLUMN,
    EXCLUSION_MFG_ID_COLUMN,
]


# CID mapping
L0_CID_COLUMN = "l0_cid"
L1_CID_COLUMN = "l1_cid"
L2_CID_COLUMN = "l2_cid"
L3_CID_COLUMN = "l3_cid"
L4_CID_COLUMN = "l4_cid"
L5_CID_COLUMN = "l5_cid"
BRAND_CID_COLUMN = "brand_cid"

# CUQ mapping
L0_CUQ_COLUMN = "l0_cuq"
L1_CUQ_COLUMN = "l1_cuq"
L2_CUQ_COLUMN = "l2_cuq"
L3_CUQ_COLUMN = "l3_cuq"
L4_CUQ_COLUMN = "l4_cuq"
L5_CUQ_COLUMN = "l5_cuq"
BRAND_CUQ_COLUMN = "brand"


L0_ID_KEY = "l0_id"
L1_ID_KEY = "l1_id"
L2_ID_KEY = "l2_id"
L3_ID_KEY = "l3_id"
L4_ID_KEY = "l4_id"
L5_ID_KEY = "l5_id"
BRAND_ID_KEY = "brand_id"
LIFECYCLE_INDICATOR_KEY = "lifecycle_indicator_id"


# EXCLUSION_TYPES
TYPE_PRODUCT_HIERARCHY = 1
TYPE_COPY_PASTE = 2
TYPE_PRODUCT_GROUP = 3
TYPE_UPLOAD = 4

SPECIAL_CHARACTER_MAPPING = {
    "__ia_char_01": "'",
    "__ia_char_02": '"',
    "__ia_char_03": "/",
    "__ia_char_04": "\\",
    "__ia_char_05": "`",
    "__ia_char_06": "~",
    "__ia_char_07": "!",
    "__ia_char_08": "@",
    "__ia_char_09": "#",
    "__ia_char_10": "$",
    "__ia_char_11": "%",
    "__ia_char_12": "^",
    "__ia_char_13": "&",
    "__ia_char_14": "*",
    "__ia_char_15": "(",
    "__ia_char_16": ")",
    "__ia_char_19": "=",
    "__ia_char_20": "+",
    "__ia_char_21": "{",
    "__ia_char_22": "}",
    "__ia_char_23": "[",
    "__ia_char_24": "]",
    "__ia_char_25": "|",
    "__ia_char_26": ":",
    "__ia_char_27": ";",
    "__ia_char_28": "<",
    "__ia_char_29": ">",
    "__ia_char_30": ",",
    "__ia_char_31": ".",
    "__ia_char_32": "?",
}

NO_RESPONSE = "No response"

WHERE_CONDITION = "where "
AND = " and "

APPLICATION_VARIABLE = "application/json"

OVERALL = -200

CSV_FILE_FORMAT = "csv"

DOWNSTREAM_INTEGRATION_PROCESS_NAME = "Downstream Integration"

DOWNSTREAM_FOLDER_NAME = "promo"
