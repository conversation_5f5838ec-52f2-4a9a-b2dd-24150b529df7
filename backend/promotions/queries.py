CHECK_PROMO_EXISTENCE_BY_NAME_QUERY = """
    select 
        promo_id 
    from  
        price_promo.promo_master 
    where 
        TRIM(name) = {promo_name}
        {promo_id_condition}
        {date_condition}
        and status != {archive_status}
"""

CHECK_PROMO_BACKGROUND_PROCESS_STATUS_QUERY = """
    SELECT 
        pm.promo_id,
        pm.name as promo_name,
        talm.screen_name as screen,
        talm.processing_action as processing_action
    FROM 
        price_promo.promo_master pm
        INNER JOIN price_promo.tb_action_log_master talm 
        ON pm.promo_id = ANY(talm.promo_ids)
        AND pm.is_under_processing = talm.processing_status
    WHERE 
        pm.is_under_processing = 1 
        AND pm.promo_id = ANY({promo_ids}::int[])
    group by 1, 2, 3, 4;
"""

APPROVE_SCENARIO_QUERY = """
DO $$
BEGIN
    -- Removing finalized data
    DELETE FROM price_promo.ps_recommended_finalized WHERE promo_id = {promo_id};
    DELETE FROM price_promo.ps_recommended_finalized_agg WHERE promo_id = {promo_id};
    update 
        price_promo.promo_master
    set
        status=2,
        last_approved_scenario_id = {scenario_id},
        offer_comment = {offer_comment},
        recommendation_type_id = {recommendation_type_id},
        updated_at=now(), 
        updated_by={user_id}
    WHERE promo_id in ({promo_id});
END $$;
"""


EXECUTION_APPROVED_PROMO_QUERY = """
    update 
        price_promo.promo_master
    set
        status=8, 
        updated_at=now(), 
        updated_by={user_id}
    WHERE promo_id in ({promo_ids});
"""

WITHDRAW_PROMO_QUERY = """
    update 
        price_promo.promo_master
    set
        status=4, 
        updated_at=now(), 
        updated_by={user_id}
    WHERE promo_id in ({promo_ids});
"""


DELETE_PROMOS_AND_ITS_METRICS_QUERY = """
    delete from price_promo.ps_recommended_finalized_agg prfa
    using price_promo.promo_master pm
    where 
        prfa.promo_id = pm.promo_id
        and pm.status = -1
        and prfa.promo_id in ({promo_ids});

    delete from price_promo.ps_recommended_finalized_stack_agg prfsa
    using price_promo.promo_master pm
    where 
        pm.status = -1
        and pm.promo_id = any(array[{promo_ids}])
        and prfsa.promo_ids && array[{promo_ids}];

    update 
        price_promo.promo_master
    set
        status=6, 
        updated_at=now(), 
        updated_by={user_id}
    WHERE promo_id in ({promo_ids});
"""

# The below query will be used to Fetch all the Promos grouped by promo_id's.
FETCH_PROMOS_WORKBENCH = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated,
            pm.is_overridden_scenario_finalized
        FROM
            price_promo.promo_master pm
        WHERE
            is_deleted = 0
            AND {date_range_where_clause}
    ),
    filtered_promos_cte as (
        select distinct promo_id from price_promo.promo_product_hierarchy
        where hierarchy_id in (
            select hierarchy_id from price_promo.tb_product_hierarchy_lifecycle_combination 
            {product_hierarchical_where_clause}        
        )
        AND promo_id IN (select promo_id from promo_master_filtered_cte)
        UNION
        select promo_id from promo_master_filtered_cte where products_count = 0 
    ),
    eligible_store_promos_cte AS (
        SELECT promo_id
        FROM promo_master_filtered_cte
        WHERE store_selection_type_id = 1
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_hierarchy psh ON pmfc.promo_id = psh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_sg_hierarchy pssgh ON pmfc.promo_id = pssgh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
    ),
    intersected_eligible_promos_cte AS (
        SELECT promo_id FROM filtered_promos_cte
        INTERSECT
        SELECT promo_id FROM eligible_store_promos_cte
    ),
    final_eligible_promos_cte AS (
        SELECT promo_id FROM promo_master_filtered_cte WHERE status_id = -1
        UNION 
        select promo_id from price_promo.tb_placeholder_targets tpt where promo_id in (select promo_id from promo_master_filtered_cte)
        UNION
        SELECT pmfc.promo_id AS promo_id FROM promo_master_filtered_cte pmfc WHERE step_count = 0 and status_id in (0, 6)
        UNION
        SELECT promo_id FROM intersected_eligible_promos_cte
    ),
    override_reason_comment AS(
        SELECT
            tpof.promo_id,
            tpof.comment as override_comment,
            tor.reason as override_reason
        FROM
            price_promo.tb_promo_override_forecast tpof
        LEFT JOIN price_promo.tb_override_reason tor
        ON tpof.reason = tor.id
        WHERE
            (tpof.promo_id, tpof.scenario_id) IN (
		    SELECT promo_id, coalesce(last_approved_scenario_id, 0) as scenario_id
		    FROM price_promo.promo_master
		    WHERE promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
		)
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.promo_name,
            pmfc.start_date,
            pmfc.end_date,
            pmfc.created_by,
            pmfc.status_id,
            STRING_AGG(psc.status_name::text, ', ') AS status,
            pmfc.step_count,
            pmfc.offer_comment,
            pmfc.products_count,
            pmfc.stores_count,
            pmfc.product_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN pstc.product_selection_sub_type::text IS NOT NULL THEN CONCAT(pstc.product_selection_type::text, '-', pstc.product_selection_sub_type::text)
                    ELSE pstc.product_selection_type::text
                END,
                ', '
            ) AS product_selection_type,
            pmfc.store_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN sstc.store_selection_sub_type::text IS NOT NULL THEN CONCAT(sstc.store_selection_type::text, '-', sstc.store_selection_sub_type::text)
                    ELSE sstc.store_selection_type::text
                END,
                ', '
            ) AS store_selection_type,
            pmfc.exclusion_selection_type_id, 
            case 
            	when exclusion_selection_type_id = 1 then 'hierarchy based exclusion '
            	when exclusion_selection_type_id = 2 then 'product based exclusion '
            	when exclusion_selection_type_id = 3 then 'product group based exclusion '
            	when exclusion_selection_type_id = 4 then 'file upload based exclusion '
            end as exclusion_selection_type,
            pmfc.customer_type_id,
            STRING_AGG(tctc.customer_type::text, ', ') AS customer_type,
            pmfc.offer_distribution_channel_id,
            STRING_AGG(todcc.channel::text, ', ') AS offer_distribution_channel,
            pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id,
            STRING_AGG(tasm.name, ', ') AS recommendation_type,
            pmfc.is_under_processing,
            pmfc.is_auto_resimulated,
            pmfc.is_overridden_scenario_finalized
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
        LEFT JOIN
            price_promo.promo_status_config psc ON pmfc.status_id = psc.status_id
        LEFT JOIN
            price_promo.product_selection_type_config pstc ON pmfc.product_selection_type_id = pstc.id
        LEFT JOIN
            price_promo.store_selection_type_config sstc ON pmfc.store_selection_type_id = sstc.id
        LEFT JOIN
            price_promo.tb_customer_type_config tctc ON pmfc.customer_type_id = tctc.id
        LEFT JOIN
            price_promo.tb_offer_distributor_channel_config todcc ON pmfc.offer_distribution_channel_id = todcc.id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pmfc.recommendation_type_id = tasm.id
        GROUP BY
            pmfc.promo_id, pmfc.promo_name, pmfc.start_date, pmfc.end_date, pmfc.created_by, pmfc.status_id, pmfc.step_count,
            pmfc.offer_comment, pmfc.products_count, pmfc.stores_count, pmfc.product_selection_type_id, pmfc.store_selection_type_id, pmfc.exclusion_selection_type_id,
            pmfc.customer_type_id, pmfc.offer_distribution_channel_id, pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id, pmfc.is_under_processing, pmfc.is_auto_resimulated, pmfc.is_overridden_scenario_finalized
    ),
    promo_rules_cte AS (
        SELECT
            pr.promo_id,
            pr.discount_level AS discount_level_id,
            dlc.discount_level_value AS discount_level,
            pr.discount_type,
            tasm.display_name as display_discount_type,
            pr.units_target as sales_units_target,
            pr.revenue_target,
            pr.gross_margin_target as margin_target
        FROM
            price_promo.ps_rules pr
        LEFT JOIN
            price_promo.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
        LEFT JOIN
            metaschema.tb_app_sub_master tasm ON pr.discount_type = tasm.name
        WHERE
            pr.promo_id IN (SELECT DISTINCT promo_id FROM final_eligible_promos_cte)
    ),
    original_cte AS (
        SELECT
            fe.promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_contribution_margin_percent,
            MIN(offer_type_combined_display_name) AS original_discount,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_performance
        FROM
            final_eligible_promos_cte fe 
        INNER JOIN
            price_promo.ps_recommended_finalized_agg pa using(promo_id)
        GROUP BY
            fe.promo_id
    ),
    stacked_original_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_stack_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_stack_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_stack_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_stack_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_stack_contribution_margin_percent,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_stack_performance
        FROM
            price_promo.ps_recommended_finalized_stack_agg pa
        INNER JOIN
            final_eligible_promos_cte fe ON fe.promo_id = any(pa.promo_ids)
        GROUP BY
            promo_id
    ),
    finalized_scenarios_cte AS (
        SELECT 
            fep.promo_id,
            pof.is_default
        FROM 
            final_eligible_promos_cte fep
        LEFT JOIN 
            price_promo.promo_master pm ON fep.promo_id = pm.promo_id
        LEFT JOIN 
            price_promo.tb_promo_override_forecast pof ON fep.promo_id = pof.promo_id AND coalesce(pm.last_approved_scenario_id,0) = pof.scenario_id
    ),
    finalized_cte AS (
        SELECT
            sfsc.promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))
                        ELSE (SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0)) END)::NUMERIC, 2) AS finalized_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_contribution_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))
                        ELSE (SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0)) END)::NUMERIC, 2) AS finalized_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN MIN(override.offer_type_combined_display_name)
                ELSE MIN(original.offer_type_combined_display_name)
            END AS finalized_discount,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))
                        ELSE (SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0)) END)::DECIMAL * 100::DECIMAL, 2) AS finalized_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_agg original ON sfsc.promo_id = original.promo_id
        LEFT JOIN 
            price_promo.ps_recommended_finalized_override_agg override ON sfsc.promo_id = override.promo_id and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    stacked_finalized_cte AS (
        SELECT
            promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_stack_baseline_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_stack_contribution_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
                ELSE ROUND((SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
            END AS finalized_stack_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_agg original ON sfsc.promo_id = ANY(original.promo_ids)
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_override_agg override ON sfsc.promo_id = ANY(override.promo_ids) and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    ia_recc_cte AS (
        SELECT
            promo_id,
            MIN(offer_type_combined_display_name) AS ia_recc_discount
        FROM
            price_promo.ps_recommended_ia_projected_agg pripa
        WHERE
            promo_id IN (SELECT DISTINCT promo_id FROM final_eligible_promos_cte)
        GROUP BY
            promo_id
    ),
    actualized_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS actualized_sales_units,
            ROUND(SUM(baseline_sales_units)::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(SUM(incremental_sales_units)::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS actualized_revenue,
            ROUND(SUM(baseline_revenue)::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(SUM(incremental_revenue)::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS actualized_margin,
            ROUND(SUM(baseline_margin)::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(SUM(incremental_margin)::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN SUM(baseline_margin) IS NULL OR SUM(baseline_margin) = 0 THEN NULL
                ELSE ROUND((SUM(incremental_margin) / ABS(SUM(baseline_margin)))::DECIMAL * 100::DECIMAL, 2)
            END AS performance
        FROM
            price_promo.ps_recommended_actuals_agg praa
        WHERE
            promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
        GROUP BY
            promo_id
    )
    SELECT
        pmc.promo_id,
        pmc.promo_name AS promo_name,
        pmc.start_date,
        pmc.end_date,
        um.name AS created_by,
        pmc.offer_comment,
        pmc.status_id,
        pmc.status,
        pmc.step_count,
        pmc.products_count,
        pmc.stores_count,
        pmc.product_selection_type_id,
        pmc.product_selection_type,
        pmc.store_selection_type_id,
        pmc.store_selection_type,
        pmc.exclusion_selection_type_id,
        pmc.exclusion_selection_type,
        pmc.customer_type_id,
        pmc.customer_type,
        pmc.offer_distribution_channel_id,
        pmc.offer_distribution_channel,
        pmc.last_approved_scenario_id,
        pmc.recommendation_type_id,
        pmc.recommendation_type,
        pmc.is_under_processing,
        pmc.is_auto_resimulated,
        case when pmc.is_overridden_scenario_finalized = true then 1 else 0 end as is_overridden,
        orcc.override_comment,
        orcc.override_reason,
        --
        prc.discount_level_id,
        prc.discount_level,
        display_discount_type AS discount_type,
        --
        prc.sales_units_target AS target_sales_units,
        prc.revenue_target AS target_revenue,
        prc.margin_target AS target_margin,
        --
        COALESCE(acc.performance, fc.finalized_performance) AS performance,
        ---
        iarc.ia_recc_discount,
        --
        fc.finalized_sales_units,
        fc.finalized_baseline_sales_units as baseline_sales_units,
        fc.finalized_revenue,
        fc.finalized_baseline_revenue as baseline_revenue,
        fc.finalized_margin,
        fc.finalized_baseline_margin as baseline_margin,
        fc.finalized_margin_percent,
        fc.finalized_promo_spend,
        fc.finalized_contribution_revenue,
        fc.finalized_contribution_margin,
        fc.finalized_contribution_margin_percent,
        fc.finalized_discount,
        --
        sfc.finalized_stack_sales_units,
        sfc.finalized_stack_baseline_sales_units as stack_baseline_sales_units,
        sfc.finalized_stack_revenue,
        sfc.finalized_stack_baseline_revenue as stack_baseline_revenue,
        sfc.finalized_stack_margin,
        sfc.finalized_stack_baseline_margin as stack_baseline_margin,
        sfc.finalized_stack_margin_percent,
        sfc.finalized_stack_promo_spend,
        sfc.finalized_stack_contribution_revenue,
        sfc.finalized_stack_contribution_margin,
        sfc.finalized_stack_contribution_margin_percent,
        --
        oc.original_sales_units,
        oc.original_revenue,
        oc.original_margin,
        oc.original_margin_percent,
        oc.original_promo_spend,
        oc.original_contribution_revenue,
        oc.original_contribution_margin,
        oc.original_contribution_margin_percent,
        oc.original_discount,
        --
        soc.original_stack_sales_units,
        soc.original_stack_revenue,
        soc.original_stack_margin,
        soc.original_stack_margin_percent,
        soc.original_stack_promo_spend,
        soc.original_stack_contribution_revenue,
        soc.original_stack_contribution_margin,
        soc.original_stack_contribution_margin_percent
    FROM 
        promo_master_details_cte pmc
    LEFT JOIN 
        promo_rules_cte prc ON pmc.promo_id = prc.promo_id
    LEFT JOIN
        override_reason_comment orcc ON pmc.promo_id = orcc.promo_id
    LEFT JOIN 
        finalized_cte fc ON pmc.promo_id = fc.promo_id
    LEFT JOIN 
        stacked_finalized_cte sfc ON pmc.promo_id = sfc.promo_id
    LEFT JOIN 
        ia_recc_cte iarc ON pmc.promo_id = iarc.promo_id
    LEFT JOIN
        original_cte oc ON pmc.promo_id = oc.promo_id
    LEFT JOIN
        stacked_original_cte soc ON pmc.promo_id = soc.promo_id
    LEFT JOIN 
        actualized_cte acc ON pmc.promo_id = acc.promo_id
    LEFT JOIN 
        global.user_master um ON pmc.created_by = um.user_code
    order by promo_id;
"""

FETCH_PROMOS_WORKBENCH_BY_PROMO_IDS = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated,
            pm.is_overridden_scenario_finalized
        FROM
            {promo_schema}.promo_master pm
        where pm.promo_id in {promo_ids}
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.promo_name,
            pmfc.start_date,
            pmfc.end_date,
            pmfc.created_by,
            pmfc.status_id,
            STRING_AGG(psc.status_name::text, ', ') AS status,
            pmfc.step_count,
            pmfc.offer_comment,
            pmfc.products_count,
            pmfc.stores_count,
            pmfc.product_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN pstc.product_selection_sub_type::text IS NOT NULL THEN CONCAT(pstc.product_selection_type::text, '-', pstc.product_selection_sub_type::text)
                    ELSE pstc.product_selection_type::text
                END,
                ', '
            ) AS product_selection_type,
            pmfc.store_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN sstc.store_selection_sub_type::text IS NOT NULL THEN CONCAT(sstc.store_selection_type::text, '-', sstc.store_selection_sub_type::text)
                    ELSE sstc.store_selection_type::text
                END,
                ', '
            ) AS store_selection_type,
            pmfc.exclusion_selection_type_id, 
            case 
            	when exclusion_selection_type_id = 1 then 'hierarchy based exclusion '
            	when exclusion_selection_type_id = 2 then 'product based exclusion '
            	when exclusion_selection_type_id = 3 then 'product group based exclusion '
            	when exclusion_selection_type_id = 4 then 'file upload based exclusion '
            end as exclusion_selection_type,
            pmfc.customer_type_id,
            STRING_AGG(tctc.customer_type::text, ', ') AS customer_type,
            pmfc.offer_distribution_channel_id,
            STRING_AGG(todcc.channel::text, ', ') AS offer_distribution_channel,
            pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id,
            STRING_AGG(tasm.name, ', ') AS recommendation_type,
            pmfc.is_under_processing,
            pmfc.is_auto_resimulated,
            pmfc.is_overridden_scenario_finalized
        FROM
            promo_master_filtered_cte pmfc
        LEFT JOIN
            {promo_schema}.promo_status_config psc ON pmfc.status_id = psc.status_id
        LEFT JOIN
            {promo_schema}.product_selection_type_config pstc ON pmfc.product_selection_type_id = pstc.id
        LEFT JOIN
            {promo_schema}.store_selection_type_config sstc ON pmfc.store_selection_type_id = sstc.id
        LEFT JOIN
            {promo_schema}.tb_customer_type_config tctc ON pmfc.customer_type_id = tctc.id
        LEFT JOIN
            {promo_schema}.tb_offer_distributor_channel_config todcc ON pmfc.offer_distribution_channel_id = todcc.id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pmfc.recommendation_type_id = tasm.id
        GROUP BY
            pmfc.promo_id, pmfc.promo_name, pmfc.start_date, pmfc.end_date, pmfc.created_by, pmfc.status_id, pmfc.step_count,
            pmfc.offer_comment, pmfc.products_count, pmfc.stores_count, pmfc.product_selection_type_id, pmfc.store_selection_type_id, pmfc.exclusion_selection_type_id,
            pmfc.customer_type_id, pmfc.offer_distribution_channel_id, pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id, pmfc.is_under_processing, pmfc.is_auto_resimulated, pmfc.is_overridden_scenario_finalized
    ),
    promo_rules_cte AS (
        SELECT
            pr.promo_id,
            pr.discount_level AS discount_level_id,
            dlc.discount_level_value AS discount_level,
            pr.discount_type,
            tasm.display_name as display_discount_type,
            pr.units_target as sales_units_target,
            pr.revenue_target,
            pr.gross_margin_target as margin_target
        FROM
            {promo_schema}.ps_rules pr
        LEFT JOIN
            {promo_schema}.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
        LEFT JOIN
            metaschema.tb_app_sub_master tasm ON pr.discount_type = tasm.name
        WHERE
            pr.promo_id IN (SELECT DISTINCT promo_id FROM promo_master_filtered_cte)
    ),
    original_cte AS (
        SELECT
            fe.promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_contribution_margin_percent,
            MIN(offer_type_combined_display_name) AS original_discount,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_performance
        FROM
            promo_master_filtered_cte fe 
        INNER JOIN
            price_promo.ps_recommended_finalized_agg pa using(promo_id)
        GROUP BY
            fe.promo_id
    ),
    stacked_original_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_stack_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_stack_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_stack_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_stack_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_stack_contribution_margin_percent,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_stack_performance
        FROM
            price_promo.ps_recommended_finalized_stack_agg pa
        INNER JOIN
            promo_master_filtered_cte fe ON fe.promo_id = any(pa.promo_ids)
        GROUP BY
            promo_id
    ),
    finalized_scenarios_cte AS (
        SELECT 
            fep.promo_id,
            pof.is_default
        FROM 
            promo_master_filtered_cte fep
        LEFT JOIN 
            price_promo.promo_master pm ON fep.promo_id = pm.promo_id
        LEFT JOIN 
            price_promo.tb_promo_override_forecast pof ON fep.promo_id = pof.promo_id AND coalesce(pm.last_approved_scenario_id,0) = pof.scenario_id
    ),
    finalized_cte AS (
        SELECT
            sfsc.promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))
                        ELSE (SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0)) END)::NUMERIC, 2) AS finalized_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_contribution_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))
                        ELSE (SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0)) END)::NUMERIC, 2) AS finalized_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN MIN(override.offer_type_combined_display_name)
                ELSE MIN(original.offer_type_combined_display_name)
            END AS finalized_discount,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))
                        ELSE (SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0)) END)::DECIMAL * 100::DECIMAL, 2) AS finalized_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_agg original ON sfsc.promo_id = original.promo_id
        LEFT JOIN 
            price_promo.ps_recommended_finalized_override_agg override ON sfsc.promo_id = override.promo_id and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    stacked_finalized_cte AS (
        SELECT
            promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_stack_baseline_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_stack_contribution_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
                ELSE ROUND((SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
            END AS finalized_stack_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_agg original ON sfsc.promo_id = ANY(original.promo_ids)
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_override_agg override ON sfsc.promo_id = ANY(override.promo_ids) and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    ia_recc_cte AS (
        SELECT
            promo_id,
            MIN(offer_type_combined_display_name) AS ia_recc_discount
        FROM
            price_promo.ps_recommended_ia_projected_agg pripa
        WHERE
            promo_id IN (SELECT DISTINCT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            promo_id
    ),
    actualized_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS actualized_sales_units,
            ROUND(SUM(baseline_sales_units)::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(SUM(incremental_sales_units)::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS actualized_revenue,
            ROUND(SUM(baseline_revenue)::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(SUM(incremental_revenue)::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS actualized_margin,
            ROUND(SUM(baseline_margin)::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(SUM(incremental_margin)::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN SUM(baseline_margin) IS NULL OR SUM(baseline_margin) = 0 THEN NULL
                ELSE ROUND((SUM(incremental_margin) / ABS(SUM(baseline_margin)))::DECIMAL * 100::DECIMAL, 2)
            END AS performance
        FROM
            {promo_schema}.ps_recommended_actuals_agg praa
        WHERE
            promo_id IN (SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            promo_id
    )
    SELECT
        pmc.promo_id,
        pmc.promo_name AS promo_name,
        pmc.start_date,
        pmc.end_date,
        um.name AS created_by,
        pmc.offer_comment,
        pmc.status_id,
        pmc.status,
        pmc.step_count,
        pmc.products_count,
        pmc.stores_count,
        pmc.product_selection_type_id,
        pmc.product_selection_type,
        pmc.store_selection_type_id,
        pmc.store_selection_type,
        pmc.exclusion_selection_type_id,
        pmc.exclusion_selection_type,
        pmc.customer_type_id,
        pmc.customer_type,
        pmc.offer_distribution_channel_id,
        pmc.offer_distribution_channel,
        pmc.last_approved_scenario_id,
        pmc.recommendation_type_id,
        pmc.recommendation_type,
        pmc.is_under_processing,
        pmc.is_auto_resimulated,
        --
        prc.discount_level_id,
        prc.discount_level,
        display_discount_type AS discount_type,
        --
        prc.sales_units_target AS target_sales_units,
        prc.revenue_target AS target_revenue,
        prc.margin_target AS target_margin,
        --
        COALESCE(acc.performance, fc.finalized_performance) AS performance,
        ---
        iarc.ia_recc_discount AS ia_recc_discount,
        --
        fc.finalized_sales_units,
        fc.finalized_baseline_sales_units as baseline_sales_units,
        fc.finalized_revenue,
        fc.finalized_baseline_revenue as baseline_revenue,
        fc.finalized_margin,
        fc.finalized_baseline_margin as baseline_margin,
        fc.finalized_margin_percent,
        fc.finalized_promo_spend,
        fc.finalized_contribution_revenue,
        fc.finalized_contribution_margin,
        fc.finalized_contribution_margin_percent,
        fc.finalized_discount,
        --
        sfc.finalized_stack_sales_units,
        sfc.finalized_stack_baseline_sales_units as stack_baseline_sales_units,
        sfc.finalized_stack_revenue,
        sfc.finalized_stack_baseline_revenue as stack_baseline_revenue,
        sfc.finalized_stack_margin,
        sfc.finalized_stack_baseline_margin as stack_baseline_margin,
        sfc.finalized_stack_margin_percent,
        sfc.finalized_stack_promo_spend,
        sfc.finalized_stack_contribution_revenue,
        sfc.finalized_stack_contribution_margin,
        sfc.finalized_stack_contribution_margin_percent,
        --
        oc.original_sales_units,
        oc.original_revenue,
        oc.original_margin,
        oc.original_margin_percent,
        oc.original_promo_spend,
        oc.original_contribution_revenue,
        oc.original_contribution_margin,
        oc.original_contribution_margin_percent,
        oc.original_discount,
        --
        soc.original_stack_sales_units,
        soc.original_stack_revenue,
        soc.original_stack_margin,
        soc.original_stack_margin_percent,
        soc.original_stack_promo_spend,
        soc.original_stack_contribution_revenue,
        soc.original_stack_contribution_margin,
        soc.original_stack_contribution_margin_percent
    FROM 
        promo_master_details_cte pmc
    LEFT JOIN 
        promo_rules_cte prc ON pmc.promo_id = prc.promo_id
    LEFT JOIN 
        finalized_cte fc ON pmc.promo_id = fc.promo_id
    LEFT JOIN 
        stacked_finalized_cte sfc ON pmc.promo_id = sfc.promo_id
    LEFT JOIN 
        ia_recc_cte iarc ON pmc.promo_id = iarc.promo_id
    LEFT JOIN
        original_cte oc ON pmc.promo_id = oc.promo_id
    LEFT JOIN
        stacked_original_cte soc ON pmc.promo_id = soc.promo_id
    LEFT JOIN 
        actualized_cte acc ON pmc.promo_id = acc.promo_id
    LEFT JOIN 
        global.user_master um ON pmc.created_by = um.user_code
    order by promo_id;
"""

FETCH_PROMOS_WORKBENCH_DOWNLOAD = """
    SELECT
        promo_name AS "Offer Name",
        event_name AS "Event Name",
        country_name AS "Country",
        start_date AS "Start Date",
        end_date AS "End Date",
        created_by AS "Created By",
        status AS "Offer Status",
        offer_comment AS "Comments",
        currency_symbol AS "Currency",
        products_count AS "#Products",
        override_comment as "Override Comment",
        override_reason as "Override Reason",
        exclusion_selection_type AS "Exclusion Selection Type",
        performance AS "Performance",
        discount_type AS "Discount Type",
        discount_level AS "Discount Level - Products",
        finalized_promo_spend AS "Offer $",
        finalized_sales_units AS "Finalized Sales Units",
        finalized_baseline_sales_units AS "Baseline Sales Units",
        target_sales_units AS "Target Sales Units",
        finalized_revenue AS "Finalized Revenue",
        finalized_baseline_revenue AS "Baseline Revenue",
        target_revenue AS "Target Revenue",
        finalized_margin AS "Finalized Margin$",
        finalized_margin_percent AS "Finalized Margin%",
        finalized_baseline_margin AS "Baseline Margin",
        finalized_st_percent as "Finalized ST %",
        target_margin AS "Target Margin",
        product_selection_type AS "Product Selection Type",
        customer_type AS "Customer Type",
        offer_distribution_channel AS "Offer Distribution Channel",
        original_promo_spend AS "Original Offer $",
        original_sales_units AS "Original Sales Units",
        original_revenue AS "Original Revenue",
        original_margin AS "Original Margin$",
        original_margin_percent AS "Original Margin%",
        --
        original_stack_sales_units as "Original Stacked Sales Units",
        original_stack_revenue as "Original Stacked Revenue",
        original_stack_margin as "Original Stacked Margin",
        original_stack_margin_percent as "Original Stacked Margin %",
        original_stack_promo_spend as "Original Stacked Promo Spend",
        --
        finalized_stack_sales_units as "Finalized Stacked Sales Units",
        finalized_stack_baseline_sales_units as "Finalized Stacked Baseline Sales Units",
        finalized_stack_revenue as "Finalized Stacked Revenue",
        finalized_stack_baseline_revenue  as "Finalized Stacked Baseline Revenue",
        finalized_stack_margin as "Finalized Stacked Margin",
        finalized_stack_baseline_margin as "Finalized Stacked Baseline Margin",
        finalized_stack_margin_percent as "Finalized Stacked Margin %",
        finalized_stack_promo_spend as "Finalized Stacked Promo Spend"
    FROM 
        price_promo.fn_fetch_workbench_table_data({request_payload}::jsonb);
"""

FETCH_PROMOS_DECISION_DASHBOARD = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated,
            pm.is_overridden_scenario_finalized
        FROM
            price_promo.promo_master pm
        WHERE
            is_deleted = 0
            AND status IN (-1, 4, 8)
            AND {date_range_where_clause}
    ),
    filtered_promos_cte AS (
        SELECT DISTINCT promo_id
        FROM price_promo.promo_product_hierarchy
        WHERE hierarchy_id IN (
            SELECT hierarchy_id
            FROM price_promo.tb_product_hierarchy_lifecycle_combination
            {product_hierarchical_where_clause}  
        )
        AND promo_id IN (select promo_id from promo_master_filtered_cte)
        UNION
        select promo_id from promo_master_filtered_cte where products_count = 0 
    ),
    eligible_store_promos_cte AS (
        SELECT distinct promo_id
        FROM promo_master_filtered_cte
        WHERE store_selection_type_id = 1
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_hierarchy psh ON pmfc.promo_id = psh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_sg_hierarchy pssgh ON pmfc.promo_id = pssgh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
    ),
    intersected_eligible_promos_cte AS (
        SELECT promo_id
        FROM filtered_promos_cte
        INTERSECT
        SELECT promo_id
        FROM eligible_store_promos_cte
    ),
    final_eligible_promos_cte AS (
        SELECT promo_id FROM promo_master_filtered_cte  WHERE status_id = -1
        UNION
        SELECT promo_id FROM intersected_eligible_promos_cte
    ),
    override_reason_comment AS(
        SELECT
            tpof.promo_id,
            tpof.comment as override_comment,
            tor.reason as override_reason
        FROM
            price_promo.tb_promo_override_forecast tpof
        LEFT JOIN price_promo.tb_override_reason tor
        ON tpof.reason = tor.id
        WHERE
            (tpof.promo_id, tpof.scenario_id) IN (
		    SELECT promo_id, coalesce(last_approved_scenario_id, 0) as scenario_id
		    FROM price_promo.promo_master
		    WHERE promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
		)
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.promo_name,
            pmfc.start_date,
            pmfc.end_date,
            pmfc.created_by,
            pmfc.status_id,
            STRING_AGG(psc.status_name::text, ', ') AS status,
            pmfc.step_count,
            pmfc.offer_comment,
            pmfc.products_count,
            pmfc.stores_count,
            pmfc.product_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN pstc.product_selection_sub_type::text IS NOT NULL THEN CONCAT(pstc.product_selection_type::text, '-', pstc.product_selection_sub_type::text)
                    ELSE pstc.product_selection_type::text
                END,
                ', '
            ) AS product_selection_type,
            pmfc.store_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN sstc.store_selection_sub_type::text IS NOT NULL THEN CONCAT(sstc.store_selection_type::text, '-', sstc.store_selection_sub_type::text)
                    ELSE sstc.store_selection_type::text
                END,
                ', '
            ) AS store_selection_type,
            pmfc.exclusion_selection_type_id, 
            CASE 
                WHEN pmfc.exclusion_selection_type_id = 1 THEN 'hierarchy based exclusion '
                WHEN pmfc.exclusion_selection_type_id = 2 THEN 'product based exclusion '
                WHEN pmfc.exclusion_selection_type_id = 3 THEN 'product group based exclusion '
                WHEN pmfc.exclusion_selection_type_id = 4 THEN 'file upload based exclusion '
            END AS exclusion_selection_type,
            pmfc.customer_type_id,
            STRING_AGG(tctc.customer_type::text, ', ') AS customer_type,
            pmfc.offer_distribution_channel_id,
            STRING_AGG(todcc.channel::text, ', ') AS offer_distribution_channel,
            pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id,
            STRING_AGG(tasm.name, ', ') AS recommendation_type,
            pmfc.is_under_processing,
            pmfc.is_auto_resimulated,
            pmfc.is_overridden_scenario_finalized
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
        LEFT JOIN
            price_promo.promo_status_config psc ON pmfc.status_id = psc.status_id
        LEFT JOIN
            price_promo.product_selection_type_config pstc ON pmfc.product_selection_type_id = pstc.id
        LEFT JOIN
            price_promo.store_selection_type_config sstc ON pmfc.store_selection_type_id = sstc.id
        LEFT JOIN
            price_promo.tb_customer_type_config tctc ON pmfc.customer_type_id = tctc.id
        LEFT JOIN
            price_promo.tb_offer_distributor_channel_config todcc ON pmfc.offer_distribution_channel_id = todcc.id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pmfc.recommendation_type_id = tasm.id
        GROUP BY
            pmfc.promo_id, pmfc.promo_name, pmfc.start_date, pmfc.end_date, pmfc.created_by, pmfc.status_id, 
            pmfc.step_count, pmfc.offer_comment, pmfc.products_count, pmfc.stores_count, 
            pmfc.product_selection_type_id, pmfc.store_selection_type_id, pmfc.exclusion_selection_type_id, 
            pmfc.customer_type_id, pmfc.offer_distribution_channel_id, pmfc.last_approved_scenario_id, 
            pmfc.recommendation_type_id, pmfc.is_under_processing, pmfc.is_auto_resimulated, pmfc.is_overridden_scenario_finalized
    ),
    promo_rules_cte AS (
        SELECT
            pr.promo_id,
            pr.discount_level AS discount_level_id,
            dlc.discount_level_value AS discount_level
        FROM
            price_promo.ps_rules pr
        LEFT JOIN
            price_promo.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
        WHERE
            pr.promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
    ),
    promo_override_forecast_cte as (
        select
        tpof.promo_id,
        tpof.is_default as is_override_default
        from 
            price_promo.tb_promo_override_forecast tpof
        inner join
            price_promo.promo_master pm
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
    ),
    finalized_cte AS (
        WITH
            aggregated_data AS (
                SELECT
                    fa.promo_id,
                    case when tpof.is_override_default then sum(foa.margin) else SUM(fa.margin) end AS total_margin,
                    case when tpof.is_override_default then SUM(foa.revenue) else SUM(fa.revenue) end AS total_revenue,
                    case when tpof.is_override_default then SUM(foa.sales_units) else SUM(fa.sales_units) end AS total_sales_units,
                    case when tpof.is_override_default then SUM(foa.baseline_sales_units) else SUM(fa.baseline_sales_units) end AS total_baseline_sales_units,
                    case when tpof.is_override_default then SUM(foa.incremental_sales_units) else SUM(fa.incremental_sales_units) end AS total_incremental_sales_units,
                    case when tpof.is_override_default then SUM(foa.baseline_revenue) else SUM(fa.baseline_revenue) end AS total_baseline_revenue,
                    case when tpof.is_override_default then SUM(foa.incremental_revenue) else SUM(fa.incremental_revenue) end AS total_incremental_revenue,
                    case when tpof.is_override_default then SUM(foa.baseline_margin) else SUM(fa.baseline_margin) end AS total_baseline_margin,
                    case when tpof.is_override_default then SUM(foa.incremental_margin) else SUM(fa.incremental_margin) end AS total_incremental_margin,
                    case when tpof.is_override_default then SUM(foa.promo_spend) else SUM(fa.promo_spend) end AS total_promo_spend,
                    case when tpof.is_override_default then SUM(foa.contribution_revenue) else SUM(fa.contribution_revenue) end AS total_contribution_revenue,
                    case when tpof.is_override_default then SUM(foa.contribution_margin) else SUM(fa.contribution_margin) end AS total_contribution_margin,
                    case when tpof.is_override_default then MIN(foa.offer_type_combined_display_name) else MIN(fa.offer_type_combined_display_name) end as offer_type_combined_display_name,

                    case when coalesce(tpof.is_override_default,false) then SUM(fa.margin) else null end AS original_total_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.revenue) else null end AS original_total_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.sales_units) else null end AS original_total_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_sales_units) else null end AS original_total_baseline_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_sales_units) else null end AS original_total_incremental_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_revenue) else null end AS original_total_baseline_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_revenue) else null end AS original_total_incremental_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_margin) else null end AS original_total_baseline_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_margin) else null end AS original_total_incremental_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.promo_spend) else null end AS original_total_promo_spend,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.contribution_revenue) else null end AS original_total_contribution_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.contribution_margin) else null end AS original_total_contribution_margin,
                    case when coalesce(tpof.is_override_default,false) then MIN(fa.offer_type_combined_display_name) else null end as original_offer_type_combined_display_name

                FROM
                    price_promo.ps_recommended_finalized_agg fa
                left join
                    price_promo.ps_recommended_finalized_override_agg foa
                on fa.promo_id = foa.promo_id and fa.recommendation_date = foa.recommendation_date
                left join
                    promo_override_forecast_cte tpof
                on tpof.promo_id = fa.promo_id
                WHERE
                    fa.promo_id IN (
                        SELECT
                            promo_id
                        FROM
                            final_eligible_promos_cte
                    )
                GROUP BY
                    fa.promo_id,tpof.is_override_default
            )
        SELECT
            promo_id,
            ROUND(total_sales_units::DECIMAL, 2) AS finalized_sales_units,
            ROUND(total_baseline_sales_units::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(total_incremental_sales_units::DECIMAL, 2) AS finalized_incremental_sales_units,
            ROUND(total_revenue::DECIMAL, 2) AS finalized_revenue,
            ROUND(total_baseline_revenue::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(total_incremental_revenue::DECIMAL, 2) AS finalized_incremental_revenue,
            ROUND(total_margin::DECIMAL, 2) AS finalized_margin,
            ROUND(total_baseline_margin::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND(total_incremental_margin::DECIMAL, 2) AS finalized_incremental_margin,
            ROUND(total_promo_spend::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(total_contribution_revenue::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(total_contribution_margin::DECIMAL, 2) AS finalized_contribution_margin,
            offer_type_combined_display_name as finalized_discount,
            CASE
                WHEN total_revenue != 0 THEN ROUND((total_margin * 100 / total_revenue)::NUMERIC, 2)
                ELSE 0
            END AS finalized_margin_percent,
            CASE
                WHEN total_contribution_revenue != 0 THEN ROUND((total_contribution_margin * 100 / total_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS finalized_contribution_margin_percent,
            CASE
                WHEN total_baseline_margin IS NULL
                OR total_baseline_margin = 0 THEN NULL
                ELSE ROUND(
                    (
                        total_incremental_margin / ABS(total_baseline_margin)
                    )::DECIMAL * 100::DECIMAL,
                    2
                )
            END AS performance,

            ROUND(original_total_sales_units::DECIMAL, 2) AS original_sales_units,
            ROUND(original_total_baseline_sales_units::DECIMAL, 2) AS original_baseline_sales_units,
            ROUND(original_total_incremental_sales_units::DECIMAL, 2) AS original_incremental_sales_units,
            ROUND(original_total_revenue::DECIMAL, 2) AS original_revenue,
            ROUND(original_total_baseline_revenue::DECIMAL, 2) AS original_baseline_revenue,
            ROUND(original_total_incremental_revenue::DECIMAL, 2) AS original_incremental_revenue,
            ROUND(original_total_margin::DECIMAL, 2) AS original_margin,
            ROUND(original_total_baseline_margin::DECIMAL, 2) AS original_baseline_margin,
            ROUND(original_total_incremental_margin::DECIMAL, 2) AS original_incremental_margin,
            ROUND(original_total_promo_spend::DECIMAL, 2) AS original_promo_spend,
            ROUND(original_total_contribution_revenue::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(original_total_contribution_margin::DECIMAL, 2) AS original_contribution_margin,
            original_offer_type_combined_display_name as original_discount,
            CASE
                WHEN original_total_revenue != 0 THEN ROUND((original_total_margin * 100 / original_total_revenue)::NUMERIC, 2)
                ELSE 0
            END AS original_margin_percent,
            CASE
                WHEN original_total_contribution_revenue != 0 THEN ROUND((original_total_contribution_margin * 100 / original_total_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS original_contribution_margin_percent,
            CASE
                WHEN original_total_baseline_margin IS NULL
                OR original_total_baseline_margin = 0 THEN NULL
                ELSE ROUND(
                    (
                        original_total_incremental_margin / ABS(original_total_baseline_margin)
                    )::DECIMAL * 100::DECIMAL,
                    2
                )
            end as original_performance
        FROM
            aggregated_data
    ),
    finalized_stack_cte as (
        WITH
            aggregated_data AS (
                SELECT
                    fepc.promo_id,
                    case when tpof.is_override_default then sum(foa.margin) else SUM(fa.margin) end AS total_margin,
                    case when tpof.is_override_default then SUM(foa.revenue) else SUM(fa.revenue) end AS total_revenue,
                    case when tpof.is_override_default then SUM(foa.sales_units) else SUM(fa.sales_units) end AS total_sales_units,
                    case when tpof.is_override_default then SUM(foa.baseline_sales_units) else SUM(fa.baseline_sales_units) end AS total_baseline_sales_units,
                    case when tpof.is_override_default then SUM(foa.incremental_sales_units) else SUM(fa.incremental_sales_units) end AS total_incremental_sales_units,
                    case when tpof.is_override_default then SUM(foa.baseline_revenue) else SUM(fa.baseline_revenue) end AS total_baseline_revenue,
                    case when tpof.is_override_default then SUM(foa.incremental_revenue) else SUM(fa.incremental_revenue) end AS total_incremental_revenue,
                    case when tpof.is_override_default then SUM(foa.baseline_margin) else SUM(fa.baseline_margin) end AS total_baseline_margin,
                    case when tpof.is_override_default then SUM(foa.incremental_margin) else SUM(fa.incremental_margin) end AS total_incremental_margin,
                    case when tpof.is_override_default then SUM(foa.promo_spend) else SUM(fa.promo_spend) end AS total_promo_spend,
                    case when tpof.is_override_default then SUM(foa.contribution_revenue) else SUM(fa.contribution_revenue) end AS total_contribution_revenue,
                    case when tpof.is_override_default then SUM(foa.contribution_margin) else SUM(fa.contribution_margin) end AS total_contribution_margin,
                    case when tpof.is_override_default then MIN(foa.offer_type_combined_display_name) else MIN(fa.offer_type_combined_display_name) end as offer_type_combined_display_name,

                    case when coalesce(tpof.is_override_default,false) then SUM(fa.margin) else null end AS original_total_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.revenue) else null end AS original_total_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.sales_units) else null end AS original_total_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_sales_units) else null end AS original_total_baseline_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_sales_units) else null end AS original_total_incremental_sales_units,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_revenue) else null end AS original_total_baseline_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_revenue) else null end AS original_total_incremental_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.baseline_margin) else null end AS original_total_baseline_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.incremental_margin) else null end AS original_total_incremental_margin,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.promo_spend) else null end AS original_total_promo_spend,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.contribution_revenue) else null end AS original_total_contribution_revenue,
                    case when coalesce(tpof.is_override_default,false) then SUM(fa.contribution_margin) else null end AS original_total_contribution_margin

                FROM
                    final_eligible_promos_cte fepc
                left join 
                    price_promo.ps_recommended_finalized_stack_agg fa
                on fepc.promo_id = any(fa.promo_ids)
                left join
                    price_promo.ps_recommended_finalized_stack_override_agg foa
                on fa.promo_ids = foa.promo_ids and fa.recommendation_date = foa.recommendation_date
                left join
                    promo_override_forecast_cte tpof
                on tpof.promo_id = fepc.promo_id
                GROUP BY
                    fepc.promo_id,tpof.is_override_default
            )
        SELECT
            promo_id,
            ROUND(total_sales_units::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(total_baseline_sales_units::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(total_incremental_sales_units::DECIMAL, 2) AS finalized_stack_incremental_sales_units,
            ROUND(total_revenue::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(total_baseline_revenue::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(total_incremental_revenue::DECIMAL, 2) AS finalized_stack_incremental_revenue,
            ROUND(total_margin::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(total_baseline_margin::DECIMAL, 2) AS finalized_stack_baseline_margin,
            ROUND(total_incremental_margin::DECIMAL, 2) AS finalized_stack_incremental_margin,
            ROUND(total_promo_spend::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(total_contribution_revenue::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(total_contribution_margin::DECIMAL, 2) AS finalized_stack_contribution_margin,
            CASE
                WHEN total_revenue != 0 THEN ROUND((total_margin * 100 / total_revenue)::NUMERIC, 2)
                ELSE 0
            END AS finalized_stack_margin_percent,
            CASE
                WHEN total_contribution_revenue != 0 THEN ROUND((total_contribution_margin * 100 / total_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS finalized_stack_contribution_margin_percent,
            CASE
                WHEN total_baseline_margin IS NULL
                OR total_baseline_margin = 0 THEN NULL
                ELSE ROUND(
                    (
                        total_incremental_margin / ABS(total_baseline_margin)
                    )::DECIMAL * 100::DECIMAL,
                    2
                )
            END AS stack_performance,

            ROUND(original_total_sales_units::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(original_total_baseline_sales_units::DECIMAL, 2) AS original_stack_baseline_sales_units,
            ROUND(original_total_incremental_sales_units::DECIMAL, 2) AS original_stack_incremental_sales_units,
            ROUND(original_total_revenue::DECIMAL, 2) AS original_stack_revenue,
            ROUND(original_total_baseline_revenue::DECIMAL, 2) AS original_stack_baseline_revenue,
            ROUND(original_total_incremental_revenue::DECIMAL, 2) AS original_stack_incremental_revenue,
            ROUND(original_total_margin::DECIMAL, 2) AS original_stack_margin,
            ROUND(original_total_baseline_margin::DECIMAL, 2) AS original_stack_baseline_margin,
            ROUND(original_total_incremental_margin::DECIMAL, 2) AS original_stack_incremental_margin,
            ROUND(original_total_promo_spend::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND(original_total_contribution_revenue::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(original_total_contribution_margin::DECIMAL, 2) AS original_stack_contribution_margin,
            CASE
                WHEN original_total_revenue != 0 THEN ROUND((original_total_margin * 100 / original_total_revenue)::NUMERIC, 2)
                ELSE 0
            END AS original_stack_margin_percent,
            CASE
                WHEN original_total_contribution_revenue != 0 THEN ROUND((original_total_contribution_margin * 100 / original_total_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS original_stack_contribution_margin_percent,
            CASE
                WHEN original_total_baseline_margin IS NULL
                OR original_total_baseline_margin = 0 THEN NULL
                ELSE ROUND(
                    (
                        original_total_incremental_margin / ABS(original_total_baseline_margin)
                    )::DECIMAL * 100::DECIMAL,
                    2
                )
            end as original_stack_performance
        FROM
            aggregated_data
    ),
    ia_recc_cte AS (
        SELECT
            promo_id,
            MIN(offer_type_combined_display_name) AS ia_recc_discount
        FROM
            price_promo.ps_recommended_ia_projected_agg pripa
        WHERE
            promo_id IN (SELECT DISTINCT promo_id FROM final_eligible_promos_cte)
        GROUP BY
            promo_id
    ),
    actualized_cte AS (
        WITH
            aggregated_data AS (
                SELECT
                    promo_id,
                    SUM(sales_units) AS total_sales_units,
                    SUM(baseline_sales_units) AS total_baseline_sales_units,
                    SUM(incremental_sales_units) AS total_incremental_sales_units,
                    SUM(revenue) AS total_revenue,
                    SUM(baseline_revenue) AS total_baseline_revenue,
                    SUM(incremental_revenue) AS total_incremental_revenue,
                    SUM(margin) AS total_margin,
                    SUM(baseline_margin) AS total_baseline_margin,
                    SUM(incremental_margin) AS total_incremental_margin,
                    SUM(contribution_revenue) AS total_contribution_revenue,
                    SUM(contribution_margin) AS total_contribution_margin,
                    SUM(promo_spend) AS total_promo_spend
                FROM
                    price_promo.ps_recommended_actuals_agg praa
                WHERE
                    promo_id IN (
                        SELECT
                            promo_id
                        FROM
                            final_eligible_promos_cte
                    )
                GROUP BY
                    promo_id
            )
        SELECT
            promo_id,
            ROUND(total_sales_units::DECIMAL, 2) AS actualized_sales_units,
            ROUND(total_baseline_sales_units::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(total_incremental_sales_units::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(total_revenue::DECIMAL, 2) AS actualized_revenue,
            ROUND(total_baseline_revenue::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(total_incremental_revenue::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(total_margin::DECIMAL, 2) AS actualized_margin,
            ROUND(total_baseline_margin::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(total_incremental_margin::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(total_contribution_revenue::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(total_contribution_margin::DECIMAL, 2) AS actualized_contribution_margin,
            ROUND(total_promo_spend::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN total_revenue != 0 THEN ROUND((total_margin * 100 / total_revenue)::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            CASE
                WHEN total_contribution_revenue != 0 THEN ROUND((total_contribution_margin * 100 / total_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN total_baseline_margin IS NULL
                OR total_baseline_margin = 0 THEN NULL
                ELSE ROUND(
                    (
                        total_incremental_margin / ABS(total_baseline_margin)
                    )::DECIMAL * 100::DECIMAL,
                    2
                )
            END AS performance
        FROM
            aggregated_data
    ),
    transaction_cte AS (
        SELECT
            promo_id,
            promo_offer_txn AS no_of_txn,
            promo_offer_units_per_txn AS units_per_txn,
            promo_offer_avg_basket_size AS avg_basket_size
        FROM
            price_promo.tb_promo_basketdetails
        WHERE
            promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
    )
    SELECT
        pmc.promo_id,
        pmc.promo_name AS promo_name,
        pmc.start_date,
        pmc.end_date,
        um.name AS created_by,
        pmc.offer_comment,
        pmc.status_id,
        pmc.status,
        pmc.step_count,
        pmc.products_count,
        pmc.stores_count,
        pmc.product_selection_type_id,
        pmc.product_selection_type,
        pmc.store_selection_type_id,
        pmc.store_selection_type,
        pmc.exclusion_selection_type_id,
        pmc.exclusion_selection_type,
        pmc.customer_type_id,
        pmc.customer_type,
        pmc.offer_distribution_channel_id,
        pmc.offer_distribution_channel,
        pmc.last_approved_scenario_id,
        pmc.recommendation_type_id,
        pmc.recommendation_type,
        pmc.is_under_processing,
        pmc.is_auto_resimulated,
        case when pmc.is_overridden_scenario_finalized = true then 1 else 0 end as is_overridden,
        orcc.override_comment,
        orcc.override_reason,
        --
        prc.discount_level_id,
        prc.discount_level,
        --
        fc.finalized_discount,
        iarc.ia_recc_discount,
        --
        COALESCE(acc.performance, fc.performance) AS performance,
        COALESCE(
            CASE
                WHEN pmc.products_count != 0 THEN ROUND((fc.finalized_revenue / pmc.products_count)::NUMERIC, 2)
                ELSE 0
            END,
            CASE
                WHEN pmc.products_count != 0 THEN ROUND((acc.actualized_revenue / pmc.products_count)::NUMERIC, 2)
                ELSE 0
            END
        ) AS revenue_per_style,
        txnc.no_of_txn,
        txnc.units_per_txn,
        txnc.avg_basket_size,
        ---
        acc.actualized_promo_spend,
        fc.finalized_promo_spend,
        --
        fc.finalized_sales_units AS finalized_sales_units,
        fc.finalized_baseline_sales_units AS baseline_sales_units,
        fc.finalized_incremental_sales_units AS finalized_incremental_sales_units,
        acc.actualized_incremental_sales_units AS actualized_incremental_sales_units,
        acc.actualized_sales_units AS actualized_sales_units,

        fc.finalized_revenue AS finalized_revenue,
        fc.finalized_baseline_revenue AS baseline_revenue,
        fc.finalized_incremental_revenue AS finalized_incremental_revenue,
        acc.actualized_incremental_revenue AS actualized_incremental_revenue,
        acc.actualized_revenue AS actualized_revenue,

        fc.finalized_margin AS finalized_margin,
        fc.finalized_baseline_margin AS baseline_margin,
        fc.finalized_incremental_margin AS finalized_incremental_margin,
        acc.actualized_incremental_margin AS actualized_incremental_margin,
        acc.actualized_margin AS actualized_margin,

        fc.finalized_margin_percent AS finalized_margin_percent,
        acc.actualized_margin_percent AS actualized_margin_percent,
        
        fc.finalized_contribution_revenue AS finalized_contribution_revenue,
        fc.finalized_contribution_margin AS finalized_contribution_margin,
        acc.actualized_contribution_revenue AS actualized_contribution_revenue,
        acc.actualized_contribution_margin AS actualized_contribution_margin,
        fc.finalized_contribution_margin_percent AS finalized_contribution_margin_percent,
        acc.actualized_contribution_margin_percent AS actualized_contribution_margin_percent,

        fsc.finalized_stack_promo_spend,
        fsc.finalized_stack_sales_units ,
        fsc.finalized_stack_baseline_sales_units as stack_baseline_sales_units,
        fsc.finalized_stack_incremental_sales_units ,
        fsc.finalized_stack_revenue ,
        fsc.finalized_stack_baseline_revenue as stack_baseline_revenue,
        fsc.finalized_stack_incremental_revenue ,
        fsc.finalized_stack_margin ,
        fsc.finalized_stack_baseline_margin as stack_baseline_margin,
        fsc.finalized_stack_incremental_margin ,
        fsc.finalized_stack_margin_percent ,
        fsc.finalized_stack_contribution_revenue ,
        fsc.finalized_stack_contribution_margin ,
        fsc.finalized_stack_contribution_margin_percent ,

        fc.original_sales_units,
        fc.original_revenue,
        fc.original_margin,
        fc.original_margin_percent,
        fc.original_promo_spend,
        fc.original_discount,
        fc.original_contribution_revenue,
        fc.original_contribution_margin,
        fc.original_contribution_margin_percent,

        fsc.original_stack_sales_units,
        fsc.original_stack_revenue,
        fsc.original_stack_margin,
        fsc.original_stack_margin_percent,
        fsc.original_stack_promo_spend,
        fsc.original_stack_contribution_revenue,
        fsc.original_stack_contribution_margin,
        fsc.original_stack_contribution_margin_percent
    FROM 
        promo_master_details_cte pmc
    LEFT JOIN
        override_reason_comment orcc ON pmc.promo_id = orcc.promo_id
    LEFT JOIN 
        promo_rules_cte prc ON pmc.promo_id = prc.promo_id
    LEFT JOIN 
        finalized_cte fc ON pmc.promo_id = fc.promo_id
    left join   
        finalized_stack_cte fsc on fsc.promo_id = pmc.promo_id
    left join 
        ia_recc_cte iarc on pmc.promo_id = iarc.promo_id    
    LEFT JOIN 
        actualized_cte acc ON pmc.promo_id = acc.promo_id
    LEFT JOIN
        transaction_cte txnc ON pmc.promo_id = txnc.promo_id
    LEFT JOIN 
        global.user_master um ON pmc.created_by = um.user_code
    ORDER BY 
        promo_id;
"""

FETCH_PROMOS_DECISION_DASHBOARD_DOWNLOAD = """
    SELECT
        promo_name AS "Offer Name",
        event_name AS "Event Name"
        {product_hierarchy_selection}
        {store_hierarchy_selection},
        currency_symbol AS "Currency",
        country_name AS "Country",
        start_date AS "Start Date",
        end_date AS "End Date",
        created_by AS "Created By",
        offer_comment AS "Comments",
        status AS "Status",
        actual_performance AS "Actual Performance",
        finalized_performance AS "Finalized Performance",
        ia_recc_discount AS "IA Recommended Discount",
        finalized_discount AS "Finalized Discount",
        products_count AS "Products",
        exclusion_selection_type AS "Exclusion Selection Type",
        avg_basket_size AS "Avg. Basket Size",
        units_per_txn AS "Units Per Transaction",
        no_of_txn AS "# of Transaction",
        override_comment as "Override Comment",
        override_reason as "Override Reason",
        finalized_revenue_per_style AS "Revenue Per SKU",
        finalized_stack_revenue_per_style as "Finalized Stack Revenue Per SKU",
        actual_revenue_per_style as "Actual Revenue Per SKU",
        actualized_promo_spend AS "Actualized Promo$",
        finalized_promo_spend AS "Finalized Promo$",
        actualized_sales_units AS "Actualized Sales Units",
        finalized_sales_units AS "IA Projected(Finalized) Sales Units",
        finalized_baseline_sales_units AS "Baseline Sales Units",
        finalized_incremental_sales_units AS "Incremental Sales Units",
        actualized_revenue AS "Actualized Revenue",
        finalized_revenue AS "IA Projected(Finalized) Revenue",
        finalized_baseline_revenue AS "Baseline Revenue",
        finalized_incremental_revenue AS "Incremental Revenue",
        actualized_margin AS "Actualized Margin$",
        finalized_margin AS "IA Projected(Finalized) Margin$",
        finalized_baseline_margin AS "Baseline Margin$",
        finalized_incremental_margin AS "Incremental Margin$",
        actualized_margin_percent AS "Actualized Margin%",
        finalized_margin_percent AS "Finalized Margin%",
        finalized_order_discount as "Finalized Order Discount",
        finalized_baseline_margin_percent as "Finalized Baseline Margin Percent",
        finalized_incremental_margin_percent as "Finalized Incremental Margin Percent",

        actualized_order_discount as "Actualized Order Discount",
        actualized_incremental_margin_percent as "Actualized Incremental Margin Percent",

        original_promo_spend as "Original Promo$",
        original_sales_units as "Original Sales Units",
        original_revenue as "Original Revenue",
        original_margin as "Original Margin$",
        original_margin_percent as "Original Margin%",
        finalized_contribution_revenue as "IA Projected(Finalized) Contribution Revenue",
        original_contribution_revenue as "Original Contribution Revenue",
        original_order_discount as "Original Order Discount",
        original_baseline_margin as "Original Baseline Margin$",
        original_baseline_sales_units as "Original Baseline Sales Units",
        original_baseline_revenue as "Original Baseline Revenue",
        original_baseline_margin_percent as "Original Baseline Margin Percent",
        original_incremental_margin_percent as "Original Incremental Margin Percent",

        finalized_stack_promo_spend as "Finalized Stack Promo$",
        finalized_stack_sales_units as "Finalized Stack Sales Units",
        finalized_stack_baseline_sales_units as "Finalized Stack Baseline Sales Units",
        finalized_stack_incremental_sales_units as "Finalized Stack Incremental Sales Units",
        finalized_stack_revenue as "Finalized Stack Revenue",
        finalized_stack_baseline_revenue as "Finalized Stack Baseline Revenue",
        finalized_stack_incremental_revenue  as "Finalized Stack Incremental Revenue",
        finalized_stack_margin as "Finalized Stack Margin$",
        finalized_stack_baseline_margin as "Finalized Stack Baseline Margin$",
        finalized_stack_incremental_margin as "Finalized Stack Incremental Margin$",
        finalized_stack_margin_percent as "Finalized Stack Margin%",
        finalized_stack_contribution_revenue as "Finalized Stack Contribution Revenue",
        finalized_stack_order_discount as "Finalized Stack Order Discount",
        finalized_stack_baseline_margin_percent as "Finalized Stack Baseline Margin Percent",
        finalized_stack_incremental_margin_percent as "Finalized Stack Incremental Margin Percent",

        original_stack_sales_units as "Original Stack Sales Units",
        original_stack_revenue as "Original Stack Revenue",
        original_stack_margin as "Original Stack Margin$",
        original_stack_margin_percent as "Original Stack Margin%",
        original_stack_promo_spend as "Original Stack Promo$",
        original_stack_contribution_revenue as "Original Stack Contribution Revenue",
        original_stack_order_discount as "Original Stack Order Discount",
        original_stack_baseline_margin as "Original Stack Baseline Margin$",
        original_stack_baseline_sales_units as "Original Stack Baseline Sales Units",
        original_stack_baseline_revenue as "Original Stack Baseline Revenue",
        original_stack_baseline_margin_percent as "Original Stack Baseline Margin Percent",
        original_stack_incremental_margin_percent as "Original Stack Incremental Margin Percent"
    FROM 
        price_promo.fn_fetch_decision_dashboard_table_data({request_payload}::jsonb, true);
"""

FETCH_DD_TILES_ID_BASED = """
    WITH final_eligible_promos_cte AS (
        SELECT 
            pm.promo_id
        FROM 
            price_promo.promo_master pm
        left join 
            price_promo.ps_rules pr on pm.promo_id = pr.promo_id
        WHERE 
            is_deleted = 0
            AND status IN (-1, 4, 8)
            {ids_where_str}
            {priority_number_where_str}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from final_eligible_promos_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    )
    SELECT
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        jsonb_build_object(
            'sales_units', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_sales_units),
                jsonb_build_object('baseline', finalized.finalized_baseline_sales_units),
                jsonb_build_object('incremental', finalized.finalized_incremental_sales_units),
                jsonb_build_object('actualized', COALESCE(actualized.actualized_sales_units))
            ],
            'revenue', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_revenue),
                jsonb_build_object('baseline', finalized.finalized_baseline_revenue),
                jsonb_build_object('incremental', finalized.finalized_incremental_revenue),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_revenue),
                jsonb_build_object('actualized', actualized.actualized_revenue)
            ],
            'gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin),
                jsonb_build_object('baseline', finalized.finalized_baseline_margin),
                jsonb_build_object('incremental', finalized.finalized_incremental_margin),
                jsonb_build_object('actualized', actualized.actualized_margin)
            ],
            'gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin_percent),
                jsonb_build_object('actualized', actualized.actualized_margin_percent),
                jsonb_build_object('baseline', finalized.baseline_margin_percent),
                jsonb_build_object('incremental', finalized.finalized_margin_percent - finalized.baseline_margin_percent)
            ],
            'contribution_gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin),
                jsonb_build_object('actualized', actualized.actualized_contribution_margin)
            ],
            'contribution_gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin_percent),
                jsonb_build_object('actualized', actualized.actualized_contribution_margin_percent)
            ],
            'promo_spend', ARRAY[
                jsonb_build_object('finalized', finalized.promo_spend),
                jsonb_build_object('actualized', actualized.actualized_promo_spend)
            ],
            'order_discount', ARRAY[
                jsonb_build_object('finalized', finalized.coupon_amount),
                jsonb_build_object('actualized', actualized.actualized_coupon_amount)
            ]
        ) AS metrics
    FROM
     (
        SELECT
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS finalized_baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_sales_units else fsa.incremental_sales_units end) AS finalized_incremental_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS finalized_baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS finalized_baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue else fsa.revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.margin else fsa.margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.revenue else fsa.revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_margin_percent,
            ROUND(SUM(case when pofc.is_override_default then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(SUM(case when pofc.is_override_default then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_margin,
            CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin else fsa.contribution_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_contribution_margin_percent,
             CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue else fsa.baseline_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin else fsa.baseline_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue else fsa.baseline_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS baseline_margin_percent,
            sum(case 
                when coalesce(pofc.is_override_default,false) then 
                    fsoa.promo_spend * pfr.planned_conversion_multiplier
                else
                    fsa.promo_spend * pfr.planned_conversion_multiplier
            end) as promo_spend,
            sum(case 
                when coalesce(pofc.is_override_default,false) then 
                    fsoa.coupon_amount * pfr.planned_conversion_multiplier
                else
                    fsa.coupon_amount * pfr.planned_conversion_multiplier
            end) as coupon_amount
        from
        promo_override_forecast_cte pofc
        inner join
        price_promo.ps_recommended_finalized_stack_agg fsa
        on pofc.promo_ids = fsa.promo_ids and pofc.recommendation_date = fsa.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa    
        on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ) finalized
    FULL OUTER JOIN (
        SELECT
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(baseline_sales_units) AS ia_recommended_baseline_sales_units,
            SUM(incremental_sales_units) AS ia_recommended_incremental_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(baseline_revenue * pfr.planned_conversion_multiplier) AS ia_recommended_baseline_revenue,
            SUM(incremental_revenue * pfr.planned_conversion_multiplier) AS ia_recommended_incremental_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin,
            SUM(baseline_margin * pfr.planned_conversion_multiplier) AS ia_recommended_baseline_margin,
            SUM(incremental_margin * pfr.planned_conversion_multiplier) AS ia_recommended_incremental_margin,
            ROUND(SUM(contribution_revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_revenue,
            ROUND(SUM(contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_margin
        FROM price_promo.ps_recommended_ia_projected_agg pripa
        inner join 
            global.planned_forex_rate pfr 
            on 
                pripa.recommendation_date = pfr.date 
                and pfr.source_currency_id = pripa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
    ) ia_recommended on true
    FULL OUTER JOIN (
        SELECT
            SUM(sales_units) AS actualized_sales_units,
            SUM(baseline_sales_units) AS actualized_baseline_sales_units,
            SUM(incremental_sales_units) AS actualized_incremental_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(baseline_revenue * afr.planned_conversion_multiplier) AS actualized_baseline_revenue,
            SUM(incremental_revenue * afr.planned_conversion_multiplier) AS actualized_incremental_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(baseline_margin * afr.planned_conversion_multiplier) AS actualized_baseline_margin,
            SUM(incremental_margin * afr.planned_conversion_multiplier) AS actualized_incremental_margin,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            sum(promo_spend * afr.planned_conversion_multiplier) as actualized_promo_spend,
            sum(coupon_amount * afr.planned_conversion_multiplier) as actualized_coupon_amount
        FROM price_promo.ps_recommended_actuals_stack_agg praa
        inner join 
            global.actual_forex_rate afr 
            on 
                praa.recommendation_date = afr.date 
                and afr.source_currency_id = praa.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where praa.promo_ids && array(SELECT promo_id FROM final_eligible_promos_cte)
    ) actualized on true
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
"""


FETCH_DD_TILES_FILTER_BASED = """
    WITH final_eligible_promos_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated
        FROM
            price_promo.promo_master pm
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping},
                {priority_numbers}
            )))
            AND status IN (-1, 4, 8)
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from final_eligible_promos_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    )
    SELECT
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        jsonb_build_object(
            'sales_units', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_sales_units),
                jsonb_build_object('baseline', finalized.finalized_baseline_sales_units),
                jsonb_build_object('incremental', finalized.finalized_incremental_sales_units),
                jsonb_build_object('actualized', COALESCE(actualized.actualized_sales_units))
            ],
            'revenue', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_revenue),
                jsonb_build_object('baseline', finalized.finalized_baseline_revenue),
                jsonb_build_object('incremental', finalized.finalized_incremental_revenue),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_revenue),
                jsonb_build_object('actualized', actualized.actualized_revenue)
            ],
            'gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin),
                jsonb_build_object('baseline', finalized.finalized_baseline_margin),
                jsonb_build_object('incremental', finalized.finalized_incremental_margin),
                jsonb_build_object('actualized', actualized.actualized_margin)
            ],
            'gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin_percent),
                jsonb_build_object('actualized', actualized.actualized_margin_percent),
                jsonb_build_object('baseline', finalized.baseline_margin_percent),
                jsonb_build_object('incremental', finalized.finalized_margin_percent - finalized.baseline_margin_percent)
            ],
            'contribution_gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin),
                jsonb_build_object('actualized', actualized.actualized_contribution_margin)
            ],
            'contribution_gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin_percent),
                jsonb_build_object('actualized', actualized.actualized_contribution_margin_percent)
            ],
            'promo_spend', ARRAY[
                jsonb_build_object('finalized', finalized.promo_spend),
                jsonb_build_object('actualized', actualized.actualized_promo_spend)
            ],
            'order_discount', ARRAY[
                jsonb_build_object('finalized', finalized.coupon_amount),
                jsonb_build_object('actualized', actualized.actualized_coupon_amount)
            ]
        ) AS metrics
    FROM
     (
        SELECT
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS finalized_baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_sales_units else fsa.incremental_sales_units end) AS finalized_incremental_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS finalized_baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS finalized_baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue else fsa.revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.margin else fsa.margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.revenue else fsa.revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_margin_percent,
            ROUND(SUM(case when pofc.is_override_default then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(SUM(case when pofc.is_override_default then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_margin,
            CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin else fsa.contribution_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_contribution_margin_percent,
            CASE
                WHEN SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue else fsa.baseline_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin else fsa.baseline_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue else fsa.baseline_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS baseline_margin_percent,
            sum(case 
                when coalesce(pofc.is_override_default,false) then 
                    fsoa.promo_spend
                else
                    fsa.promo_spend
            end) as promo_spend,
            sum(case 
                when coalesce(pofc.is_override_default,false) then 
                    fsoa.coupon_amount
                else
                    fsa.coupon_amount
            end) as coupon_amount
        from
        promo_override_forecast_cte pofc
        inner join
        price_promo.ps_recommended_finalized_stack_agg fsa
        on pofc.promo_ids = fsa.promo_ids and pofc.recommendation_date = fsa.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa    
        on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ) finalized
    FULL OUTER JOIN (
        SELECT
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(baseline_sales_units) AS ia_recommended_baseline_sales_units,
            SUM(incremental_sales_units) AS ia_recommended_incremental_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(baseline_revenue * pfr.planned_conversion_multiplier) AS ia_recommended_baseline_revenue,
            SUM(incremental_revenue * pfr.planned_conversion_multiplier) AS ia_recommended_incremental_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin,
            SUM(baseline_margin * pfr.planned_conversion_multiplier) AS ia_recommended_baseline_margin,
            SUM(incremental_margin * pfr.planned_conversion_multiplier) AS ia_recommended_incremental_margin,
            ROUND(SUM(contribution_revenue * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_revenue,
            ROUND(SUM(contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_margin
        FROM price_promo.ps_recommended_ia_projected_agg pripa
        inner join 
            global.planned_forex_rate pfr 
            on 
                pripa.recommendation_date = pfr.date 
                and pfr.source_currency_id = pripa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
    ) ia_recommended on true
    FULL OUTER JOIN (
        SELECT
            SUM(sales_units) AS actualized_sales_units,
            SUM(baseline_sales_units) AS actualized_baseline_sales_units,
            SUM(incremental_sales_units) AS actualized_incremental_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(baseline_revenue * afr.planned_conversion_multiplier) AS actualized_baseline_revenue,
            SUM(incremental_revenue * afr.planned_conversion_multiplier) AS actualized_incremental_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(baseline_margin * afr.planned_conversion_multiplier) AS actualized_baseline_margin,
            SUM(incremental_margin * afr.planned_conversion_multiplier) AS actualized_incremental_margin,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin * afr.planned_conversion_multiplier)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            sum(promo_spend) as actualized_promo_spend,
            sum(coupon_amount) as actualized_coupon_amount
        FROM price_promo.ps_recommended_actuals_stack_agg praa
        inner join 
            global.actual_forex_rate afr 
            on 
                praa.recommendation_date = afr.date 
                and afr.source_currency_id = praa.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where praa.promo_ids && array(SELECT promo_id FROM final_eligible_promos_cte)
    ) actualized on true
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
"""


QUARTERLY_PERFORMANCE_GRAPH_QUERY_BY_IDS = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date
        FROM
            price_promo.promo_master pm
        join
            price_promo.ps_rules pr on pm.promo_id = pr.promo_id
        WHERE
            is_deleted = 0
            AND status IN (-1, 4, 8)
            {ids_where_str}
            {priority_number_where_str}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    calendar_cte as (
        SELECT
            date_id,
            fiscal_year,
            fiscal_quarter
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_filtered_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_filtered_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_quarter
        FROM
            promo_master_filtered_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from promo_master_filtered_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        LEFT JOIN
            calendar_cte c ON fsa.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN ( SELECT promo_id FROM promo_master_filtered_cte )
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array( SELECT promo_id FROM promo_master_filtered_cte )
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_quarter,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,
            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown,
            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
	            WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS actualized_contribution_margin_percent
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_quarter
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_quarter)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_quarter)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_quarter)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FQ' || LPAD(fiscal_quarter::text, 2, '0') || ' ' || fiscal_year::text AS quarter,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,

        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,

        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm
    on
        tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_quarter;
"""


QUARTERLY_PERFORMANCE_GRAPH_QUERY = """
    WITH final_eligible_promos_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date,
            pm.status AS status_id,
            pm.store_selection_type AS store_selection_type_id
        FROM
            price_promo.promo_master pm
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping},
                {priority_numbers}
            )))
            AND status IN (-1, 4, 8)
    ),
    promo_master_filtered_cte AS (
        SELECT
            *
        FROM
            final_eligible_promos_cte
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.start_date,
            pmfc.end_date
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
    ),
    calendar_cte as (
    	SELECT
            date_id,
            fiscal_year,
            fiscal_quarter
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_details_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_details_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_quarter
        FROM
            final_eligible_promos_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa
        on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        LEFT JOIN
            calendar_cte c
            ON fsa.recommendation_date = c.date_id
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN (
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_quarter,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array(
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_quarter
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_quarter,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,
            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown,
            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
	            WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS actualized_contribution_margin_percent
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_quarter
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_quarter)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_quarter)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_quarter)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FQ' || LPAD(fiscal_quarter::text, 2, '0') || ' ' || fiscal_year::text AS quarter,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,

        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm
    on
        tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_quarter;
"""

MONTHLY_PERFORMANCE_GRAPH_QUERY_BY_IDS = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date
        FROM
            price_promo.promo_master pm
        join
            price_promo.ps_rules pr on pm.promo_id = pr.promo_id
        WHERE
            is_deleted = 0
            AND status IN (-1, 4, 8)
            {ids_where_str}
            {priority_number_where_str}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    calendar_cte as (
    	SELECT
            date_id,
            fiscal_year,
            fiscal_month
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_filtered_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_filtered_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_month
        FROM
            promo_master_filtered_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from promo_master_filtered_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        LEFT JOIN
            calendar_cte c ON fsa.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN (SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
            
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array( SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_month,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,
            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown,
            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
	            WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS actualized_contribution_margin_percent
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_month
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_month)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_month)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_month)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FM' || LPAD(fiscal_month::text, 2, '0') || ' ' || fiscal_year::text AS month,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,
        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm
    on
        tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_month;
"""

MONTHLY_PERFORMANCE_GRAPH_QUERY = """
    WITH final_eligible_promos_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date,
            pm.status AS status_id,
            pm.store_selection_type AS store_selection_type_id
        FROM
            price_promo.promo_master pm
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping},
                {priority_numbers}
            )))
            AND status IN (-1, 4, 8)
    ),
    promo_master_filtered_cte AS (
        SELECT
            *
        FROM
            final_eligible_promos_cte
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.start_date,
            pmfc.end_date
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
    ),
    calendar_cte as (
    	SELECT
            date_id,
            fiscal_year,
            fiscal_month
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_details_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_details_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_month
        FROM
            final_eligible_promos_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa
        on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        LEFT JOIN
            calendar_cte c
            ON fsa.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN (
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_month,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array(
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_month
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_month,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,
            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown,
            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
	            WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS actualized_contribution_margin_percent
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_month
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_month)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_month)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_month)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FM' || LPAD(fiscal_month::text, 2, '0') || ' ' || fiscal_year::text AS month,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,

        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm
    on
        tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_month;
"""


WEEKLY_PERFORMANCE_GRAPH_QUERY_BY_IDS = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date
        FROM
            price_promo.promo_master pm
        join
            price_promo.ps_rules pr on pm.promo_id = pr.promo_id
        WHERE
            is_deleted = 0
            AND status IN (-1, 4, 8)
            {ids_where_str}
            {priority_number_where_str}
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    calendar_cte as (
    	SELECT
            date_id,
            fiscal_year,
            fiscal_week
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_filtered_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_filtered_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_week
        FROM
            promo_master_filtered_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa on pm.promo_id = any(fsa.promo_ids)
        where 
            pm.promo_id in (select promo_id from promo_master_filtered_cte)
        group by 
            fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        LEFT JOIN
            calendar_cte c ON fsa.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN (SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array(SELECT promo_id FROM promo_master_filtered_cte)
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_week,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,
            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown,
            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
	            WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS actualized_contribution_margin_percent
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_week
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_week)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_week)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_week)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FW' || LPAD(fiscal_week::text, 2, '0') || ' ' || fiscal_year::text AS week,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,
        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_week;

"""


WEEKLY_PERFORMANCE_GRAPH_QUERY = """
    WITH final_eligible_promos_cte AS (
        SELECT
            pm.promo_id,
            pm.start_date,
            pm.end_date,
            pm.status AS status_id,
            pm.store_selection_type AS store_selection_type_id
        FROM
            price_promo.promo_master pm
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping},
                {priority_numbers}
            )))
            AND status IN (-1, 4, 8)
    ),
    promo_master_filtered_cte AS (
        SELECT
            *
        FROM
            final_eligible_promos_cte
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from promo_master_filtered_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.start_date,
            pmfc.end_date
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
    ),
    calendar_cte as (
    	SELECT
            date_id,
            fiscal_year,
            fiscal_week
        FROM
            global.tb_fiscal_date_mapping
        WHERE
            date_id >= (select min(start_date) as min_date from promo_master_details_cte)
            and date_id <= (select max(end_date) as max_date from promo_master_details_cte)
    ),
    promo_calendar_combined_cte AS (
        SELECT
            fep.promo_id,
            c.fiscal_year,
            c.fiscal_week
        FROM
            final_eligible_promos_cte fep
        CROSS JOIN
            calendar_cte c
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from    
            price_promo.promo_master pm
        left join  
            price_promo.tb_promo_override_forecast tpof
        on pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pm.promo_id = any(fsa.promo_ids)
        where pm.promo_id in (select promo_id from final_eligible_promos_cte)
        group by fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.promo_spend * pfr.planned_conversion_multiplier else fsa.promo_spend * pfr.planned_conversion_multiplier end) AS finalized_markdown,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS baseline_sales_units,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS baseline_revenue,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS baseline_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end) AS contribution_margin,
            SUM(case when coalesce(pofc.is_override_default,false) then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end) AS contribution_revenue
        from
            promo_override_forecast_cte pofc
        inner join
            price_promo.ps_recommended_finalized_stack_agg fsa
        on pofc.promo_ids = fsa.promo_ids and fsa.recommendation_date = pofc.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa
        on fsa.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        LEFT JOIN
            calendar_cte c
            ON fsa.recommendation_date = c.date_id
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    ia_recommended_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(sales_units) AS ia_recommended_sales_units,
            SUM(revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin
        FROM
            price_promo.ps_recommended_ia_projected_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                a.recommendation_date = pfr.date 
                and pfr.source_currency_id = a.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_id IN (
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    actualized_cte AS (
        SELECT
            c.fiscal_year,
            c.fiscal_week,
            SUM(sales_units) AS actualized_sales_units,
            SUM(revenue * afr.planned_conversion_multiplier) AS actualized_revenue,
            SUM(margin * afr.planned_conversion_multiplier) AS actualized_margin,
            SUM(promo_spend * afr.planned_conversion_multiplier) AS actualized_markdown,
            SUM(contribution_margin * afr.planned_conversion_multiplier) AS actualized_contribution_margin,
            SUM(contribution_revenue * afr.planned_conversion_multiplier) AS actualized_contribution_revenue
        FROM
            price_promo.ps_recommended_actuals_stack_agg a
        LEFT JOIN
            calendar_cte c
            ON a.recommendation_date = c.date_id
        inner join 
            global.actual_forex_rate afr 
            on 
                a.recommendation_date = afr.date 
                and afr.source_currency_id = a.currency_id
                and afr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            a.promo_ids && array(
                SELECT promo_id FROM final_eligible_promos_cte
            )
        GROUP BY
            c.fiscal_year,
            c.fiscal_week
    ),
    combined_cte AS (
        SELECT
            pc.fiscal_year,
            pc.fiscal_week,
            fc.finalized_sales_units,
            fc.finalized_revenue,
            fc.finalized_margin,
            fc.finalized_markdown,
            fc.finalized_incremental_revenue,
            fc.finalized_incremental_margin,
            fc.baseline_sales_units,
            fc.baseline_revenue,
            fc.baseline_margin,
            fc.contribution_margin as finalized_contribution_margin,
            CASE
	            WHEN fc.contribution_revenue != 0 THEN ROUND((fc.contribution_margin * 100 / fc.contribution_revenue)::NUMERIC, 2)
	            ELSE 0
	        END AS finalized_contribution_margin_percent,

            ac.actualized_contribution_margin as actualized_contribution_margin,
            CASE
                WHEN ac.actualized_contribution_revenue != 0 THEN ROUND((ac.actualized_contribution_margin * 100 / ac.actualized_contribution_revenue)::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,

            iac.ia_recommended_sales_units,
            iac.ia_recommended_revenue,
            iac.ia_recommended_margin,
            ac.actualized_sales_units,
            ac.actualized_revenue,
            ac.actualized_margin,
            ac.actualized_markdown
        FROM
            (
                SELECT DISTINCT
                    fiscal_year,
                    fiscal_week
                FROM
                    promo_calendar_combined_cte
            ) pc
        LEFT JOIN
            finalized_cte fc USING (fiscal_year, fiscal_week)
        LEFT JOIN
            ia_recommended_cte iac USING (fiscal_year, fiscal_week)
        LEFT JOIN
            actualized_cte ac USING (fiscal_year, fiscal_week)
    )
    SELECT 
        -- Fiscal year and month formatted as "FM MM YYYY"
        'FW' || LPAD(fiscal_week::text, 2, '0') || ' ' || fiscal_year::text AS week,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        -- Finalized metrics
        finalized_sales_units AS finalized_sales_units,
        finalized_revenue AS finalized_revenue,
        finalized_margin AS finalized_margin,
        CASE 
            WHEN finalized_revenue != 0 THEN 
                (finalized_margin / finalized_revenue) * 100
            ELSE 
                0
        END AS finalized_margin_percent,
        finalized_markdown AS finalized_markdown,

        -- IA recommended metrics
        ia_recommended_sales_units AS ia_recommended_sales_units,
        ia_recommended_revenue AS ia_recommended_revenue,
        ia_recommended_margin AS ia_recommended_margin,
        CASE 
            WHEN ia_recommended_revenue != 0 THEN 
                (ia_recommended_margin / ia_recommended_revenue) * 100
            ELSE 
                0
        END AS ia_recommended_margin_percent,

        -- Actualized metrics
        actualized_sales_units AS actualized_sales_units,
        actualized_revenue AS actualized_revenue,
        actualized_margin AS actualized_margin,
        CASE 
            WHEN actualized_revenue != 0 THEN 
                (actualized_margin / actualized_revenue) * 100
            ELSE 
                0
        END AS actualized_margin_percent,
        actualized_markdown AS actualized_markdown,

        -- Baseline metrics
        baseline_sales_units AS baseline_sales_units,
        baseline_revenue AS baseline_revenue,
        baseline_margin AS baseline_margin,
        finalized_contribution_margin as finalized_contribution_margin,
        finalized_contribution_margin_percent as finalized_contribution_margin_percent,
        actualized_contribution_margin as actualized_contribution_margin,
        actualized_contribution_margin_percent as actualized_contribution_margin_percent,

        -- Incremental metrics
        CASE 
            WHEN actualized_sales_units IS NULL THEN (finalized_sales_units - baseline_sales_units)
            ELSE (actualized_sales_units - baseline_sales_units)
        END AS incremental_sales_units,
        CASE 
            WHEN actualized_revenue IS NULL THEN (finalized_revenue - baseline_revenue)
            ELSE (actualized_revenue - baseline_revenue)
        END AS incremental_revenue,
        CASE 
            WHEN actualized_margin IS NULL THEN (finalized_margin - baseline_margin)
            ELSE (actualized_margin - baseline_margin)
        END AS incremental_margin
    FROM 
        combined_cte
    inner join
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ORDER BY
        fiscal_year, fiscal_week;
"""


FETCH_WORKBENCH_TILES_FILTER_BASED = """
    WITH final_eligible_promos_cte AS (
        select
            promo_id, 
            step_count,
            status AS status_id,
            product_selection_type AS product_selection_type_id,
            store_selection_type AS store_selection_type_id
        FROM
            price_promo.promo_master
        WHERE
            promo_id = any(array(select * from price_promo.fn_filter_promos(
                {start_date},
                {end_date},
                {product_hierarchies},
                {store_hierarchies},
                null,
                {show_partial_overlapping},
                {priority_numbers}
            )))
            AND status IN (-1, 4, 8)
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id in (select promo_id from final_eligible_promos_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from
            final_eligible_promos_cte fep
        inner join
            price_promo.promo_master pm on pm.promo_id = fep.promo_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa on fep.promo_id = any(fsa.promo_ids) 
        left join 
            price_promo.tb_promo_override_forecast tpof ON fep.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        group by 
            fsa.promo_ids,fsa.recommendation_date
    ),
    finalized_cte as(
        SELECT
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS finalized_baseline_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_sales_units else fsa.incremental_sales_units end) AS finalized_incremental_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.revenue else fsa.revenue end) AS finalized_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_revenue else fsa.baseline_revenue end) AS finalized_baseline_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_revenue else fsa.incremental_revenue end) AS finalized_incremental_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.margin else fsa.margin end) AS finalized_margin,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_margin else fsa.baseline_margin end) AS finalized_baseline_margin,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_margin else fsa.incremental_margin end) AS finalized_incremental_margin,
            CASE
                WHEN SUM(case when coalesce(fepc.is_override_default,false) then fsoa.revenue else fsa.revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.margin else fsa.margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.revenue else fsa.revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_margin_percent,
            ROUND(SUM(case when fepc.is_override_default then fsoa.contribution_revenue else fsa.contribution_revenue end)::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(SUM(case when fepc.is_override_default then fsoa.contribution_margin else fsa.contribution_margin end)::DECIMAL, 2) AS finalized_contribution_margin,
            CASE
                WHEN SUM(case when coalesce(fepc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.contribution_margin else fsa.contribution_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_contribution_margin_percent
        FROM 
            promo_override_forecast_cte	fepc
        left join 
            price_promo.ps_recommended_finalized_stack_agg fsa on fepc.promo_ids = fsa.promo_ids and fepc.recommendation_date = fsa.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa on fepc.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ),
    ia_recommended_cte AS (
        SELECT
            SUM(pria.sales_units) AS ia_recommended_sales_units,
            SUM(pria.revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(pria.margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin,
            ROUND(SUM(pria.contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_margin
        FROM price_promo.ps_recommended_ia_projected_agg pria
        inner join 
            global.planned_forex_rate pfr 
            on 
                pria.recommendation_date = pfr.date 
                and pfr.source_currency_id = pria.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    )
    SELECT 
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        jsonb_build_object(
            'sales_units', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_sales_units),
                jsonb_build_object('baseline', finalized.finalized_baseline_sales_units),
                jsonb_build_object('incremental', finalized.finalized_incremental_sales_units),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_sales_units)
            ],
            'revenue', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_revenue),
                jsonb_build_object('baseline', finalized.finalized_baseline_revenue),
                jsonb_build_object('incremental', finalized.finalized_incremental_revenue),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_revenue)
            ],
            'gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin),
                jsonb_build_object('baseline', finalized.finalized_baseline_margin),
                jsonb_build_object('incremental', finalized.finalized_incremental_margin),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_margin)
            ],
            'gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin_percent)
            ],
            'contribution_gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_contribution_margin)
            ],
            'contribution_gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin_percent)
            ]
        ) AS metrics
    FROM
        finalized_cte finalized
    FULL OUTER JOIN ia_recommended_cte ia_recommended ON TRUE
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
"""


FETCH_WORKBENCH_TILES_ID_BASED = """
    with promo_override_forecast_cte as (
        select
            fsa.promo_ids,
            fsa.recommendation_date,
            max(tpof.is_default::int)::bool as is_override_default
        from
            price_promo.promo_master pm 
        left join
            price_promo.ps_rules pr on pm.promo_id = pr.promo_id
        left join
            price_promo.ps_recommended_finalized_stack_agg fsa on pm.promo_id = any(fsa.promo_ids) 
        left join 
            price_promo.tb_promo_override_forecast tpof ON pm.promo_id = tpof.promo_id and coalesce(pm.last_approved_scenario_id,0) = tpof.scenario_id
        where
            pm.{ids_where_str}
            {priority_number_where_str}
        group by 
            fsa.promo_ids,fsa.recommendation_date
    ),
    target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master 
                    WHERE promo_id = any(select unnest(promo_ids) from promo_override_forecast_cte)
                ),
                {target_currency_id}::integer
        )
    ),
    finalized_cte as(
        SELECT
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.sales_units else fsa.sales_units end) AS finalized_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_sales_units else fsa.baseline_sales_units end) AS finalized_baseline_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_sales_units else fsa.incremental_sales_units end) AS finalized_incremental_sales_units,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.revenue * pfr.planned_conversion_multiplier else fsa.revenue * pfr.planned_conversion_multiplier end) AS finalized_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_revenue * pfr.planned_conversion_multiplier else fsa.baseline_revenue * pfr.planned_conversion_multiplier end) AS finalized_baseline_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_revenue * pfr.planned_conversion_multiplier else fsa.incremental_revenue * pfr.planned_conversion_multiplier end) AS finalized_incremental_revenue,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.margin * pfr.planned_conversion_multiplier else fsa.margin * pfr.planned_conversion_multiplier end) AS finalized_margin,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.baseline_margin * pfr.planned_conversion_multiplier else fsa.baseline_margin * pfr.planned_conversion_multiplier end) AS finalized_baseline_margin,
            SUM(case when coalesce(fepc.is_override_default,false) then fsoa.incremental_margin * pfr.planned_conversion_multiplier else fsa.incremental_margin * pfr.planned_conversion_multiplier end) AS finalized_incremental_margin,
            CASE
                WHEN SUM(case when coalesce(fepc.is_override_default,false) then fsoa.revenue else fsa.revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.margin else fsa.margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.revenue else fsa.revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_margin_percent,
            ROUND(SUM(case when fepc.is_override_default then fsoa.contribution_revenue * pfr.planned_conversion_multiplier else fsa.contribution_revenue * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(SUM(case when fepc.is_override_default then fsoa.contribution_margin * pfr.planned_conversion_multiplier else fsa.contribution_margin * pfr.planned_conversion_multiplier end)::DECIMAL, 2) AS finalized_contribution_margin,
            CASE
                WHEN SUM(case when coalesce(fepc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end) != 0 
                THEN ROUND(
                    (SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.contribution_margin else fsa.contribution_margin end
                    ) * 100 / 
                    SUM(
                        case when coalesce(fepc.is_override_default,false) then fsoa.contribution_revenue else fsa.contribution_revenue end
                        )
                    )::NUMERIC, 2
                )
                ELSE 0
            END AS finalized_contribution_margin_percent
        FROM 
            promo_override_forecast_cte	fepc
        left join 
            price_promo.ps_recommended_finalized_stack_agg fsa on fepc.promo_ids = fsa.promo_ids and fepc.recommendation_date = fsa.recommendation_date
        left join
            price_promo.ps_recommended_finalized_stack_override_agg fsoa on fepc.promo_ids = fsoa.promo_ids and fsa.recommendation_date = fsoa.recommendation_date
        inner join 
            global.planned_forex_rate pfr 
            on 
                fsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = fsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
    ),
    ia_recommended_cte AS (
        SELECT
            SUM(pria.sales_units) AS ia_recommended_sales_units,
            SUM(pria.revenue * pfr.planned_conversion_multiplier) AS ia_recommended_revenue,
            SUM(pria.margin * pfr.planned_conversion_multiplier) AS ia_recommended_margin,
            ROUND(SUM(pria.contribution_margin * pfr.planned_conversion_multiplier)::DECIMAL, 2) AS ia_recommended_contribution_margin
        FROM price_promo.ps_recommended_ia_projected_agg pria
        inner join 
            global.planned_forex_rate pfr 
            on 
                pria.recommendation_date = pfr.date 
                and pfr.source_currency_id = pria.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where pria.{ids_where_str}
    )
    SELECT 
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        jsonb_build_object(
            'sales_units', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_sales_units),
                jsonb_build_object('baseline', finalized.finalized_baseline_sales_units),
                jsonb_build_object('incremental', finalized.finalized_incremental_sales_units),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_sales_units)
            ],
            'revenue', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_revenue),
                jsonb_build_object('baseline', finalized.finalized_baseline_revenue),
                jsonb_build_object('incremental', finalized.finalized_incremental_revenue),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_revenue)
            ],
            'gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin),
                jsonb_build_object('baseline', finalized.finalized_baseline_margin),
                jsonb_build_object('incremental', finalized.finalized_incremental_margin),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_margin)
            ],
            'gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_margin_percent)
            ],
            'contribution_gross_margin', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin),
                jsonb_build_object('ia_recommended', ia_recommended.ia_recommended_contribution_margin)
            ],
            'contribution_gross_margin_percent', ARRAY[
                jsonb_build_object('finalized', finalized.finalized_contribution_margin_percent)
            ]
        ) AS metrics
    FROM
        finalized_cte finalized
    FULL OUTER JOIN ia_recommended_cte ia_recommended ON TRUE
    inner join 
        global.tb_currency_master tcm on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
"""


FETCH_PROMOS_CALENDAR_VIEW = """
    WITH promo_master_filtered_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.start_date,
            pm.end_date,
            pm.created_by,
            pm.status AS status_id,
            pm.step_count,
            pm.offer_comment,
            pm.products_count,
            pm.stores_count,
            pm.product_selection_type AS product_selection_type_id,
            pm.store_selection_type AS store_selection_type_id,
            pm.exclusion_selection_type as exclusion_selection_type_id,
            pm.customer_type AS customer_type_id,
            pm.offer_distribution_channel AS offer_distribution_channel_id,
            pm.last_approved_scenario_id,
            pm.recommendation_type_id,
            pm.is_under_processing,
            pm.is_auto_resimulated,
            pm.is_overridden_scenario_finalized
        FROM
            price_promo.promo_master pm
        WHERE
            is_deleted = 0
            AND {date_range_where_clause}
    ),
    filtered_promos_cte as (
        SELECT DISTINCT promo_id
        FROM price_promo.promo_product_hierarchy
        WHERE hierarchy_id IN (
            SELECT hierarchy_id
            FROM price_promo.tb_product_hierarchy_lifecycle_combination
            {product_hierarchical_where_clause}  
        )
        AND promo_id IN (select promo_id from promo_master_filtered_cte)
        UNION
        select promo_id from promo_master_filtered_cte where products_count = 0 
    ),
    eligible_store_promos_cte AS (
        SELECT promo_id
        FROM promo_master_filtered_cte
        WHERE store_selection_type_id = 1
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_hierarchy psh ON pmfc.promo_id = psh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
        UNION ALL
        SELECT pmfc.promo_id
        FROM promo_master_filtered_cte pmfc
        JOIN price_promo.promo_store_sg_hierarchy pssgh ON pmfc.promo_id = pssgh.promo_id
        GROUP BY pmfc.promo_id
        {store_hierarchical_having_clause}
    ),
    intersected_eligible_promos_cte AS (
        SELECT promo_id FROM filtered_promos_cte
        INTERSECT
        SELECT promo_id FROM eligible_store_promos_cte
    ),
    final_eligible_promos_cte AS (
        SELECT  promo_id FROM promo_master_filtered_cte WHERE status_id = -1
        UNION 
        select promo_id from price_promo.tb_placeholder_targets tpt where promo_id in (select promo_id from promo_master_filtered_cte)
        UNION
        SELECT pmfc.promo_id AS promo_id FROM promo_master_filtered_cte pmfc WHERE step_count = 0 and status_id = 0
        UNION
        SELECT promo_id FROM intersected_eligible_promos_cte
    ),
    override_reason_comment AS(
        SELECT
            tpof.promo_id,
            tpof.comment as override_comment,
            tor.reason as override_reason
        FROM
            price_promo.tb_promo_override_forecast tpof
        LEFT JOIN price_promo.tb_override_reason tor
        ON tpof.reason = tor.id
        WHERE
            (tpof.promo_id, tpof.scenario_id) IN (
		    SELECT promo_id, coalesce(last_approved_scenario_id, 0) as scenario_id
		    FROM price_promo.promo_master
		    WHERE promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
		)
    ),
    promo_master_details_cte AS (
        SELECT
            pmfc.promo_id,
            pmfc.promo_name,
            pmfc.start_date,
            pmfc.end_date,
            pmfc.created_by,
            pmfc.status_id,
            STRING_AGG(psc.status_name::text, ', ') AS status,
            CASE 
                WHEN pmfc.end_date < CURRENT_DATE THEN 'Completed'
                WHEN pmfc.start_date > CURRENT_DATE THEN 'Upcoming'
                ELSE 'Ongoing'
            END AS timeline_status,
            pmfc.step_count,
            pmfc.offer_comment,
            pmfc.products_count,
            pmfc.stores_count,
            pmfc.product_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN pstc.product_selection_sub_type::text IS NOT NULL THEN CONCAT(pstc.product_selection_type::text, '-', pstc.product_selection_sub_type::text)
                    ELSE pstc.product_selection_type::text
                END,
                ', '
            ) AS product_selection_type,
            pmfc.store_selection_type_id,
            STRING_AGG(
                CASE
                    WHEN sstc.store_selection_sub_type::text IS NOT NULL THEN CONCAT(sstc.store_selection_type::text, '-', sstc.store_selection_sub_type::text)
                    ELSE sstc.store_selection_type::text
                END,
                ', '
            ) AS store_selection_type,
            pmfc.exclusion_selection_type_id, 
            case 
            	when exclusion_selection_type_id = 1 then 'hierarchy based exclusion '
            	when exclusion_selection_type_id = 2 then 'product based exclusion '
            	when exclusion_selection_type_id = 3 then 'product group based exclusion '
            	when exclusion_selection_type_id = 4 then 'file upload based exclusion '
            end as exclusion_selection_type,
            pmfc.customer_type_id,
            STRING_AGG(tctc.customer_type::text, ', ') AS customer_type,
            pmfc.offer_distribution_channel_id,
            STRING_AGG(todcc.channel::text, ', ') AS offer_distribution_channel,
            pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id,
            STRING_AGG(tasm.name, ', ') AS recommendation_type,
            pmfc.is_under_processing,
            pmfc.is_auto_resimulated,
            pmfc.is_overridden_scenario_finalized
        FROM
            final_eligible_promos_cte fep
        JOIN
            promo_master_filtered_cte pmfc ON fep.promo_id = pmfc.promo_id
        LEFT JOIN
            price_promo.promo_status_config psc ON pmfc.status_id = psc.status_id
        LEFT JOIN
            price_promo.product_selection_type_config pstc ON pmfc.product_selection_type_id = pstc.id
        LEFT JOIN
            price_promo.store_selection_type_config sstc ON pmfc.store_selection_type_id = sstc.id
        LEFT JOIN
            price_promo.tb_customer_type_config tctc ON pmfc.customer_type_id = tctc.id
        LEFT JOIN
            price_promo.tb_offer_distributor_channel_config todcc ON pmfc.offer_distribution_channel_id = todcc.id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pmfc.recommendation_type_id = tasm.id
        GROUP BY
            pmfc.promo_id, pmfc.promo_name, pmfc.start_date, pmfc.end_date, pmfc.created_by, pmfc.status_id, pmfc.step_count,
            pmfc.offer_comment, pmfc.products_count, pmfc.stores_count, pmfc.product_selection_type_id, pmfc.store_selection_type_id, pmfc.exclusion_selection_type_id, 
            pmfc.customer_type_id, pmfc.offer_distribution_channel_id, pmfc.last_approved_scenario_id,
            pmfc.recommendation_type_id, pmfc.is_under_processing, pmfc.is_auto_resimulated, pmfc.is_overridden_scenario_finalized
    ),
    promo_rules_cte AS (
        SELECT
            pr.promo_id,
            pr.discount_level AS discount_level_id,
            dlc.discount_level_value AS discount_level
        FROM
            price_promo.ps_rules pr
        LEFT JOIN
            price_promo.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
        WHERE
            pr.promo_id IN (SELECT DISTINCT promo_id FROM final_eligible_promos_cte)
    ),
    original_cte AS (
        SELECT
            fe.promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_contribution_margin_percent,
            MIN(offer_type_combined_display_name) AS original_discount,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_performance
        FROM
            final_eligible_promos_cte fe 
        INNER JOIN
            price_promo.ps_recommended_finalized_agg pa using(promo_id)
        GROUP BY
            fe.promo_id
    ),
    stacked_original_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS original_stack_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS original_stack_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS original_stack_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS original_stack_promo_spend,
            ROUND((SUM(margin) * 100 / NULLIF(SUM(revenue), 0))::NUMERIC, 2) AS original_stack_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS original_stack_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS original_stack_contribution_margin,
            ROUND((SUM(contribution_margin) * 100 / NULLIF(SUM(contribution_revenue), 0))::NUMERIC, 2) AS original_stack_contribution_margin_percent,
            ROUND((SUM(incremental_margin) / NULLIF(ABS(SUM(baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2) AS original_stack_performance
        FROM
            price_promo.ps_recommended_finalized_stack_agg pa
        INNER JOIN
            final_eligible_promos_cte fe ON fe.promo_id = any(pa.promo_ids)
        GROUP BY
            promo_id
    ),
    finalized_scenarios_cte AS (
        SELECT 
            fep.promo_id,
            pof.is_default
        FROM 
            final_eligible_promos_cte fep
        LEFT JOIN 
            price_promo.promo_master pm ON fep.promo_id = pm.promo_id
        LEFT JOIN 
            price_promo.tb_promo_override_forecast pof ON fep.promo_id = pof.promo_id AND coalesce(pm.last_approved_scenario_id,0) = pof.scenario_id
    ),
    finalized_cte AS (
        SELECT
            sfsc.promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.incremental_sales_units) ELSE SUM(original.incremental_sales_units) END::DECIMAL, 2) AS finalized_incremental_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.incremental_revenue) ELSE SUM(original.incremental_revenue) END::DECIMAL, 2) AS finalized_incremental_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_baseline_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.incremental_margin) ELSE SUM(original.incremental_margin) END::DECIMAL, 2) AS finalized_incremental_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))
                        ELSE (SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0)) END)::NUMERIC, 2) AS finalized_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_contribution_margin,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))
                        ELSE (SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0)) END)::NUMERIC, 2) AS finalized_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN MIN(override.offer_type_combined_display_name)
                ELSE MIN(original.offer_type_combined_display_name)
            END AS finalized_discount,
            ROUND((CASE WHEN sfsc.is_default 
                        THEN (SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))
                        ELSE (SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0)) END)::DECIMAL * 100::DECIMAL, 2) AS finalized_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_agg original ON sfsc.promo_id = original.promo_id
        LEFT JOIN 
            price_promo.ps_recommended_finalized_override_agg override ON sfsc.promo_id = override.promo_id and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    stacked_finalized_cte AS (
        SELECT
            promo_id,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.sales_units) ELSE SUM(original.sales_units) END::DECIMAL, 2) AS finalized_stack_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_sales_units) ELSE SUM(original.baseline_sales_units) END::DECIMAL, 2) AS finalized_stack_baseline_sales_units,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.revenue) ELSE SUM(original.revenue) END::DECIMAL, 2) AS finalized_stack_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_revenue) ELSE SUM(original.baseline_revenue) END::DECIMAL, 2) AS finalized_stack_baseline_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.margin) ELSE SUM(original.margin) END::DECIMAL, 2) AS finalized_stack_margin,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.baseline_margin) ELSE SUM(original.baseline_margin) END::DECIMAL, 2) AS finalized_stack_baseline_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.margin) * 100 / NULLIF(SUM(override.revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.margin) * 100 / NULLIF(SUM(original.revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_margin_percent,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.promo_spend) ELSE SUM(original.promo_spend) END::DECIMAL, 2) AS finalized_stack_promo_spend,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_revenue) ELSE SUM(original.contribution_revenue) END::DECIMAL, 2) AS finalized_stack_contribution_revenue,
            ROUND(CASE WHEN sfsc.is_default = TRUE THEN SUM(override.contribution_margin) ELSE SUM(original.contribution_margin) END::DECIMAL, 2) AS finalized_stack_contribution_margin,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.contribution_margin) * 100 / NULLIF(SUM(override.contribution_revenue), 0))::NUMERIC, 2)
                ELSE ROUND((SUM(original.contribution_margin) * 100 / NULLIF(SUM(original.contribution_revenue), 0))::NUMERIC, 2)
            END AS finalized_stack_contribution_margin_percent,
            CASE 
                WHEN sfsc.is_default 
                THEN ROUND((SUM(override.incremental_margin) / NULLIF(ABS(SUM(override.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
                ELSE ROUND((SUM(original.incremental_margin) / NULLIF(ABS(SUM(original.baseline_margin)), 0))::DECIMAL * 100::DECIMAL, 2)
            END AS finalized_stack_performance
        FROM
            finalized_scenarios_cte sfsc
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_agg original ON sfsc.promo_id = ANY(original.promo_ids)
        LEFT JOIN 
            price_promo.ps_recommended_finalized_stack_override_agg override ON sfsc.promo_id = ANY(override.promo_ids) and original.recommendation_date = override.recommendation_date
        GROUP BY 
            sfsc.promo_id,
            sfsc.is_default
    ),
    actualized_cte AS (
        SELECT
            promo_id,
            ROUND(SUM(sales_units)::DECIMAL, 2) AS actualized_sales_units,
            ROUND(SUM(baseline_sales_units)::DECIMAL, 2) AS actualized_baseline_sales_units,
            ROUND(SUM(incremental_sales_units)::DECIMAL, 2) AS actualized_incremental_sales_units,
            ROUND(SUM(revenue)::DECIMAL, 2) AS actualized_revenue,
            ROUND(SUM(baseline_revenue)::DECIMAL, 2) AS actualized_baseline_revenue,
            ROUND(SUM(incremental_revenue)::DECIMAL, 2) AS actualized_incremental_revenue,
            ROUND(SUM(margin)::DECIMAL, 2) AS actualized_margin,
            ROUND(SUM(baseline_margin)::DECIMAL, 2) AS actualized_baseline_margin,
            ROUND(SUM(incremental_margin)::DECIMAL, 2) AS actualized_incremental_margin,
            ROUND(SUM(promo_spend)::DECIMAL, 2) AS actualized_promo_spend,
            CASE
                WHEN SUM(revenue) != 0 THEN ROUND((SUM(margin) * 100 / SUM(revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_margin_percent,
            ROUND(SUM(contribution_revenue)::DECIMAL, 2) AS actualized_contribution_revenue,
            ROUND(SUM(contribution_margin)::DECIMAL, 2) AS actualized_contribution_margin,
            CASE
                WHEN SUM(contribution_revenue) != 0 THEN ROUND((SUM(contribution_margin) * 100 / SUM(contribution_revenue))::NUMERIC, 2)
                ELSE 0
            END AS actualized_contribution_margin_percent,
            CASE
                WHEN SUM(baseline_margin) IS NULL OR SUM(baseline_margin) = 0 THEN NULL
                ELSE ROUND((SUM(incremental_margin) / ABS(SUM(baseline_margin)))::DECIMAL * 100::DECIMAL, 2)
            END AS performance
        FROM
            price_promo.ps_recommended_actuals_agg praa
        WHERE
            promo_id IN (SELECT promo_id FROM final_eligible_promos_cte)
        GROUP BY
            promo_id
    ),
    ia_recc_cte AS (
        SELECT
            promo_id,
            MIN(offer_type_combined_display_name) AS ia_recc_discount
        FROM
            price_promo.ps_recommended_ia_projected_agg pripa
        WHERE
            promo_id IN (SELECT DISTINCT promo_id FROM final_eligible_promos_cte)
        GROUP BY
            promo_id
    )
    SELECT
        pmc.promo_id,
        pmc.promo_name AS promo_name,
        pmc.start_date,
        pmc.end_date,
        um.name AS created_by,
        pmc.offer_comment,
        pmc.status_id,
        pmc.status,
        pmc.timeline_status,
        pmc.step_count,
        pmc.products_count,
        pmc.stores_count,
        pmc.product_selection_type_id,
        pmc.product_selection_type,
        pmc.store_selection_type_id,
        pmc.store_selection_type,
        pmc.exclusion_selection_type_id,
        pmc.exclusion_selection_type,
        pmc.customer_type_id,
        pmc.customer_type,
        pmc.offer_distribution_channel_id,
        pmc.offer_distribution_channel,
        pmc.last_approved_scenario_id,
        pmc.recommendation_type_id,
        pmc.recommendation_type,
        pmc.is_under_processing,
        pmc.is_auto_resimulated,
        case when pmc.is_overridden_scenario_finalized = true then 1 else 0 end as is_overridden,
        orcc.override_comment,
        orcc.override_reason,
        --
        prc.discount_level_id,
        prc.discount_level,
        --
        iarc.ia_recc_discount,
        --
        COALESCE(acc.performance, fc.finalized_performance) AS performance,
        --
        fc.finalized_sales_units,
        fc.finalized_baseline_sales_units as baseline_sales_units,
        fc.finalized_incremental_sales_units AS incremental_sales_units,
        fc.finalized_revenue,
        fc.finalized_baseline_revenue as baseline_revenue,
        fc.finalized_incremental_revenue AS incremental_revenue,
        fc.finalized_margin,
        fc.finalized_baseline_margin as baseline_margin,
        fc.finalized_incremental_margin AS incremental_margin,
        fc.finalized_margin_percent,
        fc.finalized_promo_spend,
        fc.finalized_contribution_revenue,
        fc.finalized_contribution_margin,
        fc.finalized_contribution_margin_percent,
        fc.finalized_discount,
        --
        sfc.finalized_stack_sales_units,
        sfc.finalized_stack_baseline_sales_units as stack_baseline_sales_units,
        sfc.finalized_stack_revenue,
        sfc.finalized_stack_baseline_revenue as stack_baseline_revenue,
        sfc.finalized_stack_margin,
        sfc.finalized_stack_baseline_margin as stack_baseline_margin,
        sfc.finalized_stack_margin_percent,
        sfc.finalized_stack_promo_spend,
        sfc.finalized_stack_contribution_revenue,
        sfc.finalized_stack_contribution_margin,
        sfc.finalized_stack_contribution_margin_percent,
        --
        oc.original_sales_units,
        oc.original_revenue,
        oc.original_margin,
        oc.original_margin_percent,
        oc.original_promo_spend,
        oc.original_contribution_revenue,
        oc.original_contribution_margin,
        oc.original_contribution_margin_percent,
        oc.original_discount,
        --
        soc.original_stack_sales_units,
        soc.original_stack_revenue,
        soc.original_stack_margin,
        soc.original_stack_margin_percent,
        soc.original_stack_promo_spend,
        soc.original_stack_contribution_revenue,
        soc.original_stack_contribution_margin,
        soc.original_stack_contribution_margin_percent
    FROM 
        promo_master_details_cte pmc
    LEFT JOIN 
        promo_rules_cte prc ON pmc.promo_id = prc.promo_id
    LEFT JOIN
        override_reason_comment orcc ON pmc.promo_id = orcc.promo_id
    LEFT JOIN 
        finalized_cte fc ON pmc.promo_id = fc.promo_id
    LEFT JOIN 
        stacked_finalized_cte sfc ON pmc.promo_id = sfc.promo_id
    LEFT JOIN 
        actualized_cte acc ON pmc.promo_id = acc.promo_id
    LEFT JOIN
        ia_recc_cte iarc ON pmc.promo_id = iarc.promo_id
    LEFT JOIN
        original_cte oc ON pmc.promo_id = oc.promo_id 
    LEFT JOIN
        stacked_original_cte soc ON pmc.promo_id = soc.promo_id
    LEFT JOIN 
        global.user_master um ON pmc.created_by = um.user_code
    ORDER BY promo_id;
"""


FETCH_PROMOS_CALENDAR_VIEW_DOWNLOAD = """
    SELECT
        promo_name AS "Offer Name",
        start_date AS "Start Date",
        end_date AS "End Date",
        event_name AS "Event Name",
        country_name AS "Country",
        created_by AS "Created By",
        status AS "Offer Status",
        actual_performance AS "Actual Performance",
        finalized_performance AS "Finalized Performance",
        products_count AS "#Products",
        stores_count AS "#Stores",
        currency_symbol AS "Currency",
        override_comment as "Override Comment",
        override_reason as "Override Reason",
        exclusion_selection_type AS "Exclusion Selection Type",
        offer_comment AS "Comments",
        discount_type AS "Discount Type",
        discount_level AS "Discount Level - Products",
        finalized_promo_spend AS "Offer $",
        finalized_total_inventory AS "Inventory",
        finalized_st_percent as "ST %",
        product_selection_type AS "Product Selection Type",
        finalized_sales_units AS "Finalized Sales Units",
        finalized_baseline_sales_units AS "Baseline Sales Units",
        finalized_incremental_sales_units AS "Incremental Sales Units",

        finalized_revenue AS "Finalized Revenue",
        finalized_baseline_revenue AS "Baseline Revenue",
        finalized_incremental_revenue AS "Incremental Revenue",

        finalized_margin AS "Finalized Margin$",
        finalized_baseline_margin AS "Baseline Margin",
        finalized_incremental_margin AS "Incremental Margin",
        finalized_margin_percent AS "Finalized Margin%",
        
        original_promo_spend AS "Original Promo$",
        original_sales_units AS "Original Sales Units",
        original_revenue AS "Original Revenue",
        original_margin AS "Original Margin$",
        original_margin_percent AS "Original Margin%",
        --
        finalized_stack_sales_units as "Finalized Stacked Sales Units",
        finalized_stack_baseline_sales_units as "Finalized Stacked Baseline Sales Units",
        finalized_stack_revenue as "Finalized Stacked Revenue",
        finalized_stack_baseline_revenue as "Finalized Stacked Baseline Revenue",
        finalized_stack_margin as "Finalized Stacked Margin",
        finalized_stack_baseline_margin as "Finalized Stacked Baseline Margin",
        finalized_stack_margin_percent as "Finalized Stacked Margin %",
        finalized_stack_promo_spend as "Finalized Stacked Promo Spend",
        --
        original_stack_sales_units as "Orignal Stacked Sales Units",
        original_stack_revenue as "Orignal Stacked Revenue",
        original_stack_margin as "Orignal Stacked Margin",
        original_stack_margin_percent as "Orignal Stacked Margin %",
        original_stack_promo_spend as "Orignal Stacked Promo Spend"
    FROM 
        price_promo.fn_fetch_marketing_calender_data_using_promo({request_payload}::jsonb);
"""


FETCH_VALID_DISCOUNTING_LEVELS = """
SELECT
    pm.promo_id,
    dl.discount_level_id,
    dl.discount_level_value
FROM
    {promo_schema}.promo_master pm
LEFT JOIN
    {promo_schema}.product_selection_type_config pst
    ON pm.product_selection_type = pst.id
LEFT JOIN
    LATERAL (
        SELECT
            unnest(
                CASE 
                    WHEN pst.product_selection_type IN ('sitewide', 'whole_category', 'product_group', 'specific_products') THEN 
                        ARRAY['Overall']
--                    WHEN pst.product_selection_type = 'specific_products' 
--                         AND pst.product_selection_sub_type IN ('hierarchy', 'upload', 'copy_paste') THEN 
--                        ARRAY['Overall', 'Division', 'Department', 'Group', 'Class', 'Sub-class', 'Brand', 'SKU']
--                    WHEN pst.product_selection_type = 'specific_products' 
--                         AND pst.product_selection_sub_type IN ('hierarchy', 'upload', 'copy_paste', 'product_group') THEN 
--                        ARRAY['Overall', 'Division', 'Department', 'Group', 'Class', 'Sub-class', 'Brand', 'Product-Group', 'SKU']
                    ELSE
                        ARRAY[]::TEXT[]  -- Default to an empty array if no conditions match
                END
            ) AS discount_level_value
    ) AS valid_levels
    ON true
LEFT JOIN
    {promo_schema}.discount_level_config dl
    ON valid_levels.discount_level_value = dl.discount_level_value::text
WHERE 
    pm.promo_id = {promo_id}
ORDER BY 
    CASE
        WHEN dl.discount_level_value = 'Overall' THEN 1
        WHEN dl.discount_level_value = 'Division' THEN 2
        WHEN dl.discount_level_value = 'Group' THEN 3
        WHEN dl.discount_level_value = 'Department' THEN 4
        WHEN dl.discount_level_value = 'Class' THEN 5
        WHEN dl.discount_level_value = 'Sub-class' THEN 6
        WHEN dl.discount_level_value = 'SKU' THEN 7
        WHEN dl.discount_level_value = 'Brand' THEN 8
        WHEN dl.discount_level_value = 'Product-Group' THEN 9
        ELSE 10
    END;
"""

FETCH_DISCOUNT_LEVELS = """
    with product_group_overlap_cte as (
        select 
            case when exists(
                select 
                    tpp.product_id,count(*)
                from price_promo.included_promo_product_groups tpppg
                inner join global.tb_pg_product tpp on tpppg.product_group_id = tpp.pg_id
                where tpppg.promo_id = {promo_id}
                group by tpp.product_id
                having count(*) > 1
            ) then true
            else false
            end as has_overlap_of_product_groups,
            case when exists(
                select 
                1
                from price_promo.included_promo_product_groups tpppg
                where promo_id = {promo_id}
            ) then true
            else false
            end as has_product_groups
    ),
    store_group_overlap_cte as (
        select 
            case when exists(
                select 
                    tss.store_id,count(*) 
                from price_promo.tb_promo_store_groups tpsg
                inner join global.tb_sg_store tss on tpsg.store_group_id = tss.sg_id
                where tpsg.promo_id = {promo_id}
                group by tss.store_id
                having count(*) > 1
            ) then true
            else false
            end as has_overlap_of_store_groups,
            case when exists(
                select 
                1
                from price_promo.tb_promo_store_groups tpsg
                where promo_id = {promo_id}
            ) then true
            else false
            end as has_store_groups
    ),
    category_wise_discount_levels_cte as (
        select
            category,
            jsonb_agg(
                jsonb_build_object(
                    'value',discount_level_id,
                    'label',discount_level_value
                )
            ) as result
        from
            price_promo.discount_level_config,
            product_group_overlap_cte,
            store_group_overlap_cte
        where 
            (discount_level_id != -100)
            or
            (
                (
                    category = 'product' 
                    and has_overlap_of_product_groups = false
                    and has_product_groups = true
                )
                or
                (
                    category = 'store' 
                    and has_overlap_of_store_groups = false
                    and has_store_groups = true
                )
            )
        group by
            category
    )
    select
        jsonb_object_agg(
            category,result
        ) as discount_levels
    from
        category_wise_discount_levels_cte
"""


UPDATE_DISCOUNT_RULES = """
do $$
declare
    _current_product_discount_level int[];
    _current_store_discount_level int[];
    _current_customer_discount_level int[];
    _current_priority_number int;
    _current_promo_status int;
begin

    select 
        product_discount_level, 
        store_discount_level, 
        customer_discount_level,
        priority_number
    into 
        _current_product_discount_level, 
        _current_store_discount_level, 
        _current_customer_discount_level,
        _current_priority_number
    from price_promo.ps_rules pr 
    where pr.promo_id = {promo_id};

    delete from {promo_schema}.ps_rules where promo_id = {promo_id};

    _current_promo_status = (select status from price_promo.promo_master pm where pm.promo_id = {promo_id});
    if not _current_promo_status = any(array(select remarks::int2[] from metaschema.tb_app_sub_master where name = 'stacked_offers_eligibility')) then
        call price_promo_opt.pc_delete_promo_metrics(array[{promo_id}], NULL::integer[], 1, 1, 1);
    else
        call price_promo_opt.pc_delete_agg_promo_metrics(array[{promo_id}], NULL::integer[], 1, 1, 1);
    end if;

    perform price_promo.fn_delete_promo_override_forecast({promo_id});

    INSERT INTO {promo_schema}.ps_rules
    (
        promo_id, product_discount_level, store_discount_level, customer_discount_level , discount_type,
        discount_type_id,priority_number, created_at, created_by, vf_type, vf_fixed_amount, vf_per_unit, min_upto_percent, max_upto_percent, products_on_max_upto_percent
    )
    VALUES(
        {promo_id}, {product_discount_level}::int[], {store_discount_level}::int[], {customer_discount_level}::int[], {discount_type},
        {discount_type_id}, {priority_number}, now(), {user_id}, {vf_type}, {vf_fixed_amount}, {vf_per_unit}, {min_upto_percent}, {max_upto_percent}, {products_on_max_upto_percent}
    );

    -- Update the promo_master using the promo_id from the rules_cte
    update {promo_schema}.promo_master
    set 
        step_count = 2,
        status = 0,
        last_approved_scenario_id = null,
        recommendation_type_id = null,
        updated_at = now(),
        updated_by = {user_id}
    where promo_id = {promo_id};

    update price_promo.tb_exmd_promo
    set
        folder_id = NULL,
        price_filter_id = NULL
    where
        tb_exmd_promo.promo_id = {promo_id};

    perform price_promo.fn_update_promo_stacked_offers_mapping_with_flag_updates(array[{promo_id}]); 

    if (_current_product_discount_level is null or _current_product_discount_level != {product_discount_level}::int[]) 
        or 
        (_current_store_discount_level is null or _current_store_discount_level != {store_discount_level}::int[])
        or
        (_current_priority_number is null or _current_priority_number != {priority_number})
        or 
        not exists (
            select 1 from {promo_schema}.ps_scenario_discounts psd
            where psd.promo_id = {promo_id}
        )
    then
        perform price_promo.fn_populate_scenario_discounts_table({promo_id},{user_id});
    end if;
    

    end;
    $$;
    select rule_id, promo_id  
    from {promo_schema}.ps_rules
    where promo_id = {promo_id};
"""

FETCH_DISCOUNT_RULES = """
select
    promo_id,
    product_discount_level,
    store_discount_level
from {promo_schema}.ps_rules pr
where
    promo_id = {promo_id}
"""

FETCH_RESIMULATE_VALID_OFFER_TYPES = """
    select fn_step3_get_valid_offers as result from price_promo.fn_step3_get_valid_offers({promo_id},{is_optimisation})
"""

CREATE_PROMO = """
    select price_promo.fn_create_promotion(
        {promo_name},{promo_start_date},{promo_end_date},
        {promo_status},0, {user_id}, {event_id}
    ) as promo_id
"""


FETCH_DISCOUNTING_LEVEL_VALUES_SPECIFIC_PRODUCTS = """
select
	distinct
    pph.hierarchy_value_id as id,
	hierarchy_value_name as value
from
	{promo_schema}.promo_product_hierarchy pph
where
	promo_id = {promo_id}
	and hierarchy_level_id = (
		select
			discount_level
		from
			{promo_schema}.ps_rules pr
		where
			pr.promo_id = {promo_id}
	)
"""

FETCH_DISCOUNTING_LEVEL_VALUES_OVERALL = """
select
	distinct pm.promo_id as id,
	pm.name as value
from
	{promo_schema}.promo_master pm
where
	pm.promo_id = {promo_id}
order by
	pm.promo_id
"""

FETCH_DISCOUNTING_LEVEL_VALUES_PRODUCT_GROUP = """
select
	tpg.pg_id as id,
	tpg.pg_name as value
from
	{promo_schema}.promo_master pm
left join {promo_schema}.tb_promo_product_groups tppg on
	pm.promo_id = tppg.promo_id
left join {global_schema}.tb_product_group tpg on
	tppg.product_group_id = tpg.pg_id 
where
    pm.promo_id = {promo_id}
"""


STEP1_UPDATE_PROMO_DETAILS_AT_QUERY = """
    update price_promo.promo_master pm
    SET
        step_count = {step_count},
        product_selection_type = {product_selection_type},
        exclusion_selection_type = {product_exclusion_type},
        customer_type = {customer_type},
        offer_distribution_channel = {offer_distribution_channel},
        store_selection_type = {store_selection_type},
        updated_by = {user_id},
        updated_at = now()
    WHERE
        pm.promo_id = {promo_id};
"""

STEP1_SAVE_PROMO_LIFECYCLE_INDICATOR_QUERY = """
    FOREACH lifecycle_indicator_id IN ARRAY lifecycle_indicator_ids
    LOOP
        -- Fetch the indicator name based on lifecycle_indicator_id
        SELECT lifecycle_indicator INTO v_lifecycle_indicator
        FROM global.tb_lifecycle_indicator_config
        WHERE id = lifecycle_indicator_id;

        -- Construct and execute the insert statement using EXECUTE and USING
        EXECUTE '
            INSERT INTO price_promo.included_product_hierarchy (
                promo_id,
                hierarchy_level_id,
                hierarchy_level_name,
                hierarchy_value_id,
                hierarchy_value_name
            )
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT DO NOTHING'
        USING promo_id, -2, 'Lifecycle Indicator', lifecycle_indicator_id, v_lifecycle_indicator;
    END LOOP;
"""

STEP1_SAVE_PROMO_PRODUCT_HIERARCHY_QUERY = """
    FOR hierarchy_key, cfg IN
        SELECT j.key, j.value
        FROM jsonb_each(_product_hierarchies_config) AS j(key, value)  -- <- your configuration JSON variable
        WHERE (j.value->>'is_linked_to_promo')::BOOLEAN IS TRUE LOOP
		
		-- Extract configuration
        id_col := cfg->>'id_column';
        val_col := cfg->>'value_column';
        hierarchy_level_id := (cfg->>'id')::INT;
		label_value:= (cfg->>'label')::TEXT;

        -- Determine the hierarchy field name based on hierarchy_level_id
        hierarchy_field := id_col;

        -- Insert data into the temporary table
        EXECUTE format('
            INSERT INTO price_promo.included_product_hierarchy  (
                promo_id, hierarchy_level_id, hierarchy_level_name, hierarchy_value_id, hierarchy_value_name
            )
            SELECT DISTINCT
                %1$s AS promo_id,
                %2$s AS hierarchy_level_id,
                %3$L AS hierarchy_level_name,
                %4$s::bigint AS hierarchy_value_id,
                %5$L AS hierarchy_value_name
            FROM price_promo.product_master pm
            WHERE 
                pm.product_id IN (SELECT unnest(%6$L::INTEGER[]))
                and %4$s is not null
            ',
            promo_id,
            hierarchy_level_id,
            label_value,
            hierarchy_field,
            val_col,
            product_ids
            );
    END LOOP;
"""

STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY = """
    -- Loop over each product_id and product_name in the temporary table
    FOR product_details IN
        SELECT product_id, product_name
        FROM temp_product_details
    LOOP
        -- Insert data into promo_product, routing to the correct partition
        EXECUTE format('
            INSERT INTO price_promo.included_products_%s (promo_id, product_id, product_name)
            VALUES ($1, $2, $3)
            ON CONFLICT DO NOTHING',
            promo_id::TEXT
        )
        USING promo_id, product_details.product_id, product_details.product_name;
    END LOOP;
"""


STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY = """
    -- Create a temporary table to store the data
    CREATE TEMP TABLE temp_promo_product_hierarchy (
        promo_id INTEGER,
        hierarchy_level_id INT,
        hierarchy_level_name TEXT,
        hierarchy_value_id BIGINT,
        hierarchy_value_name TEXT
    );

    	FOR hierarchy_key, cfg IN
        SELECT j.key, j.value
        FROM jsonb_each(_product_hierarchies_config) AS j(key, value)  -- <- your configuration JSON variable
        WHERE (j.value->>'id')::INTEGER IS NOT NULL LOOP
		
		-- Extract configuration
        id_col := cfg->>'id_column';
        val_col := cfg->>'value_column';
        hierarchy_level_id := (cfg->>'id')::INT;
		label_value:= (cfg->>'label')::TEXT;

		-- Check if the corresponding hierarchy data exists in product_hierarchy
        IF (jsonb_typeof(product_hierarchy[id_col]) = 'array' AND jsonb_array_length(product_hierarchy[id_col]) > 0)
           OR (hierarchy_level_id = -2 AND product_hierarchy[id_col] IS NOT NULL) THEN
           
            -- Determine the hierarchy field name based on hierarchy_level_id
            hierarchy_field := id_col;
			
			-- Build dynamic value_expr like 'pm.l3_cuq' or 'tlic.lifecycle_indicator' and join_expr
			IF hierarchy_level_id = -2 THEN
                value_expr := format('tlic.%I', val_col);
				join_expr := format('LEFT JOIN global.tb_parent_lifecycle_mapping tlic
                    ON val::bigint = tlic.%I', id_col);
			ELSE
                value_expr := format('pm.%I', val_col);
				join_expr := format('LEFT JOIN price_promo.product_master pm 
                    ON val::bigint = pm.%I', id_col);
            END IF;

            -- Insert data into the temporary table
            EXECUTE format('
                INSERT INTO temp_promo_product_hierarchy (
                    promo_id, hierarchy_level_id, hierarchy_level_name, hierarchy_value_id, hierarchy_value_name
                )
                SELECT DISTINCT
                    $1 AS promo_id,
                    $2 AS hierarchy_level_id,
                    $5 AS hierarchy_level_name,
                    val::bigint AS hierarchy_value_id,'
					|| value_expr || '::TEXT AS hierarchy_value_name
                FROM jsonb_array_elements_text($3->$4) AS vals(val) ' || join_expr
                || ' where val is not null'
                ) USING promo_id, hierarchy_level_id, product_hierarchy, hierarchy_field, label_value;
        END IF;
    END LOOP;

    -- Output the contents of the temporary table
    RAISE NOTICE 'Contents of temp_promo_product_hierarchy:';
    FOR debug_rec IN (SELECT * FROM temp_promo_product_hierarchy) LOOP
        RAISE NOTICE 'promo_id: %, hierarchy_level_id: %, hierarchy_value_id: %',
            debug_rec.promo_id, debug_rec.hierarchy_level_id, debug_rec.hierarchy_value_id;
    END LOOP;

    -- Insert data from the temporary table into the promo_product_hierarchy table
    INSERT INTO price_promo.included_product_hierarchy(
        promo_id, hierarchy_level_id, hierarchy_level_name, hierarchy_value_id, hierarchy_value_name
    )
    SELECT
        tpph.promo_id, tpph.hierarchy_level_id, tpph.hierarchy_level_name, tpph.hierarchy_value_id, tpph.hierarchy_value_name
    FROM temp_promo_product_hierarchy tpph;

    -- Drop the temp_product_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_promo_product_hierarchy';
"""

STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_HIERARCHY_INPUTS_QUERY = """
    -- Create a temporary table to store unique product IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_product_details AS
        SELECT DISTINCT
            pm.product_id,
            pm.product_name
        FROM price_promo.product_master pm
        LEFT JOIN global.tb_parent_lifecycle_mapping lic on pm.product_id = lic.product_id 
        {where_str}
    '
    {using_str}

    -- Fetch product_ids into an array variable
    SELECT array_agg(product_id) INTO product_ids FROM temp_product_details;
    
    -- Call the function to create the partition for promo_id
    PERFORM price_promo.fn_create_promo_product_partition_table(promo_id);

    {STEP1_SAVE_WHOLE_CATECORY_PROMO_PRODUCT_HIERARCHY_QUERY}
    {STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY}
    
    -- Drop the temp_product_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_product_details';
"""


STEP1_SAVE_PROMO_PRODUCT_DETAILS_BASED_ON_PRODUCT_IDS_INPUTS_QUERY = f"""
    -- Create a temporary table to store unique product IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_product_details AS
        SELECT DISTINCT
            product_id,
            product_name
        FROM price_promo.product_master pm
        WHERE product_id = ANY($1::INTEGER[])'
    USING product_ids;
    
    -- Call the function to create the partition for promo_id
    PERFORM price_promo.fn_create_promo_product_partition_table(promo_id);

    {STEP1_SAVE_PROMO_PRODUCT_HIERARCHY_QUERY}
    {STEP1_SAVE_PROMO_PRODUCT_DETAILS_QUERY}

    -- Drop the temp_product_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_product_details';
"""


STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY = """

    FOR hierarchy_key, cfg IN
        SELECT j.key, j.value
        FROM jsonb_each(_store_hierarchies_config) AS j(key, value)  -- <- your configuration JSON variable
        WHERE (j.value->>'is_linked_to_promo')::BOOLEAN IS TRUE LOOP
		
		-- Extract configuration
        id_col := cfg->>'id_column';
        val_col := cfg->>'value_column';
        hierarchy_level_id := (cfg->>'id')::INT;
		label_value:= (cfg->>'label')::TEXT;

        -- Determine the hierarchy field name based on hierarchy_level_id
        hierarchy_field := id_col;

        -- Insert data into the temporary table
        EXECUTE format('
            INSERT INTO price_promo.promo_store_hierarchy  (
                promo_id, hierarchy_level_id, hierarchy_level_name, hierarchy_value_id, hierarchy_value_name
            )
            SELECT DISTINCT
                $1 AS promo_id,
                $2 AS hierarchy_level_id,
                $3 AS hierarchy_level_name,'
                || hierarchy_field || '::bigint AS hierarchy_value_id,'
                || val_col || '::TEXT AS hierarchy_value_name
            FROM global.tb_store_master tsm
            WHERE 
                store_id IN (SELECT unnest($4::INTEGER[]))'
            ) USING promo_id, hierarchy_level_id, label_value, store_ids;
    END LOOP;
"""


STEP1_SAVE_PROMO_STORE_DETAILS_QUERY = """
    -- Loop over each store_id and store_name in the temporary table
    FOR store_details IN
        SELECT store_id, store_name
        FROM temp_store_details
    LOOP
        -- Insert data into promo_store, routing to the correct partition
        EXECUTE format('
            INSERT INTO price_promo.promo_store_%s (promo_id, store_id, store_name)
            VALUES ($1, $2, $3)
            ON CONFLICT DO NOTHING',
            promo_id::TEXT
        )
        USING promo_id, store_details.store_id, store_details.store_name;
    END LOOP;
"""


STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_BNM_STORE_QUERY = f"""
    -- Create a temporary table to save unique store IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_store_details AS
        SELECT DISTINCT
            store_id,
            store_name
        FROM global.tb_store_master
        WHERE
            is_active = 1
            AND s1_id IN (2)
    ';
            
    -- Fetch product_ids into an array variable
    SELECT array_agg(store_id) INTO store_ids FROM temp_store_details;

    {STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY}
    {STEP1_SAVE_PROMO_STORE_DETAILS_QUERY}

    -- Drop the temp_store_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_store_details';
"""


STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_ECOM_STORE_QUERY = f"""
    
     -- Create a temporary table to save unique store IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_store_details AS
        SELECT DISTINCT
            store_id,
            store_name
        FROM global.tb_store_master
        WHERE
            is_active = 1
            AND s1_id IN (1)
    ';
            
    -- Fetch product_ids into an array variable
    SELECT array_agg(store_id) INTO store_ids FROM temp_store_details;
    
    {STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY}
    EXECUTE 'INSERT INTO price_promo.promo_store (promo_id, store_id, store_name)
    SELECT
    		$1,
            store_id,
            store_name
        FROM global.tb_store_master
        WHERE
            is_active = 1
            AND s1_id IN (1)
    	group by 1,2,3'
    USING promo_id;
    
    -- Drop the temp_store_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_store_details';
"""


STEP1_SAVE_PROMO_STORE_DETAILS_BASED_ON_STORE_IDS_INPUTS_QUERY = f"""
    -- Create a temporary table to store unique product IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_store_details AS
        SELECT DISTINCT
            store_id,
            store_name
        FROM global.tb_store_master tsm
        WHERE store_id = ANY($1::INTEGER[])'
    USING store_ids;
    
    {STEP1_SAVE_PROMO_STORE_HIERARCHY_QUERY}
    {STEP1_SAVE_PROMO_STORE_DETAILS_QUERY}

    -- Drop the temp_store_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_store_details';
"""

STEP1_SAVE_PROMO_PRODUCT_GROUP_BASED_ON_PG_INPUTS_QUERY = """
    -- Call the function to create the partition for promo_id
    --PERFORM price_promo.fn_create_promo_product_partition_table(promo_id);
    
    -- Insert into tb_promo_product_groups
    EXECUTE '
        INSERT INTO price_promo.included_promo_product_groups (promo_id, product_group_id, product_group_name)
        SELECT $1,
            pg_id,
            pg_name
        FROM global.tb_product_group
        WHERE pg_id = ANY($2::INTEGER[])
        GROUP BY 1,2,3'
    USING promo_id, product_group_ids;
    
    EXECUTE '
        INSERT INTO price_promo.included_promo_pg_hierarchy (
            promo_id,
            product_group_id,
            product_group_name,
            hierarchy_level_id, 
            hierarchy_level_name, 
            hierarchy_value_id, 
            hierarchy_value_name
        )
        select distinct
            $1 as promo_id,
            pg.pg_id as product_group_id,
            pg.pg_name as product_group_name,
            ph.hierarchy_level as hierarchy_level_id,
            hlc.hierarchy_level_name,
            ph.hierarchy_value as hierarchy_value_id,
            hlc.cuq as hierarchy_value_name
        from 
            global.tb_product_group pg 
        inner join 
            global.tb_pg_hierarchy ph on ph.pg_id = pg.pg_id  	
        inner join 
            global.tb_hierarchy_level_config hlc on ph.hierarchy_level = hlc.hierarchy_level and ph.hierarchy_value = hlc.cid
        where 
            pg.pg_id = ANY($2::INTEGER[])
        '
    USING promo_id, product_group_ids;
    
    
    EXECUTE '
        INSERT INTO price_promo.included_products (promo_id, product_id, product_name)
        SELECT 
            $1, 
            tph.product_id,
            pm.product_name
        FROM global.tb_pg_product tph
        LEFT JOIN price_promo.product_master pm ON tph.product_id = pm.product_id
        WHERE pg_id = ANY($2::INTEGER[])
        group by 1,2,3 '
     using promo_id, product_group_ids;
"""


STEP1_SAVE_PROMO_SG_HIERARCHY_QUERY = """

    FOR hierarchy_key, cfg IN SELECT * FROM jsonb_each(_store_hierarchies_config)
    LOOP
        IF cfg ? 'id' AND cfg->>'id' IS NOT NULL THEN
            case_level_name := case_level_name || format(
                'WHEN tsh.hierarchy_level = %s THEN %L ',
                cfg->>'id',
                COALESCE(cfg->>'label', hierarchy_key)
            );

            case_value_name := case_value_name || format(
                'WHEN tsh.hierarchy_level = %s THEN tsm.%I ',
                cfg->>'id',
                cfg->>'value_column'
            );

            join_condition := join_condition || format(
                '(tsh.hierarchy_level = %s AND tsh.hierarchy_value = tsm.%I) OR ',
                cfg->>'id',
                cfg->>'id_column'
            );
        END IF;
    END LOOP;

    -- Trim trailing ' OR '
    join_condition := left(join_condition, length(join_condition) - 4);
    
    FOREACH store_group_id IN ARRAY store_group_ids
    LOOP
        -- Insert into temporary table
        EXECUTE format($f$
            INSERT INTO price_promo.promo_store_sg_hierarchy (
                promo_id,
                store_group_id,
                store_group_name,
                hierarchy_level_id, 
                hierarchy_level_name, 
                hierarchy_value_id, 
                hierarchy_value_name
            )
            SELECT DISTINCT
                $1 AS promo_id,
                $2 AS store_group_id,
                tsg.sg_name AS store_group_name,
                tsh.hierarchy_level AS hierarchy_level_id,
                %s END AS hierarchy_level_name,
                tsh.hierarchy_value AS hierarchy_value_id,
                %s END AS hierarchy_value_name
            FROM 
                global.tb_sg_hierarchy tsh
            LEFT JOIN global.tb_store_group tsg ON tsh.sg_id = tsg.sg_id
            LEFT JOIN 
                global.tb_store_master tsm 
            ON 
                %s
            WHERE
                tsh.sg_id = $2
            ;$f$,
        case_level_name, case_value_name, join_condition
        )
        USING promo_id, store_group_id;
    END LOOP;
"""


UPDATE_PROMO_PRODUCT_STORE_COUNT_QUERY = """
    -- select count(*) into final_products_count from (select unnest (fn_get_promo_final_products) from price_promo.fn_get_promo_final_products({promo_id}, 1)) dd;
    select count(*) into final_products_count from price_promo.promo_product_{promo_id};
    select price_promo.fn_get_promo_final_stores({promo_id}) as store_count into final_stores_count;
  
    update price_promo.promo_master pm
    SET
        products_count = final_products_count,
        stores_count = final_stores_count
    WHERE
        pm.promo_id = {promo_id};
"""


STEP1_SAVE_PROMO_SG_DETAILS_QUERY = """
    -- Insert into tb_promo_store_groups
    EXECUTE '
        INSERT INTO price_promo.tb_promo_store_groups (promo_id, store_group_id, store_group_name)
        SELECT $1, sg_id, sg_name
        FROM temp_store_groups_details'
    USING promo_id;
"""


STEP1_SAVE_PROMO_STORE_GROUP_BASED_ON_SG_INPUTS_QUERY = f"""
    -- Create a temporary table to save store group details
    EXECUTE '
        CREATE TEMP TABLE temp_store_groups_details AS
        SELECT
            sg_id,
            sg_name
        FROM global.tb_store_group
        WHERE sg_id = ANY($1::INTEGER[])'
    USING store_group_ids;

    -- Create a temporary table to store unique product IDs and names
    EXECUTE '
        CREATE TEMP TABLE temp_store_details AS
        SELECT DISTINCT
            tss.store_id,
            pm.store_name
        FROM global.tb_sg_store tss
        LEFT JOIN global.tb_store_master pm ON tss.store_id = pm.store_id
        WHERE sg_id = ANY($1::INTEGER[])'
    USING store_group_ids;

    {STEP1_SAVE_PROMO_SG_HIERARCHY_QUERY}
    {STEP1_SAVE_PROMO_SG_DETAILS_QUERY}
    {STEP1_SAVE_PROMO_STORE_DETAILS_QUERY}

    -- Drop the temp_store_groups_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_store_groups_details';
    -- Drop the temp_store_details table
    EXECUTE 'DROP TABLE IF EXISTS temp_store_details';
"""

STEP1_SAVE_DETAILS_QUERY = """
    DO $$
    DECLARE
        hierarchy_level_id INT;
        lifecycle_indicator_id INTEGER;
        product_group_id INTEGER;
        store_group_id INTEGER;
        product_details RECORD;
        store_details RECORD;
        promo_id INTEGER := {promo_id};
        product_hierarchy JSONB := {product_hierarchy};
        product_ids INTEGER[] := {product_ids};
        store_ids INTEGER[] := {store_ids};
        product_group_ids INTEGER[] := {product_group_ids};
        store_group_ids INTEGER[] := {store_group_ids};
        lifecycle_indicator_ids INTEGER[] := {lifecycle_indicator_ids};
        hierarchy_field TEXT;
        debug_rec RECORD;
        v_lifecycle_indicator TEXT;
        final_products_count int;
        final_stores_count int;
        _current_promo_status int;
        hierarchy_key TEXT;
	    cfg JSONB;
	    id_col TEXT;
	    val_col TEXT;
	    field_name TEXT;
		label_value TEXT;
		value_expr TEXT;
		join_expr TEXT;
        case_level_name TEXT := 'Case ';
        case_value_name TEXT := 'Case ';
        join_condition TEXT := '';
        _product_hierarchies_config jsonb;
        _store_hierarchies_config jsonb;
    BEGIN
        select config_value::jsonb into _product_hierarchies_config
        from price_promo.tb_tool_configurations
        where module = 'product' and config_name = 'hierarchy_filters';

        select config_value::jsonb into _store_hierarchies_config
        from price_promo.tb_tool_configurations
        where module = 'store' and config_name = 'hierarchy_filters';

    -- Edit Step 1 Query
    {extra_query}
    -- Save Step 1 Query
    {update_promo_details_query}
    {save_product_details_based_on_event_query}
    {save_product_details_based_on_site_wide_query}
    {save_product_details_based_on_product_hierarchy_inputs_query}
    {save_product_details_based_on_product_ids_inputs_query}
    {stores_insertion_based_on_event}
    {save_store_details_based_on_all_store_query}
    {save_store_details_based_on_bnm_store_query}
    {save_store_details_based_on_ecom_store_query}
    {save_store_details_based_on_store_ids_inputs_query}
    {save_product_groups_based_on_pg_inputs_query}
    {save_store_groups_based_on_sg_inputs_query}
    {exclusion_query}
    perform price_promo.fn_save_promo_final_hierarchy({promo_id});
    perform price_promo.fn_save_promo_final_products({promo_id});
    perform price_promo.fn_update_promo_stacked_offers_mapping_with_flag_updates(array[{promo_id}]::int[]);
    call price_promo_opt.pc_refresh_promo_inventory(array[{promo_id}]::int[]);
    {update_product_store_count_query}
    END $$;
"""


FETCH_LY_TARGETS_QUERY = """
    SELECT
        {promo_id} AS promo_id,
        ROUND(baseline_units::NUMERIC, 2) AS baseline_units,
        ROUND(baseline_revenue::NUMERIC, 2) AS baseline_revenue,
        ROUND(baseline_margin::NUMERIC, 2) AS baseline_gross_margin,
        ROUND(baseline_gm_percent::NUMERIC, 2) AS baseline_gross_margin_percent,
        ROUND(ly_units::NUMERIC, 2) AS ly_units,
        ROUND(ly_revenue::NUMERIC, 2) AS ly_revenue,
        ROUND(ly_margin::NUMERIC, 2) AS ly_gross_margin,
        ROUND(ly_gm_percent::NUMERIC, 2) AS ly_gross_margin_percent
    FROM price_promo_opt.fn_workbench_get_baseline_ly_metrics({promo_id});
"""


FETCH_WBP_WCP_QUERY = """
    SELECT 
        weighted_base_price, 
        weighted_cost_price 
    FROM price_promo.tb_placeholder_pricing
    WHERE 
        month = {month} 
        and year = {year}
"""

STEP3_UPDATE_PROMO_DETAILS_QUERY = """
    update price_promo.promo_master pm
    SET
        step_count = {step_count},
        updated_by = {user_id},
        updated_at = now()
    WHERE
        pm.promo_id = {promo_id};
"""

STEP3_SAVE_TAGETS_METRICS = """
    UPDATE price_promo.ps_rules
    set
        opt_discount_type_id={opt_discount_type_id},
        discount_type_values={discount_type_values}::int[],
        min_discount={min_discount},
        max_discount={max_discount},
        gross_margin_target={gross_margin_target},
        gross_margin_lift={gross_margin_lift},
        gross_margin_priority={gross_margin_priority},
        revenue_target={revenue_target},
        revenue_lift={revenue_lift},
        revenue_priority={revenue_priority},
        units_target={units_target},
        units_lift={units_lift},
        units_priority={units_priority},
        gross_margin_percent_target={gross_margin_percent_target},
        gross_margin_percent_lift={gross_margin_percent_lift},
        gross_margin_percent_priority={gross_margin_percent_priority},
        maximization_parameter={maximization_parameter},
        created_by={created_by},
        created_at='{created_at}',
        targets_edited = (
        CASE 
            WHEN price_promo.ps_rules.revenue_target IS DISTINCT FROM {revenue_target}
              OR price_promo.ps_rules.gross_margin_target IS DISTINCT FROM {gross_margin_target}
              OR price_promo.ps_rules.units_target IS DISTINCT FROM {units_target}
              OR price_promo.ps_rules.gross_margin_percent_target IS DISTINCT FROM {gross_margin_percent_target}
            THEN TRUE
            ELSE targets_edited -- preserve original value if no change
        END
    )
    where 
        promo_id = {promo_id};
"""

GET_PROMO_SCENARIO_DEATILS_QUERY = """
    select 
        scenario_id,
        scenario_order_id
    from 
        price_promo.scenario_master sm 
    where 
        promo_id = {promo_id};
"""


FETCH_TIER_VALID_OFFER_TYPES = """
select
        id as offer_type_id,
        name as offer_type,
        display_name
    from
        {meta_schema}.tb_app_sub_master tasm
    where
        master_id in (
            select
                id
            from
                {meta_schema}.tb_app_master
            where
                name = 'Offer type'
        )
        and id in (27, 2, 32)
        and is_active = 1
"""


UPDATE_SCENARIO_NAME_QUERY = """
update
	{promo_schema}.scenario_master
set
	scenario_name = '{scenario_name}'
where
	scenario_id = {scenario_id}
"""


COPY_PROMO_QUERY = """
    select
    price_promo.fn_copy_promos({promo_details},{user_id}) as result
"""



FETCH_STEP2_BASICS_QUERY = """
    WITH exmd_check AS (
        SELECT 
            pm.promo_id,
            CASE 
                WHEN EXISTS (SELECT 1 FROM price_promo.tb_exmd_promo tep WHERE tep.promo_id = pm.promo_id) AND pm.step_count = 3
                THEN 4 
                ELSE pm.step_count 
            END AS max_enabled_step_count
        FROM price_promo.promo_master pm
        WHERE pm.promo_id = {promo_id}
    )
    SELECT
        pm.promo_id,
        pm.name AS promo_name,
        pm.currency_id,
        cm.currency_symbol as currency_symbol,
        cm.currency_name as currency_name,
        to_char(pm.start_date, 'MM/DD/YYYY') AS start_date,
        to_char(pm.end_date, 'MM/DD/YYYY') AS end_date,
        ((pm.end_date::date - pm.start_date::date) + 1) AS promo_days,
        pm.status AS status_id,
        psc.status_name::text as status,
        pm.step_count,
        exmd_check.max_enabled_step_count,
        json_build_object(
            'discount_level', pr.discount_level,
            'discount_level_value', dlc.discount_level_value,
            'discount_type_id', tasm.id,
            'discount_type', pr.discount_type,
            'discount_display_name', tasm.display_name,
            'priority_number', CASE 
                WHEN pr.priority_number IS NOT NULL THEN
                    json_build_object(
                        'value', pr.priority_number,
                        'label', COALESCE(pn.priority_display_name, 'Unknown Priority')
                    )
                ELSE NULL
            END
        ) AS discount_rules,
        jsonb_build_object(
            'vf_type', pr.vf_type,
            'vf_fixed_amount', pr.vf_fixed_amount,
            'vf_per_unit', pr.vf_per_unit
        ) AS vendor_funding,
        jsonb_build_object(
            'min_upto_percent', pr.min_upto_percent,
            'max_upto_percent', pr.max_upto_percent,
            'products_on_max_upto_percent', pr.products_on_max_upto_percent
        ) AS upto_percent,
        CASE WHEN pr.vf_type IS NOT NULL THEN true ELSE false END AS has_vendor_funding,
        
        (
            SELECT COALESCE(
                jsonb_agg(
                    jsonb_build_object(
                        'value', discount_level_id,
                        'label', COALESCE(discount_level_value, 'Unknown')
                    )
                ),
                '[]'::jsonb
            )
            FROM price_promo.discount_level_config
            WHERE discount_level_id = ANY(pr.product_discount_level)
            AND category = 'product'
        ) AS product_discount_level,
        
        (
            SELECT COALESCE(
                jsonb_agg(
                    jsonb_build_object(
                        'value', discount_level_id,
                        'label', COALESCE(discount_level_value, 'Unknown')
                    )
                ),
                '[]'::jsonb
            )
            FROM price_promo.discount_level_config
            WHERE discount_level_id = ANY(pr.store_discount_level)
            AND category = 'store'
        ) AS store_discount_level,
        
        (
            SELECT COALESCE(
                jsonb_agg(
                    jsonb_build_object(
                        'value', discount_level_id,
                        'label', COALESCE(discount_level_value, 'Unknown')
                    )
                ),
                '[]'::jsonb
            )
            FROM price_promo.discount_level_config
            WHERE discount_level_id = ANY(pr.customer_discount_level)
            AND category = 'customer'
        ) AS customer_discount_level
    FROM price_promo.promo_master pm 
    join global.tb_currency_master as cm on pm.currency_id = cm.currency_id
    LEFT JOIN price_promo.ps_rules pr ON pm.promo_id = pr.promo_id
    LEFT JOIN price_promo.discount_level_config dlc ON pr.discount_level = dlc.discount_level_id
    LEFT JOIN metaschema.tb_app_sub_master tasm ON pr.discount_type = tasm.name AND tasm.master_id = 2
    LEFT JOIN price_promo.promo_status_config psc ON pm.status = psc.status_id
    LEFT JOIN price_promo.tb_priority_number pn ON pr.priority_number = pn.priority_number
    LEFT JOIN exmd_check ON pm.promo_id = exmd_check.promo_id
    WHERE pm.promo_id = {promo_id};
"""


FETCH_STEP1_BASICS_QUERY = """
    WITH 
        product_hierarchy_cte AS (
            SELECT json_build_object(
                {product_select_list}
            ) AS product_hierarchy
            FROM price_promo.included_product_hierarchy
            WHERE promo_id = {promo_id}
        ),
        product_ids_cte AS (
            SELECT COALESCE(jsonb_agg(product_id), '[]'::jsonb) AS product_ids
            FROM price_promo.included_products_{promo_id}
        ),
        product_group_ids_cte AS (
            SELECT COALESCE(jsonb_agg(product_group_id), '[]'::jsonb) AS product_group_ids
            FROM price_promo.included_promo_product_groups
            WHERE promo_id = {promo_id}
        ),
        customer_settings_cte AS (
            SELECT jsonb_build_object(
                'customer_type', jsonb_build_object('label', tctc.customer_type::text, 'value', pm.customer_type),
                'offer_distribution_channel', jsonb_build_object('label', todcc.channel::text, 'value', pm.offer_distribution_channel)
            ) AS customer_selections
            FROM price_promo.promo_master pm
            LEFT JOIN price_promo.tb_customer_type_config tctc ON pm.customer_type = tctc.id
            LEFT JOIN price_promo.tb_offer_distributor_channel_config todcc ON pm.offer_distribution_channel = todcc.id
            WHERE pm.promo_id = {promo_id}
        ),
        store_hierarchy_cte AS (
            SELECT json_build_object(
                {store_select_list}
            ) AS store_hierarchy
            FROM price_promo.promo_store_sg_hierarchy
            WHERE promo_id = {promo_id}
        ),
        store_ids_cte AS (
            SELECT COALESCE(jsonb_agg(store_id), '[]'::jsonb) AS store_ids
            FROM price_promo.promo_store
            WHERE promo_id = {promo_id}
        ),
        store_group_ids_cte AS (
            SELECT COALESCE(jsonb_agg(store_group_id), '[]'::jsonb) AS store_group_ids
            FROM price_promo.tb_promo_store_groups
            WHERE promo_id = {promo_id}
        ),
        exmd_check AS (
        SELECT 
            pm.promo_id,
            CASE 
                WHEN EXISTS (SELECT 1 FROM price_promo.tb_exmd_promo tep WHERE tep.promo_id = pm.promo_id) AND pm.step_count = 3
                THEN 4 
                ELSE pm.step_count 
            END AS max_enabled_step_count
        FROM price_promo.promo_master pm
        WHERE pm.promo_id = {promo_id}
    )
    SELECT
        pm.promo_id,
        pm.name AS promo_name,
        to_char(pm.start_date, 'MM/DD/YYYY') AS start_date,
        to_char(pm.end_date, 'MM/DD/YYYY') AS end_date,
        ((pm.end_date::date - pm.start_date::date) + 1) AS promo_days,
        pm.status AS status_id,
        psc.status_name::text as status,
        pm.step_count,
        exmd_check.max_enabled_step_count,
        pm.currency_id,
        cm.currency_symbol as currency_symbol,
        cm.currency_name as currency_name,
        json_build_object(
            'is_exclusion_added', pm.exclusion_selection_type IS NOT NULL,
            'product_selection_type_id', pm.product_selection_type,
            'product_selection_type', pstc.product_selection_type::text,
            'product_selection_sub_type', pstc.product_selection_sub_type::text,
            'products_count', pm.products_count,
            'total_products_count', (SELECT COUNT(DISTINCT product_id) FROM price_promo.product_master WHERE is_active = 1),
            'product_hierarchy', product_hierarchy_cte.product_hierarchy,
            'product_ids', product_ids_cte.product_ids,
            'product_group_ids', product_group_ids_cte.product_group_ids
        ) AS product_selections,
        customer_settings_cte.customer_selections AS customer_selections,
        json_build_object(
            'store_selection_type_id', pm.store_selection_type,
            'store_selection_type', sstc.store_selection_type::text,
            'store_selection_sub_type', sstc.store_selection_sub_type::text,
            'stores_count', pm.stores_count,
            'total_stores_count', (SELECT COUNT(DISTINCT store_id) FROM global.tb_store_master WHERE is_active = 1),
            'store_hierarchy', store_hierarchy_cte.store_hierarchy,
            'store_ids', store_ids_cte.store_ids,
            'store_group_ids', store_group_ids_cte.store_group_ids
        ) AS store_selections
    FROM price_promo.promo_master pm
    join global.tb_currency_master as cm on pm.currency_id = cm.currency_id
    LEFT JOIN price_promo.promo_status_config psc ON pm.status = psc.status_id
    LEFT JOIN price_promo.product_selection_type_config pstc ON pm.product_selection_type = pstc.id
    LEFT JOIN price_promo.store_selection_type_config sstc ON pm.store_selection_type = sstc.id
    LEFT JOIN product_hierarchy_cte ON true
    LEFT JOIN product_ids_cte ON true
    LEFT JOIN product_group_ids_cte ON true
    LEFT JOIN customer_settings_cte ON true
    LEFT JOIN store_hierarchy_cte ON true
    LEFT JOIN store_ids_cte ON true
    LEFT JOIN store_group_ids_cte ON true
    LEFT JOIN exmd_check ON pm.promo_id = exmd_check.promo_id
    WHERE pm.promo_id = {promo_id};
"""


FETCH_STEP1_DETAILS_QUERY = """
    WITH product_ids_details_cte AS (
        SELECT
            pm.*,
            pm.{client_product_id_key} as client_product_id,
            tcm.currency_symbol as currency_symbol,
            tll.oh AS oh_inventory,
            tll.it AS it_inventory,
            tll.oo AS oo_inventory
        FROM price_promo.included_products AS u
        LEFT JOIN price_promo.product_master AS pm 
            ON u.product_id = pm.product_id
        LEFT JOIN global.tb_latest_inventory_agg AS tll 
            ON u.product_id = tll.product_id
        join global.tb_currency_master tcm on tcm.currency_id = pm.currency_id
        WHERE u.promo_id = {promo_id}
    ),
    product_group_ids_details_cte AS (
        SELECT
            tppg.product_group_id,
            tpg.pg_name as product_group_name,
            tpg.description as product_group_description,
            CASE
                WHEN tpg.pg_grouping_type = 1 THEN 'Whole Category'
                ELSE 'Specific Products'
            END AS product_group_type,
            c_usr.name AS created_by_user,
            tpg.created_at,
            CASE
                WHEN tpg.created_at <> tpg.updated_at THEN u_usr.name
                ELSE NULL
            END AS modified_by_user,
            CASE
                WHEN tpg.created_at <> tpg.updated_at THEN tpg.updated_at
                ELSE NULL
            END AS modified_at,
            tpg.products_count,
            ARRAY_AGG(DISTINCT pm.l0_name) AS l0_name,
            ARRAY_AGG(DISTINCT pm.l1_name) AS l1_name,
            ARRAY_AGG(DISTINCT pm.l2_name) AS l2_name,
            COUNT(DISTINCT tppg.promo_id) AS promos_count
        FROM price_promo.included_promo_product_groups AS tppg
        LEFT JOIN global.tb_product_group AS tpg 
            ON tppg.product_group_id = tpg.pg_id
        LEFT JOIN global.tb_pg_product AS tpp 
            ON tpg.pg_id = tpp.pg_id
        LEFT JOIN price_promo.product_master AS pm 
            ON tpp.product_id = pm.product_id
        LEFT JOIN global.user_master AS c_usr 
            ON c_usr.user_code = tpg.created_by 
        LEFT JOIN global.user_master AS u_usr 
            ON u_usr.user_code = tpg.updated_by
        WHERE tppg.promo_id = {promo_id}
        GROUP BY
            tppg.product_group_id,
            tpg.pg_name,
            tpg.description,
            tpg.pg_grouping_type,
            tpg.created_at,
            tpg.products_count,
            tpg.created_at,
            tpg.updated_at,
            c_usr.name,
            u_usr.name
    ),
    store_ids_details_cte AS (
        SELECT
            tsm.*
        FROM price_promo.promo_store AS u
        LEFT JOIN global.tb_store_master AS tsm 
            ON u.store_id = tsm.store_id
        WHERE u.promo_id = {promo_id}
    ),
    store_group_ids_details_cte AS (
        SELECT
            tpsg.store_group_id,
            tsg.sg_name as store_group_name,
            tsg.description as store_group_description,
            c_usr.name AS created_by_user,
            tsg.created_at,
            CASE
                WHEN tsg.created_at <> tsg.updated_at THEN u_usr.name
                ELSE NULL
            END AS modified_by_user,
            CASE
                WHEN tsg.created_at <> tsg.updated_at THEN tsg.updated_at
                ELSE NULL
            END AS modified_at,
            tsg.stores_count,
            ARRAY_AGG(DISTINCT sm.s0_name) AS s0_name,
            ARRAY_AGG(DISTINCT sm.s1_name) AS s1_name,
            ARRAY_AGG(DISTINCT sm.s2_name) AS s2_name,
            COUNT(DISTINCT tpsg.promo_id) AS promos_count
        FROM price_promo.tb_promo_store_groups AS tpsg
        LEFT JOIN global.tb_store_group AS tsg 
            ON tpsg.store_group_id = tsg.sg_id
        LEFT JOIN global.tb_sg_store AS tss 
            ON tsg.sg_id = tss.sg_id
        LEFT JOIN global.tb_store_master AS sm 
            ON tss.store_id = sm.store_id
        LEFT JOIN global.user_master AS c_usr 
	        ON c_usr.user_code = tsg.created_by	 
        LEFT JOIN global.user_master AS u_usr 
            ON u_usr.user_code = tsg.updated_by
        WHERE tpsg.promo_id = {promo_id}
        GROUP BY
            tpsg.store_group_id,
            tsg.sg_name,
            tsg.description,
            c_usr.name,
            u_usr.name,
            tsg.created_at,
            tsg.updated_at,
            tsg.stores_count
    )
    SELECT
        pm.promo_id,
        pm.status AS status_id,
        psc.status_name::text AS status,
        pm.step_count,
        pm.product_selection_type AS product_selection_type_id,
        pstc.product_selection_type::text AS product_selection_type,
        pstc.product_selection_sub_type::text AS product_selection_sub_type,
        pm.store_selection_type AS store_selection_type_id,
        sstc.store_selection_type::text AS store_selection_type,
        sstc.store_selection_sub_type::text AS store_selection_sub_type,
        COALESCE(
            CASE
                WHEN pm.product_selection_type IN (4, 5, 6) THEN (
                    SELECT json_agg(product_ids_details_cte)
                    FROM product_ids_details_cte
                )
                ELSE NULL
            END,
            '[]'::json
        ) AS product_details,
        COALESCE(
            CASE
                WHEN pm.product_selection_type IN (3, 7) THEN (
                    SELECT json_agg(product_group_ids_details_cte)
                    FROM product_group_ids_details_cte
                )
                ELSE NULL
            END,
            '[]'::json
        ) AS product_group_details,
        COALESCE(
            CASE
                WHEN pm.store_selection_type IN (4, 5, 6) THEN (
                    SELECT json_agg(store_ids_details_cte)
                    FROM store_ids_details_cte
                )
                ELSE NULL
            END,
            '[]'::json
        ) AS store_details,
        COALESCE(
            CASE
                WHEN pm.store_selection_type IN (7) THEN (
                    SELECT json_agg(store_group_ids_details_cte)
                    FROM store_group_ids_details_cte
                )
                ELSE NULL
            END,
            '[]'::json
        ) AS store_group_details
    FROM price_promo.promo_master AS pm
    LEFT JOIN price_promo.promo_status_config AS psc 
        ON pm.status = psc.status_id
    LEFT JOIN price_promo.product_selection_type_config AS pstc 
        ON pm.product_selection_type = pstc.id
    LEFT JOIN price_promo.store_selection_type_config AS sstc 
        ON pm.store_selection_type = sstc.id
    WHERE pm.promo_id = {promo_id};

"""


FETCH_STEP1_EXCLUSIONS_QUERY = """
    select * from price_promo.fn_get_promo_exclusion_details({promo_id}) AS t(promo_id INT, exclusion_selection_type int2, product_exclusions JSONB);
"""


FETCH_STEP3_BASICS_QUERY = """
    with exmd_check AS (
        SELECT 
            pm.promo_id,
            CASE 
                WHEN EXISTS (SELECT 1 FROM price_promo.tb_exmd_promo tep WHERE tep.promo_id = pm.promo_id) AND pm.step_count = 3
                THEN 4 
                ELSE pm.step_count 
            END AS max_enabled_step_count
        FROM price_promo.promo_master pm
        WHERE pm.promo_id = {promo_id}
    )
    SELECT 
        pm.promo_id,
        pm.name AS promo_name,
        pm.currency_id,
        cm.currency_symbol as currency_symbol,
        cm.currency_name as currency_name,
        to_char(pm.start_date, 'MM/DD/YYYY') AS start_date,
        to_char(pm.end_date, 'MM/DD/YYYY') AS end_date,
        ((pm.end_date::date - pm.start_date::date) + 1) AS promo_days,
        pm.status AS status_id,
        psc.status_name::text as status,
        pm.step_count,
        exmd_check.max_enabled_step_count,
        CASE 
            WHEN EXISTS (SELECT 1 FROM price_promo.ps_recommended_scenarios_agg WHERE promo_id = {promo_id}) 
            THEN false 
            ELSE true 
        END AS enable_simulation,
        CASE 
            WHEN EXISTS (SELECT 1 FROM price_promo.ps_recommended_ia_projected_agg WHERE promo_id = {promo_id}) 
            THEN true 
            ELSE false 
        END AS is_optimised
    FROM price_promo.promo_master pm
    join global.tb_currency_master as cm on pm.currency_id = cm.currency_id
    LEFT JOIN price_promo.promo_status_config psc ON pm.status = psc.status_id
    LEFT JOIN exmd_check ON pm.promo_id = exmd_check.promo_id
    WHERE pm.promo_id =  {promo_id}
"""


FETCH_STEP3_OPTIMISE_QUERY = """
    SELECT price_promo.fn_save_workbench_ps_rules_objectives_metrics({promo_id});
    with optimisation_data_cte as (
        SELECT      
            opt_discount_type_id,
            opt_tasm.name as opt_discount_type
        FROM 
            price_promo.ps_rules pr
        INNER JOIN 
            metaschema.tb_app_sub_master opt_tasm 
            ON pr.opt_discount_type_id = opt_tasm.id AND opt_tasm.master_id = 2
        WHERE 
            pr.promo_id = {promo_id}
    ),
    targets_cte as (
        -- Fetch data from ps_rules (higher priority)
        SELECT      
            tasm.id AS discount_type_id,
            pr.discount_type,
            discount_type_values,
            min_discount,
            max_discount,
            gross_margin_target,
            gross_margin_lift,
            gross_margin_priority,
            revenue_target,
            revenue_lift,
            revenue_priority,
            units_target,
            units_lift,
            units_priority,
            gross_margin_percent_target,
            gross_margin_percent_lift,
            gross_margin_percent_priority,
            opt_discount_type_id,
            opt_tasm.name as opt_discount_type,
            ROUND(baseline_units::NUMERIC, 2) AS baseline_units,
            ROUND(baseline_revenue::NUMERIC, 2) AS baseline_revenue,
            ROUND(baseline_margin::NUMERIC, 2) AS baseline_gross_margin,
            ROUND(baseline_gm_percent::NUMERIC, 2) AS baseline_gross_margin_percent,
            ROUND(ly_units::NUMERIC, 2) AS ly_units,
            ROUND(ly_revenue::NUMERIC, 2) AS ly_revenue,
            ROUND(ly_margin::NUMERIC, 2) AS ly_gross_margin,
            ROUND(ly_gm_percent::NUMERIC, 2) AS ly_gross_margin_percent,
            maximization_parameter
        FROM 
            price_promo.ps_rules pr
        INNER JOIN 
            metaschema.tb_app_sub_master tasm 
            ON pr.discount_type = tasm.name AND tasm.master_id = 2
        LEFT JOIN 
            metaschema.tb_app_sub_master opt_tasm 
            ON pr.opt_discount_type_id = opt_tasm.id AND opt_tasm.master_id = 2
        WHERE 
            pr.promo_id = {promo_id}

    UNION ALL

    -- Fetch data from tb_placeholder_targets (lower priority if both present)
    SELECT
        tasm.id AS discount_type_id,
        pr.discount_type,
        NULL AS discount_type_values,
        NULL AS min_discount,
        NULL AS max_discount,
        tpt.gross_margin_target,
        NULL AS gross_margin_lift,
        NULL AS gross_margin_priority,
        tpt.revenue_target,
        NULL AS revenue_lift,
        NULL AS revenue_priority,
        tpt.units_target,
        NULL AS units_lift,
        NULL AS units_priority,
        tpt.gross_margin_percent_target,
        NULL AS gross_margin_percent_lift,
        NULL AS gross_margin_percent_priority,
        NULL AS opt_discount_type_id,
        NULL AS opt_discount_type,
        NULL AS baseline_units,
        NULL AS baseline_revenue,
        NULL AS baseline_gross_margin,
        NULL AS baseline_gross_margin_percent,
        NULL AS ly_units,
        NULL AS ly_revenue,
        NULL AS ly_gross_margin,
        NULL AS ly_gross_margin_percent,
        pr.maximization_parameter
    FROM 
        price_promo.tb_placeholder_targets tpt
    LEFT JOIN 
        price_promo.ps_rules pr ON tpt.promo_id = pr.promo_id
    LEFT JOIN 
        metaschema.tb_app_sub_master tasm ON 
        CASE 
            WHEN pr.discount_type IN ('fixed_price', 'extra_amount_off', 'percent_off', 'bxgy', 'bxgy_percent_off') THEN pr.discount_type
            ELSE 'percent_off'
        END = tasm.name
    WHERE 
        tpt.promo_id = {promo_id}
        AND NOT EXISTS (
            SELECT 1 FROM optimisation_data_cte
        )

    )
    select
        promo_id,
        pm.status AS status_id,
        psc.status_name::text as status,
        step_count,
        json_build_object(
                'discount_type_id', t.discount_type_id,
                'discount_type', t.discount_type,
                'discount_type_values', t.discount_type_values,
                'min_discount', t.min_discount,
                'max_discount', t.max_discount,
                'gross_margin_target', t.gross_margin_target,
                'gross_margin_lift', t.gross_margin_lift,
                'baseline_gross_margin', t.baseline_gross_margin,
                'ly_gross_margin', t.ly_gross_margin,
                'gross_margin_priority', t.gross_margin_priority,
                'revenue_target', t.revenue_target,
                'revenue_lift', t.revenue_lift,
                'baseline_revenue', t.baseline_revenue,
                'ly_revenue', t.ly_revenue,
                'revenue_priority', t.revenue_priority,
                'units_target', round(t.units_target::numeric),
                'units_lift', t.units_lift,
                'baseline_units', t.baseline_units,
                'ly_units', t.ly_units,
                'units_priority', t.units_priority,
                'gross_margin_percent_target', t.gross_margin_percent_target,
                'gross_margin_percent_lift', t.gross_margin_percent_lift,
                'baseline_gross_margin_percent', t.baseline_gross_margin_percent,
                'ly_gross_margin_percent', t.ly_gross_margin_percent,
                'gross_margin_percent_priority', t.gross_margin_percent_priority,
                'opt_discount_type_id', t.opt_discount_type_id,
                'opt_discount_type', t.opt_discount_type,
                'maximization_parameter', t.maximization_parameter
            ) AS targets
    FROM 
        price_promo.promo_master pm
    LEFT JOIN 
        targets_cte t ON pm.promo_id = {promo_id}
    LEFT JOIN price_promo.promo_status_config psc ON pm.status = psc.status_id
    WHERE 
        pm.promo_id = {promo_id};
"""


FETCH_STEP0_BASICS_QUERY = """
    WITH promo_details AS (
        SELECT
            promo_id,
            pm.name AS promo_name,
            to_char(pm.start_date, 'MM/DD/YYYY') AS start_date,
            to_char(pm.end_date, 'MM/DD/YYYY') AS end_date,
            pm.event_id as event_id,
            em.name as event_name,
            ((pm.end_date::date - pm.start_date::date) + 1) AS promo_days,
            pm.status AS status_id,
            psc.status_name::text AS status,
            pm.step_count,
            em.country_id,
            pm.currency_id
        FROM
            price_promo.promo_master pm
        LEFT JOIN
            price_promo.promo_status_config psc ON pm.status = psc.status_id
        LEFT JOIN
            price_promo.event_master em on em.event_id = pm.event_id
        WHERE
            promo_id = {promo_id}
    ),
    exmd_check AS (
        SELECT 
            pm.promo_id,
            CASE 
                WHEN EXISTS (SELECT 1 FROM price_promo.tb_exmd_promo tep WHERE tep.promo_id = pm.promo_id) AND pm.step_count = 3
                THEN 4 
                ELSE pm.step_count 
            END AS max_enabled_step_count
        FROM price_promo.promo_master pm
        WHERE pm.promo_id = {promo_id}
    )
    SELECT 
        pd.promo_id,
        pd.promo_name,
        pd.start_date,
        pd.end_date,
        pd.event_id,
        pd.event_name,
        pd.status_id,
        pd.promo_days,
        pd.status,
        pd.step_count,
        pd.country_id,
        pd.currency_id,
        cm.currency_symbol as currency_symbol,
        cm.currency_name as currency_name,
        exmd_check.max_enabled_step_count,
        CASE 
            WHEN pd.status_id = -1 THEN json_build_object(
                'discount', pt.discount,
                'inventory', pt.inventory,
                'revenue_target', pt.revenue_target,
                'units_target', pt.units_target,
                'gross_margin_target', pt.gross_margin_target,
                'gross_margin_percent_target', pt.gross_margin_percent_target
            )
            ELSE json_build_object()
        END AS metrics
    FROM 
        promo_details pd
    join
        global.tb_currency_master as cm on pd.currency_id = cm.currency_id
    LEFT JOIN 
        price_promo.tb_placeholder_targets pt 
        ON pd.promo_id = pt.promo_id
        AND pd.status_id = -1
    LEFT JOIN 
        exmd_check 
        ON pd.promo_id = exmd_check.promo_id;
"""

FETCH_BMSM_OFFER_TYPES = """
select
	x_value_type as x_value_types, 
	array_agg(y_value_type) as y_value_types
from
	{promo_schema}.bmsm_config bc
group by
	x_value_type
"""

FETCH_TIERS_INFO = """
WITH tier_info AS (
    SELECT 
        td.tier_id,
        jsonb_agg(
            jsonb_build_object(
                'sub_tier_id', td.sub_tier_id,
                'offer_x_value', td.offer_x_value,
                'offer_x_type', td.offer_x_type,
                'offer_y_value', td.offer_y_value,
                'offer_y_type', td.offer_y_type,
                'offer_z_value', td.offer_z_value,
                'offer_z_type', td.offer_z_type,
                'display_name', 'Buy ' || 
                    td.offer_x_value || ' ' || 
                    CASE
                        WHEN td.offer_x_type = 'unit'
                        AND td.offer_x_value = 1 THEN 'unit'
                        WHEN td.offer_x_type = 'unit'
                        AND td.offer_x_value > 1 THEN 'units'
                        WHEN td.offer_x_type = 'dollar' THEN '$' || td.offer_x_value
                        ELSE td.offer_x_type
                    END || 
                    ' Get ' || 
                    CASE
                        WHEN td.offer_y_type = 'percent off' THEN td.offer_y_value || ' % off'
                        WHEN td.offer_y_type = 'dollar off' THEN '$' || td.offer_y_value || ' off'
                        WHEN td.offer_y_type = 'at dollar' THEN 'at $' || td.offer_y_value
                    END
            )
        ) AS tier_information
    FROM
        {promo_schema}.tier_discounts td
    GROUP BY
        td.tier_id
),
tier_details AS (
    SELECT
        tm.promo_id,
        tm.tier_id,
        tm.tier_name,
        tm.offer_type,
        tm.offer_type_id,
        tm.sub_tier_count,
        ti.tier_information
    FROM {promo_schema}.tier_master tm 
    JOIN tier_info ti ON
        tm.tier_id = ti.tier_id
    WHERE
        tm.promo_id = {promo_id}
)
SELECT 
    td.tier_id,
    td.tier_name,
    td.offer_type,
    td.offer_type_id,
    td.sub_tier_count,
    td.tier_information,
    CASE 
        WHEN EXISTS (
            SELECT 1
            FROM {promo_schema}.ps_scenario_discounts psd
            WHERE psd.tier_id = td.tier_id
            AND psd.promo_id = {promo_id}
        ) THEN true
        ELSE false
    END AS has_scenario
FROM
    tier_details td;
"""


DELETE_TIER_INFO = """
-- Delete from tier master (cascades to tier_discounts)
delete
from
	{promo_schema}.tier_master
where
	tier_id = {tier_id};
"""

CREATE_TIER_QUERY = """
CREATE TEMP TABLE TEMP_TIER_ID (tier_id INTEGER);

DO $$
DECLARE
    _tier_id integer;
BEGIN

    -- Insert data into tier master generating tier id
    insert
        into
        {promo_schema}.tier_master (
            promo_id,
            tier_name,
            offer_type,
            offer_type_id,
            sub_tier_count
        )
        values
        (
            {promo_id},
            {tier_name},
            {tier_offer_type},
            {tier_offer_type_id},
            {sub_tier_count}
        )
        returning tier_id into _tier_id;
        
    -- Insert all sub tiers into tier discounts table
    insert
        into
        {promo_schema}.tier_discounts (
            tier_id,
            offer_x_value,
            offer_x_type,
            offer_y_value,
            offer_y_type,
            offer_z_value,
            offer_z_type,
            display_name
        )
        select
            A._tier_id,
            t.offer_x_value,
            t.offer_x_type,
            t.offer_y_value,
            t.offer_y_type,
            t.offer_z_value,
            t.offer_z_type,
            t.display_name
        from
            (select _tier_id) as A,
            (values {values_clause}
            ) AS t(offer_x_value, offer_x_type, offer_y_value, offer_y_type, offer_z_value, offer_z_type, display_name);

    -- Insert into temp table
    INSERT INTO TEMP_TIER_ID 
    (tier_id) 
    values
    (_tier_id);

END 
$$;

select tier_id from TEMP_TIER_ID;
"""


EDIT_TIER_QUERY = """
DROP TABLE IF EXISTS TEMP_TIER_ID;
CREATE TEMP TABLE TEMP_TIER_ID (tier_id INTEGER);

DO $$
DECLARE
    _promo_id integer;
    _scenario_id integer;
    _tier_id integer := {tier_id};
BEGIN

    -- Delete existing data from tier master table and tier discounts table
    delete from {promo_schema}.tier_master
    where tier_id = {tier_id};
    delete from {promo_schema}.tier_discounts
    where tier_id = {tier_id};

    -- Insert data into tier master generating tier id (if it does not exist)
    insert
        into
        {promo_schema}.tier_master (
            promo_id,
            tier_id,
            tier_name,
            offer_type,
            offer_type_id,
            sub_tier_count
        )
        values
        (
            {promo_id},
            _tier_id,
            {tier_name},
            {tier_offer_type},
            {tier_offer_type_id},
            {sub_tier_count}
        );
        
    -- Insert all sub tiers into tier discounts table
    insert
        into
        {promo_schema}.tier_discounts (
            tier_id,
            offer_x_value,
            offer_x_type,
            offer_y_value,
            offer_y_type,
            offer_z_value,
            offer_z_type,
            display_name
        )
        select
            A._tier_id,
            t.offer_x_value,
            t.offer_x_type,
            t.offer_y_value,
            t.offer_y_type,
            t.offer_z_value,
            t.offer_z_type,
            t.display_name
        from
            (select _tier_id) as A,
            (values {values_clause}
            ) AS t(offer_x_value, offer_x_type, offer_y_value, offer_y_type, offer_z_value, offer_z_type, display_name);

    -- Insert into temp table
    INSERT INTO TEMP_TIER_ID 
    (tier_id) 
    values
    (_tier_id);

END 
$$;

select tier_id from TEMP_TIER_ID;
"""

FETCH_STEP3_SIMULATION_RESULTS_QUERY = """
    with target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master
                    WHERE promo_id = {promo_id}
                ),
                {target_currency_id}::integer
        )
    ),
    resimulation_metrics_cte as (
        select
            prsa.promo_id,
            'resimulation' as scenario_type,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_scenarios_agg prsa 
        left join price_promo.scenario_master sm on prsa.promo_id=sm.promo_id and prsa.scenario_id=sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name
    ),
    optimise_metrics_cte as (
        select
            prip.promo_id,
            'optimise' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'IA Recommended' as scenario_name,
            --
            sum(prip.sales_units) as sales_units,
            sum(prip.baseline_sales_units) as baseline_sales_units,
            sum(prip.incremental_sales_units) as incremental_sales_units,
            --
            sum(prip.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prip.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prip.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prip.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prip.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prip.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prip.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prip.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prip.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prip.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prip.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prip.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prip.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prip.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prip.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_ia_projected_agg prip
        inner join 
            global.planned_forex_rate pfr 
            on 
                prip.recommendation_date = pfr.date 
                and pfr.source_currency_id = prip.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prip.promo_id = {promo_id}
        group by 
            prip.promo_id
    ),
    override_resimulation_metrics_cte as (
        select
            prsa.promo_id,
            'override_resimulation' as scenario_type,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_override_agg prsa 
        left join price_promo.scenario_master sm on prsa.promo_id=sm.promo_id and prsa.scenario_id=sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name
    ),
    override_optimise_metrics_cte as (
        select
            prsa.promo_id,
            'override_optimise' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'Override IA Recommended' as scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_override_ia_agg prsa 
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id
    ),
    scenarios_stack_metrics as (
        select
            prsa.promo_id,
            'override_resimulation' as scenario_type,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_scenarios_stack_agg prsa 
        left join price_promo.scenario_master sm on prsa.promo_id=sm.promo_id and prsa.scenario_id=sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name
    ),
    scenarios_stack_override_metrics as (
        select
            prsa.promo_id,
            'override_resimulation' as scenario_type,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_scenarios_stack_override_agg prsa 
        left join price_promo.scenario_master sm on prsa.promo_id=sm.promo_id and prsa.scenario_id=sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name
    ),
    stack_ia_metrics as (
        select
            prsa.promo_id,
            'override_optimise' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'Override IA Recommended' as scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_stack_ia_agg prsa 
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id

    ),
    stack_override_ia_metrics as (
        select
            prsa.promo_id,
            'override_optimise' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'Override IA Recommended' as scenario_name,
            --
            sum(prsa.sales_units) as sales_units,
            sum(prsa.baseline_sales_units) as baseline_sales_units,
            sum(prsa.incremental_sales_units) as incremental_sales_units,
            --
            sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
            sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
            sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
            sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
            sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
            sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
            --
            sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
            sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
            sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
            sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
            sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
            sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
            --
            sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend,
            --
            sum(prsa.contribution_revenue * pfr.planned_conversion_multiplier) as contribution_revenue,
            sum(prsa.contribution_margin * pfr.planned_conversion_multiplier) as contribution_margin
        from price_promo.ps_recommended_stack_override_ia_agg prsa 
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where prsa.promo_id = {promo_id}
        group by 
            prsa.promo_id

    ),
    final_metrics_cte as (
        select * from resimulation_metrics_cte
        union all
        select * from optimise_metrics_cte
    ),
    override_final_metrics_cte as (
        select * from override_resimulation_metrics_cte
        union all
        select * from override_optimise_metrics_cte
    ),
    stack_final_metrics_cte as (
        select * from scenarios_stack_metrics
        union all
        select * from stack_ia_metrics
    ),
    stack_override_final_metrics_cte as (
        select * from scenarios_stack_override_metrics
        union all
        select * from stack_override_ia_metrics
    )
    select
        pm.promo_id,
        pm.name as promo_name,
        pm.recommendation_type_id,
        tasm.name as recommendation_type,
        pm.last_approved_scenario_id,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        start_date - current_date as days_to_promo_start,
        case
            when pm.start_date >= current_date and (start_date - current_date) > 5 and pm.status <> 6 then true
            else false
        end as show_override,
        case
            when pm.start_date >= current_date and (start_date - current_date) > 5 and pm.status <> 6 then true
            else false
        end as show_stacked_override,
        pm.has_stacked_offers as enable_stacking_view,
        COALESCE(
            json_agg(
                CASE WHEN fmc.scenario_id IS NOT NULL THEN
                    json_build_object(
                        'scenario_type', fmc.scenario_type,
                        'scenario_id', fmc.scenario_id,
                        'scenario_order_id', fmc.scenario_order_id,
                        'scenario_name', fmc.scenario_name,
                        'original', COALESCE(
                            (
                                SELECT json_build_object(
                                    'sales_units', ROUND(fmc.sales_units::DECIMAL, 4),
                                    'baseline_sales_units', ROUND(fmc.baseline_sales_units::DECIMAL, 4),
                                    'incremental_sales_units', ROUND(fmc.incremental_sales_units::DECIMAL, 2),
                                    'total_sales_units', ROUND(fmc.sales_units::DECIMAL, 4),
                                    'revenue', ROUND(fmc.revenue::DECIMAL, 2),
                                    'baseline_revenue', ROUND(fmc.baseline_revenue::DECIMAL, 2),
                                    'incremental_revenue', ROUND(fmc.incremental_revenue::DECIMAL, 2),
                                    'affinity_revenue', ROUND(fmc.affinity_revenue::DECIMAL, 2),
                                    'cannibalization_revenue', ROUND(fmc.cannibalization_revenue::DECIMAL, 2),
                                    'pull_forward_revenue', ROUND(fmc.pull_forward_revenue::DECIMAL, 2),
                                    'total_revenue', ROUND(fmc.revenue::DECIMAL, 2),
                                    'margin', ROUND(fmc.margin::DECIMAL, 2),
                                    'baseline_margin', ROUND(fmc.baseline_margin::DECIMAL, 2),
                                    'incremental_margin', ROUND(fmc.incremental_margin::DECIMAL, 2),
                                    'affinity_margin', ROUND(fmc.affinity_margin::DECIMAL, 2),
                                    'cannibalization_margin', ROUND(fmc.cannibalization_margin::DECIMAL, 2),
                                    'pull_forward_margin', ROUND(fmc.pull_forward_margin::DECIMAL, 2),
                                    'total_margin', ROUND(fmc.margin::DECIMAL, 2),
                                    'promo_spend', ROUND(fmc.promo_spend::DECIMAL, 2),
                                    'aur', CASE WHEN fmc.sales_units = 0 THEN 0 ELSE ROUND((fmc.revenue / fmc.sales_units)::DECIMAL, 2) END,
                                    'aum', CASE WHEN fmc.sales_units = 0 THEN 0 ELSE ROUND((fmc.margin / fmc.sales_units)::DECIMAL, 2) END,
                                    'gm_percent', CASE WHEN fmc.revenue = 0 THEN 0 ELSE ROUND((fmc.margin * 100 / fmc.revenue)::DECIMAL, 2) END,
                                    'contribution_margin', ROUND(fmc.contribution_margin::DECIMAL, 2),
                                    'contribution_margin_percent', CASE WHEN fmc.contribution_revenue = 0 THEN 0 ELSE ROUND((fmc.contribution_margin * 100 / fmc.contribution_revenue)::DECIMAL, 2) END,
                                    'total_inventory', COALESCE(pm.total_inventory, 0),
                                    'finalized_st_percent',  LEAST(100, GREATEST(0, CASE 
                                                    WHEN COALESCE(pm.total_inventory, 0) = 0 THEN 0 
                                                    ELSE ROUND(((fmc.sales_units / pm.total_inventory) * 100)::DECIMAL, 1) 
                                                END))
                                )
                            ), 
                            '{{}}'
                        ),
                        'overridden', COALESCE(
                            (
                                SELECT json_build_object(
                                    'sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'baseline_sales_units', ROUND(ormc.baseline_sales_units::DECIMAL, 4),
                                    'incremental_sales_units', ROUND(ormc.incremental_sales_units::DECIMAL, 2),
                                    'total_sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'baseline_revenue', ROUND(ormc.baseline_revenue::DECIMAL, 2),
                                    'incremental_revenue', ROUND(ormc.incremental_revenue::DECIMAL, 2),
                                    'affinity_revenue', ROUND(ormc.affinity_revenue::DECIMAL, 2),
                                    'cannibalization_revenue', ROUND(ormc.cannibalization_revenue::DECIMAL, 2),
                                    'pull_forward_revenue', ROUND(ormc.pull_forward_revenue::DECIMAL, 2),
                                    'total_revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'baseline_margin', ROUND(ormc.baseline_margin::DECIMAL, 2),
                                    'incremental_margin', ROUND(ormc.incremental_margin::DECIMAL, 2),
                                    'affinity_margin', ROUND(ormc.affinity_margin::DECIMAL, 2),
                                    'cannibalization_margin', ROUND(ormc.cannibalization_margin::DECIMAL, 2),
                                    'pull_forward_margin', ROUND(ormc.pull_forward_margin::DECIMAL, 2),
                                    'total_margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'promo_spend', ROUND(ormc.promo_spend::DECIMAL, 2),
                                    'aur', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.revenue / ormc.sales_units)::DECIMAL, 2) END,
                                    'aum', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.margin / ormc.sales_units)::DECIMAL, 2) END,
                                    'gm_percent', CASE WHEN ormc.revenue = 0 THEN 0 ELSE ROUND((ormc.margin * 100 / ormc.revenue)::DECIMAL, 2) END,
                                    'contribution_margin', ROUND(ormc.contribution_margin::DECIMAL, 2),
                                    'contribution_margin_percent', CASE WHEN ormc.contribution_revenue = 0 THEN 0 ELSE ROUND((ormc.contribution_margin * 100 / ormc.contribution_revenue)::DECIMAL, 2) END,
                                    'total_inventory', COALESCE(pm.total_inventory, 0),
                                    'finalized_st_percent',  LEAST(100, GREATEST(0, CASE 
                                                    WHEN COALESCE(pm.total_inventory, 0) = 0 THEN 0 
                                                    ELSE ROUND(((fmc.sales_units / pm.total_inventory) * 100)::DECIMAL, 1) 
                                                END))
                                )
                                FROM
                                    override_final_metrics_cte ormc
                                WHERE
                                    ormc.promo_id = fmc.promo_id
                                    AND ormc.scenario_id = fmc.scenario_id
                            ), 
                            '{{}}'
                        ),
                        'stacked_original', COALESCE(
                            (
                                SELECT json_build_object(
                                    'sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'baseline_sales_units', ROUND(ormc.baseline_sales_units::DECIMAL, 4),
                                    'incremental_sales_units', ROUND(ormc.incremental_sales_units::DECIMAL, 2),
                                    'total_sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'baseline_revenue', ROUND(ormc.baseline_revenue::DECIMAL, 2),
                                    'incremental_revenue', ROUND(ormc.incremental_revenue::DECIMAL, 2),
                                    'affinity_revenue', ROUND(ormc.affinity_revenue::DECIMAL, 2),
                                    'cannibalization_revenue', ROUND(ormc.cannibalization_revenue::DECIMAL, 2),
                                    'pull_forward_revenue', ROUND(ormc.pull_forward_revenue::DECIMAL, 2),
                                    'total_revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'baseline_margin', ROUND(ormc.baseline_margin::DECIMAL, 2),
                                    'incremental_margin', ROUND(ormc.incremental_margin::DECIMAL, 2),
                                    'affinity_margin', ROUND(ormc.affinity_margin::DECIMAL, 2),
                                    'cannibalization_margin', ROUND(ormc.cannibalization_margin::DECIMAL, 2),
                                    'pull_forward_margin', ROUND(ormc.pull_forward_margin::DECIMAL, 2),
                                    'total_margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'promo_spend', ROUND(ormc.promo_spend::DECIMAL, 2),
                                    'aur', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.revenue / ormc.sales_units)::DECIMAL, 2) END,
                                    'aum', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.margin / ormc.sales_units)::DECIMAL, 2) END,
                                    'gm_percent', CASE WHEN ormc.revenue = 0 THEN 0 ELSE ROUND((ormc.margin * 100 / ormc.revenue)::DECIMAL, 2) END,
                                    'contribution_margin', ROUND(ormc.contribution_margin::DECIMAL, 2),
                                    'contribution_margin_percent', CASE WHEN ormc.contribution_revenue = 0 THEN 0 ELSE ROUND((ormc.contribution_margin * 100 / ormc.contribution_revenue)::DECIMAL, 2) END,
                                    'total_inventory', COALESCE(pm.total_inventory, 0),
                                    'finalized_st_percent',  LEAST(100, GREATEST(0, CASE 
                                                    WHEN COALESCE(pm.total_inventory, 0) = 0 THEN 0 
                                                    ELSE ROUND(((fmc.sales_units / pm.total_inventory) * 100)::DECIMAL, 1) 
                                                END))
                                )
                                FROM
                                    stack_final_metrics_cte ormc
                                WHERE
                                    ormc.promo_id = fmc.promo_id
                                    AND ormc.scenario_id = fmc.scenario_id
                            ), 
                            '{{}}'
                        ),
                        'stacked_overridden', COALESCE(
                            (
                                SELECT json_build_object(
                                    'sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'baseline_sales_units', ROUND(ormc.baseline_sales_units::DECIMAL, 4),
                                    'incremental_sales_units', ROUND(ormc.incremental_sales_units::DECIMAL, 2),
                                    'total_sales_units', ROUND(ormc.sales_units::DECIMAL, 4),
                                    'revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'baseline_revenue', ROUND(ormc.baseline_revenue::DECIMAL, 2),
                                    'incremental_revenue', ROUND(ormc.incremental_revenue::DECIMAL, 2),
                                    'affinity_revenue', ROUND(ormc.affinity_revenue::DECIMAL, 2),
                                    'cannibalization_revenue', ROUND(ormc.cannibalization_revenue::DECIMAL, 2),
                                    'pull_forward_revenue', ROUND(ormc.pull_forward_revenue::DECIMAL, 2),
                                    'total_revenue', ROUND(ormc.revenue::DECIMAL, 2),
                                    'margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'baseline_margin', ROUND(ormc.baseline_margin::DECIMAL, 2),
                                    'incremental_margin', ROUND(ormc.incremental_margin::DECIMAL, 2),
                                    'affinity_margin', ROUND(ormc.affinity_margin::DECIMAL, 2),
                                    'cannibalization_margin', ROUND(ormc.cannibalization_margin::DECIMAL, 2),
                                    'pull_forward_margin', ROUND(ormc.pull_forward_margin::DECIMAL, 2),
                                    'total_margin', ROUND(ormc.margin::DECIMAL, 2),
                                    'promo_spend', ROUND(ormc.promo_spend::DECIMAL, 2),
                                    'aur', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.revenue / ormc.sales_units)::DECIMAL, 2) END,
                                    'aum', CASE WHEN ormc.sales_units = 0 THEN 0 ELSE ROUND((ormc.margin / ormc.sales_units)::DECIMAL, 2) END,
                                    'gm_percent', CASE WHEN ormc.revenue = 0 THEN 0 ELSE ROUND((ormc.margin * 100 / ormc.revenue)::DECIMAL, 2) END,
                                    'contribution_margin', ROUND(ormc.contribution_margin::DECIMAL, 2),
                                    'contribution_margin_percent', CASE WHEN ormc.contribution_revenue = 0 THEN 0 ELSE ROUND((ormc.contribution_margin * 100 / ormc.contribution_revenue)::DECIMAL, 2) END,
                                    'total_inventory', COALESCE(pm.total_inventory, 0),
                                    'finalized_st_percent',  LEAST(100, GREATEST(0, CASE 
                                                    WHEN COALESCE(pm.total_inventory, 0) = 0 THEN 0 
                                                    ELSE ROUND(((fmc.sales_units / pm.total_inventory) * 100)::DECIMAL, 1) 
                                                END))
                                )
                                FROM
                                    stack_override_final_metrics_cte ormc
                                WHERE
                                    ormc.promo_id = fmc.promo_id
                                    AND ormc.scenario_id = fmc.scenario_id
                            ), 
                            '{{}}'
                        ),
                        'is_overridden', case when tpof.is_default is not null then true else false end,
                        'default_selected',   
                            case    
                                when tpof.is_default is true then 'overridden'
                                else 'original'
                            end
                    )
            END
            ),
            '[]'::json
        ) AS scenario_data
    from 
        price_promo.promo_master pm
        left join final_metrics_cte fmc on pm.promo_id = fmc.promo_id
        left join metaschema.tb_app_sub_master tasm on pm.recommendation_type_id = tasm.id
        left join price_promo.tb_promo_override_forecast tpof 
            on tpof.promo_id = fmc.promo_id and tpof.scenario_id = fmc.scenario_id
        inner join 
            global.tb_currency_master tcm 
            on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
        where 
        pm.promo_id = {promo_id}
    group by
        pm.promo_id,
        pm.name,
        pm.recommendation_type_id,
        tasm.name,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        pm.last_approved_scenario_id;
"""


VALIDATE_DELETE_TIER = """
select
	tier_id
from
	{promo_schema}.ps_scenario_discounts
where
	promo_id = {promo_id}
	and tier_id = {tier_id}
"""

FETCH_SCENARIO_ID_DATA = """
select
	promo_id,
	array_agg(scenario_id) as scenario_id_list
from
	{promo_schema}.scenario_master
where
	promo_id = {promo_id}
group by promo_id 
"""

EDIT_PROMOS_QUERY = """
DO $$
BEGIN
    {exec_query}
END $$;
"""


BASE_OVERALL_SIM_RESULTS_DEATILS_QUERY = """
    with target_currency_cte AS (
        SELECT 
            fn_get_target_currency_id as target_currency_id  
        from 
            price_promo.fn_get_target_currency_id(
                (
                    SELECT array_agg(DISTINCT currency_id) as source_currency_id
                    FROM price_promo.promo_master
                    WHERE promo_id = {promo_id}
                ),
                {target_currency_id}::integer
        )
    ),
    resimulation_metrics_cte AS (
        SELECT
            prsa.promo_id,
            'resimulation' AS scenario_type,
            prsa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            prsa.sales_units AS sales_units,
            prsa.baseline_sales_units AS baseline_sales_units,
            prsa.incremental_sales_units AS incremental_sales_units,
            --
            prsa.revenue * pfr.planned_conversion_multiplier AS revenue,
            prsa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prsa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prsa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prsa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prsa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prsa.margin * pfr.planned_conversion_multiplier AS margin,
            prsa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prsa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prsa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prsa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prsa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prsa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM 
            price_promo.ps_recommended_scenarios_agg prsa 
        LEFT JOIN 
            price_promo.scenario_master sm ON prsa.promo_id = sm.promo_id AND prsa.scenario_id = sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE 
            prsa.promo_id = {promo_id}
    ),
    optimize_metrics_cte AS (
        SELECT
            prsa.promo_id,
            'optimize' AS scenario_type,
            0 AS scenario_id,
            0 as scenario_order_id,
            'IA Recommended' AS scenario_name,
            --
            prsa.sales_units AS sales_units,
            prsa.baseline_sales_units AS baseline_sales_units,
            prsa.incremental_sales_units AS incremental_sales_units,
            --
            prsa.revenue * pfr.planned_conversion_multiplier AS revenue,
            prsa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prsa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prsa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prsa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prsa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prsa.margin * pfr.planned_conversion_multiplier AS margin,
            prsa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prsa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prsa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prsa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prsa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prsa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM 
            price_promo.ps_recommended_ia_projected_agg prsa 
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE 
            prsa.promo_id = {promo_id}
    ),
    override_simulation_metrics_cte AS (
        SELECT
            proa.promo_id,
            'override_resimulation' as scenario_type,
            proa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            proa.sales_units AS sales_units,
            proa.baseline_sales_units AS baseline_sales_units,
            proa.incremental_sales_units AS incremental_sales_units,
            --
            proa.revenue * pfr.planned_conversion_multiplier AS revenue,
            proa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            proa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            proa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            proa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            proa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            proa.margin * pfr.planned_conversion_multiplier AS margin,
            proa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            proa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            proa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            proa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            proa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            proa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM 
            price_promo.ps_recommended_override_agg proa
        LEFT JOIN 
            price_promo.scenario_master sm ON proa.promo_id = sm.promo_id AND proa.scenario_id = sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                proa.recommendation_date = pfr.date 
                and pfr.source_currency_id = proa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            proa.promo_id = {promo_id}
    ),
    override_optimize_metrics AS (
        SELECT
            proa.promo_id,
            'override_optimize' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'Override IA Recommended' as scenario_name,
            --
            proa.sales_units AS sales_units,
            proa.baseline_sales_units AS baseline_sales_units,
            proa.incremental_sales_units AS incremental_sales_units,
            --
            proa.revenue * pfr.planned_conversion_multiplier AS revenue,
            proa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            proa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            proa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            proa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            proa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            proa.margin * pfr.planned_conversion_multiplier AS margin,
            proa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            proa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            proa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            proa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            proa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            proa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM
            price_promo.ps_recommended_override_ia_agg proa
        inner join 
            global.planned_forex_rate pfr 
            on 
                proa.recommendation_date = pfr.date 
                and pfr.source_currency_id = proa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            proa.promo_id = {promo_id}
    ),
    stacked_resimulation_cte AS (
        SELECT
            prssa.promo_id,
            'stacked_resimulation' as scenario_type,
            prssa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            prssa.sales_units AS sales_units,
            prssa.baseline_sales_units AS baseline_sales_units,
            prssa.incremental_sales_units AS incremental_sales_units,
            --
            prssa.revenue * pfr.planned_conversion_multiplier AS revenue,
            prssa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prssa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prssa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prssa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prssa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prssa.margin * pfr.planned_conversion_multiplier AS margin,
            prssa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prssa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prssa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prssa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prssa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prssa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM
            price_promo.ps_recommended_scenarios_stack_agg prssa
        LEFT JOIN 
            price_promo.scenario_master sm ON prssa.promo_id = sm.promo_id AND prssa.scenario_id = sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prssa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prssa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            prssa.promo_id = {promo_id}
    ),
    stacked_override_resimulation_cte AS (
        SELECT
            prssoa.promo_id,
            'stacked_override_resimulation' as scenario_type,
            prssoa.scenario_id,
            sm.scenario_order_id,
            sm.scenario_name,
            --
            prssoa.sales_units AS sales_units,
            prssoa.baseline_sales_units AS baseline_sales_units,
            prssoa.incremental_sales_units AS incremental_sales_units,
            --
            prssoa.revenue * pfr.planned_conversion_multiplier AS revenue,
            prssoa.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prssoa.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prssoa.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prssoa.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prssoa.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prssoa.margin * pfr.planned_conversion_multiplier AS margin,
            prssoa.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prssoa.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prssoa.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prssoa.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prssoa.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prssoa.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM
            price_promo.ps_recommended_scenarios_stack_override_agg prssoa
        LEFT JOIN 
            price_promo.scenario_master sm ON prssoa.promo_id = sm.promo_id AND prssoa.scenario_id = sm.scenario_id
        inner join 
            global.planned_forex_rate pfr 
            on 
                prssoa.recommendation_date = pfr.date 
                and pfr.source_currency_id = prssoa.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            prssoa.promo_id = {promo_id}
    ),
    stacked_optimize_cte AS (
        SELECT
            prsia.promo_id,
            'stacked_optimize' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'IA Recommended' as scenario_name,
            --
            prsia.sales_units AS sales_units,
            prsia.baseline_sales_units AS baseline_sales_units,
            prsia.incremental_sales_units AS incremental_sales_units,
            --
            prsia.revenue * pfr.planned_conversion_multiplier AS revenue,
            prsia.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prsia.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prsia.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prsia.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prsia.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prsia.margin * pfr.planned_conversion_multiplier AS margin,
            prsia.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prsia.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prsia.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prsia.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prsia.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prsia.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM
            price_promo.ps_recommended_stack_ia_agg prsia
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsia.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsia.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            prsia.promo_id = {promo_id}
    ),
    stacked_override_optimize_cte AS (
        SELECT
            prsoia.promo_id,
            'stacked_override_optimize' as scenario_type,
            0 as scenario_id,
            0 as scenario_order_id,
            'Override IA Recommended' as scenario_name,
            --
            prsoia.sales_units AS sales_units,
            prsoia.baseline_sales_units AS baseline_sales_units,
            prsoia.incremental_sales_units AS incremental_sales_units,
            --
            prsoia.revenue * pfr.planned_conversion_multiplier AS revenue,
            prsoia.baseline_revenue * pfr.planned_conversion_multiplier AS baseline_revenue,
            prsoia.incremental_revenue * pfr.planned_conversion_multiplier AS incremental_revenue,
            prsoia.affinity_revenue * pfr.planned_conversion_multiplier AS affinity_revenue,
            prsoia.cannibalization_revenue * pfr.planned_conversion_multiplier AS cannibalization_revenue,
            prsoia.pull_forward_revenue * pfr.planned_conversion_multiplier AS pull_forward_revenue,
            --
            prsoia.margin * pfr.planned_conversion_multiplier AS margin,
            prsoia.baseline_margin * pfr.planned_conversion_multiplier AS baseline_margin,
            prsoia.incremental_margin * pfr.planned_conversion_multiplier AS incremental_margin,
            prsoia.affinity_margin * pfr.planned_conversion_multiplier AS affinity_margin,
            prsoia.cannibalization_margin * pfr.planned_conversion_multiplier AS cannibalization_margin,
            prsoia.pull_forward_margin * pfr.planned_conversion_multiplier AS pull_forward_margin,
            --
            prsoia.promo_spend * pfr.planned_conversion_multiplier AS promo_spend
        FROM
            price_promo.ps_recommended_stack_override_ia_agg prsoia
        inner join 
            global.planned_forex_rate pfr 
            on 
                prsoia.recommendation_date = pfr.date 
                and pfr.source_currency_id = prsoia.currency_id
                and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
        WHERE
            prsoia.promo_id = {promo_id}
    ),
    final_metrics_cte AS (
        SELECT * FROM resimulation_metrics_cte
        UNION ALL
        SELECT * FROM optimize_metrics_cte
        UNION ALL
        SELECT * FROM override_simulation_metrics_cte
        UNION ALL
        SELECT * FROM override_optimize_metrics
        UNION ALL
        SELECT * FROM stacked_resimulation_cte
        UNION ALL
        SELECT * FROM stacked_override_resimulation_cte
        UNION ALL
        SELECT * FROM stacked_optimize_cte
        UNION ALL
        SELECT * FROM stacked_override_optimize_cte
    ),
    override_forecast_metrics_cte AS (
        SELECT
            tpof.promo_id,
            tpof.scenario_id,
            tpof.is_default
        FROM
            price_promo.tb_promo_override_forecast tpof
        WHERE
            tpof.promo_id = {promo_id}
    ),
    agg_metrics_cte AS (
        SELECT
            pm.promo_id,
            pm.name AS promo_name,
            pm.recommendation_type_id,
            tasm.name AS recommendation_type,
            pm.last_approved_scenario_id,
            tpof.is_default,
            ---
            fmc.scenario_type,
            fmc.scenario_id,
            fmc.scenario_order_id,
            fmc.scenario_name,
            ---
            ROUND(SUM(fmc.sales_units)::decimal, 2) AS sales_units,
            ROUND(SUM(fmc.baseline_sales_units)::decimal, 2) AS baseline_sales_units,
            ROUND(SUM(fmc.incremental_sales_units)::decimal, 2) AS incremental_sales_units,
            --
            ROUND(SUM(fmc.revenue)::decimal, 2) AS revenue,
            ROUND(SUM(fmc.baseline_revenue)::decimal, 2) AS baseline_revenue,
            ROUND(SUM(fmc.incremental_revenue)::decimal, 2) AS incremental_revenue,
            ROUND(SUM(fmc.affinity_revenue)::decimal, 2) AS affinity_revenue,
            ROUND(SUM(fmc.cannibalization_revenue)::decimal, 2) AS cannibalization_revenue,
            ROUND(SUM(fmc.pull_forward_revenue)::decimal, 2) AS pull_forward_revenue,
            --
            ROUND(SUM(fmc.margin)::decimal, 2) AS margin,
            ROUND(SUM(fmc.baseline_margin)::decimal, 2) AS baseline_margin,
            ROUND(SUM(fmc.incremental_margin)::decimal, 2) AS incremental_margin,
            ROUND(SUM(fmc.affinity_margin)::decimal, 2) AS affinity_margin,
            ROUND(SUM(fmc.cannibalization_margin)::decimal, 2) AS cannibalization_margin,
            ROUND(SUM(fmc.pull_forward_margin)::decimal, 2) AS pull_forward_margin,
            --
            ROUND(SUM(fmc.promo_spend)::decimal, 2) AS promo_spend,
            --
            COALESCE(pm.total_inventory, 0) AS total_inventory,
            LEAST(100, GREATEST(0, CASE 
                WHEN COALESCE(pm.total_inventory, 0) = 0 THEN 0 
                ELSE ROUND(((SUM(fmc.sales_units) / pm.total_inventory) * 100)::DECIMAL, 1) 
            END)) AS finalized_st_percent
        FROM 
            price_promo.promo_master pm
        LEFT JOIN 
            final_metrics_cte fmc ON pm.promo_id = fmc.promo_id
        LEFT JOIN 
            metaschema.tb_app_sub_master tasm ON pm.recommendation_type_id = tasm.id
        LEFT JOIN
            override_forecast_metrics_cte tpof ON pm.promo_id = tpof.promo_id AND fmc.scenario_id = tpof.scenario_id
        WHERE 
            pm.promo_id = {promo_id}
        GROUP BY
            pm.promo_id,
            pm.name,
            pm.recommendation_type_id,
            tasm.name,
            pm.last_approved_scenario_id,
            fmc.scenario_type,
            fmc.scenario_id,
            fmc.scenario_order_id,
            fmc.scenario_name,
            tpof.is_default
    )
"""

OVERALL_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY = """
    do 
    $$
    declare 
        sce_ids int[];
        query text;
    begin
        select array_agg(distinct scenario_id) into sce_ids from price_promo.scenario_master where promo_id = {promo_id} 	;
        
        raise notice 'scenario_ids -  %', array_to_string(sce_ids, ',');

        drop table if exists tb_temp_dsr_data_{promo_id};
        
        if array_length(sce_ids::integer[], 1) > 0 then
            query = format(
		'
		create unlogged table tb_temp_dsr_data_{promo_id} as 
                with target_currency_cte AS (
                    SELECT 
                        fn_get_target_currency_id as target_currency_id  
                    from 
                        price_promo.fn_get_target_currency_id(
                            (
                                SELECT array_agg(DISTINCT currency_id) as source_currency_id
                                FROM price_promo.promo_master
                                WHERE promo_id = {promo_id}
                            ),
                            {target_currency_id}::integer
                    )
                ),
                resimulation_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    ''resimulation'' AS scenario_type,
                    prsa.scenario_id,
                    sm.scenario_order_id
                    {column_selections}
                    ,sm.scenario_name,
                    --
                    ROUND(SUM(prsa.sales_units)::decimal, 1) AS sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS incremental_sales_units,
                    --
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS pull_forward_revenue,
                    --
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS pull_forward_margin,
                    --
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS promo_spend
                FROM 
                    price_promo.scenario_master sm
                JOIN 
                    price_promo.ps_recommended_scenarios prsa 
                    ON prsa.scenario_id = sm.scenario_id
                    {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE 
                    prsa.scenario_id IN (%1$s)
                GROUP BY 
                    prsa.promo_id, 
                    prsa.scenario_id, 
                    sm.scenario_order_id
                    {group_by_str}
                    ,sm.scenario_name 
            )
            ,
            optimize_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    ''optimize'' AS scenario_type,
                    0 AS scenario_id,
                    0 AS scenario_order_id
                    {column_selections}
                    ,''IA Recommended'' AS scenario_name,
                    ROUND(SUM(prsa.sales_units)::decimal, 1) AS sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS promo_spend
                FROM price_promo.ps_recommended_ia_projected prsa
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.promo_id = {promo_id}
                GROUP BY 
                    prsa.promo_id
                    {group_by_str}
            ),
            override_simulation_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    prsa.scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS override_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS override_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS override_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS override_promo_spend
                FROM price_promo.scenario_master sm
                JOIN price_promo.ps_recommended_override prsa ON prsa.scenario_id = sm.scenario_id
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.scenario_id IN (%1$s)
                GROUP BY 
                    prsa.promo_id,
                    prsa.scenario_id
                    {group_by_str}
            ),
            override_optimize_metrics AS (
                SELECT
                    prsa.promo_id,
                    0 AS scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS override_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS override_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS override_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS override_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS override_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS override_promo_spend
                FROM price_promo.ps_recommended_override_ia prsa
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.promo_id = {promo_id}
                GROUP BY 
                    prsa.promo_id
                    {group_by_str}
            ),
            stacked_resimulation_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    prsa.scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS stacked_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS stacked_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS stacked_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_promo_spend
                FROM price_promo.scenario_master sm
                JOIN price_promo.ps_recommended_scenarios_stack prsa ON prsa.scenario_id = sm.scenario_id
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.scenario_id IN (%1$s)
                GROUP BY 
                    prsa.promo_id,
                    prsa.scenario_id
                    {group_by_str}
            ),
            stacked_optimize_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    0 AS scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS stacked_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS stacked_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS stacked_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_promo_spend
                FROM price_promo.ps_recommended_stack_ia prsa
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.promo_id = {promo_id}
                GROUP BY 
                    prsa.promo_id
                    {group_by_str}
            ),
            stacked_override_simulation_metrics_cte AS (
                SELECT
                    prsa.promo_id,
                    prsa.scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS stacked_override_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS stacked_override_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS stacked_override_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_promo_spend
                FROM price_promo.scenario_master sm
                JOIN price_promo.ps_recommended_scenarios_stack_override prsa ON prsa.scenario_id = sm.scenario_id
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.scenario_id IN (%1$s)
                GROUP BY 
                    prsa.promo_id,
                    prsa.scenario_id
                    {group_by_str}
            ),
            stacked_override_optimize_metrics AS (
                SELECT
                    prsa.promo_id,
                    0 AS scenario_id
                    {column_selections}
                    ,ROUND(SUM(prsa.sales_units)::decimal, 1) AS stacked_override_sales_units,
                    ROUND(SUM(prsa.baseline_sales_units)::decimal, 1) AS stacked_override_baseline_sales_units,
                    ROUND(SUM(prsa.incremental_sales_units)::decimal, 1) AS stacked_override_incremental_sales_units,
                    ROUND(SUM(prsa.revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_revenue,
                    ROUND(SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_baseline_revenue,
                    ROUND(SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_incremental_revenue,
                    ROUND(SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_affinity_revenue,
                    ROUND(SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_cannibalization_revenue,
                    ROUND(SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_pull_forward_revenue,
                    ROUND(SUM(prsa.margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_margin,
                    ROUND(SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_baseline_margin,
                    ROUND(SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_incremental_margin,
                    ROUND(SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_affinity_margin,
                    ROUND(SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_cannibalization_margin,
                    ROUND(SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_pull_forward_margin,
                    ROUND(SUM(prsa.promo_spend * pfr.planned_conversion_multiplier)::decimal, 0) AS stacked_override_promo_spend
                FROM price_promo.ps_recommended_stack_override_ia prsa
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
                WHERE prsa.promo_id = {promo_id}
                GROUP BY 
                    prsa.promo_id
                    {group_by_str}
            ),
            final_original_metrics_cte AS (
                SELECT * FROM resimulation_metrics_cte
                UNION ALL
                SELECT * FROM optimize_metrics_cte
            ),
            final_override_metrics_cte AS (
                SELECT * FROM override_simulation_metrics_cte
                UNION ALL
                SELECT * FROM override_optimize_metrics
            ),
            final_stacked_metrics as (
                SELECT * FROM stacked_resimulation_metrics_cte
                UNION ALL
                SELECT * FROM stacked_optimize_metrics_cte
            ),
            final_stacked_override_metrics as (
                SELECT * FROM stacked_override_simulation_metrics_cte
                UNION ALL
                SELECT * FROM stacked_override_optimize_metrics       
            ),
            final_metrics_cte AS (
                SELECT 
                    fom.promo_id,
                    ---
                    fom.scenario_type,
                    fom.scenario_id,
                    fom.scenario_order_id,
                    fom.scenario_name
                    {agg_selections}
                    
                    ,fom.sales_units,
					fom.baseline_sales_units,
					fom.incremental_sales_units,
					--
					fom.revenue,
					fom.baseline_revenue,
					fom.incremental_revenue,
					fom.affinity_revenue,
					fom.cannibalization_revenue,
					fom.pull_forward_revenue,
					--
					fom.margin,
					fom.baseline_margin,
					fom.incremental_margin,
					fom.affinity_margin,
					fom.cannibalization_margin,
					fom.pull_forward_margin,
					--
					fom.promo_spend,
					--
					fomc.override_sales_units,
					fomc.override_baseline_sales_units,
					fomc.override_incremental_sales_units,
					--
					fomc.override_revenue ,
					fomc.override_baseline_revenue ,
					fomc.override_incremental_revenue, 
					fomc.override_affinity_revenue ,
					fomc.override_cannibalization_revenue, 
					fomc.override_pull_forward_revenue, 
					--
					fomc.override_margin ,
					fomc.override_baseline_margin ,
					fomc.override_incremental_margin, 
					fomc.override_affinity_margin ,
					fomc.override_cannibalization_margin, 
					fomc.override_pull_forward_margin, 
					--
					fomc.override_promo_spend ,
					--STACKED
					fsm.stacked_sales_units ,
					fsm.stacked_baseline_sales_units ,
					fsm.stacked_incremental_sales_units, 
					--
					fsm.stacked_revenue ,
					fsm.stacked_baseline_revenue ,
					fsm.stacked_incremental_revenue, 
					fsm.stacked_affinity_revenue ,
					fsm.stacked_cannibalization_revenue, 
					fsm.stacked_pull_forward_revenue ,
					--
					fsm.stacked_margin,
					fsm.stacked_baseline_margin ,
					fsm.stacked_incremental_margin, 
					fsm.stacked_affinity_margin ,
					fsm.stacked_cannibalization_margin, 
					fsm.stacked_pull_forward_margin, 
					--
					fsm.stacked_promo_spend, 
					--
					fsom.stacked_override_sales_units ,
					fsom.stacked_override_baseline_sales_units ,
					fsom.stacked_override_incremental_sales_units, 
					--
					fsom.stacked_override_revenue ,
					fsom.stacked_override_baseline_revenue ,
					fsom.stacked_override_incremental_revenue, 
					fsom.stacked_override_affinity_revenue ,
					fsom.stacked_override_cannibalization_revenue, 
					fsom.stacked_override_pull_forward_revenue, 
					--
					fsom.stacked_override_margin ,
					fsom.stacked_override_baseline_margin ,
					fsom.stacked_override_incremental_margin, 
					fsom.stacked_override_affinity_margin ,
					fsom.stacked_override_cannibalization_margin, 
					fsom.stacked_override_pull_forward_margin

                FROM final_original_metrics_cte fom
                LEFT JOIN final_override_metrics_cte fomc 
                    USING(promo_id, scenario_id)
                LEFT JOIN final_stacked_metrics fsm
                    USING(promo_id, scenario_id)
                LEFT JOIN final_stacked_override_metrics fsom
                    USING(promo_id, scenario_id)
            ),
            override_forecast_metrics_cte AS (
                SELECT
                    tpof.promo_id,
                    tpof.scenario_id,
                    tpof.is_default
                FROM
                    price_promo.tb_promo_override_forecast tpof
                WHERE
                    tpof.promo_id = {promo_id}
            ),
            agg_metrics_cte AS (
                SELECT
                    pm.promo_id,
                    pm.name AS promo_name,
                    pm.recommendation_type_id,
                    tasm.name AS recommendation_type,
                    pm.last_approved_scenario_id,
                    tpof.is_default,
                    ---
                    fmc.scenario_type,
                    fmc.scenario_id,
                    fmc.scenario_order_id,
                    fmc.scenario_name
                    {selections_from_cte}
                    ---
                    ,ROUND(SUM(fmc.sales_units)::decimal, 1) AS sales_units,
                    ROUND(SUM(fmc.baseline_sales_units)::decimal, 1) AS baseline_sales_units,
                    ROUND(SUM(fmc.incremental_sales_units)::decimal, 1) AS incremental_sales_units,
                    --
                    ROUND(SUM(fmc.revenue)::decimal, 0) AS revenue,
                    ROUND(SUM(fmc.baseline_revenue)::decimal, 0) AS baseline_revenue,
                    ROUND(SUM(fmc.incremental_revenue)::decimal, 0) AS incremental_revenue,
                    ROUND(SUM(fmc.affinity_revenue)::decimal, 0) AS affinity_revenue,
                    ROUND(SUM(fmc.cannibalization_revenue)::decimal, 0) AS cannibalization_revenue,
                    ROUND(SUM(fmc.pull_forward_revenue)::decimal, 0) AS pull_forward_revenue,
                    --
                    ROUND(SUM(fmc.margin)::decimal, 0) AS margin,
                    ROUND(SUM(fmc.baseline_margin)::decimal, 0) AS baseline_margin,
                    ROUND(SUM(fmc.incremental_margin)::decimal, 0) AS incremental_margin,
                    ROUND(SUM(fmc.affinity_margin)::decimal, 0) AS affinity_margin,
                    ROUND(SUM(fmc.cannibalization_margin)::decimal, 0) AS cannibalization_margin,
                    ROUND(SUM(fmc.pull_forward_margin)::decimal, 0) AS pull_forward_margin,
                    --
                    ROUND(SUM(fmc.promo_spend)::decimal, 0) AS promo_spend,
                    --
                    ROUND(SUM(fmc.override_sales_units)::decimal, 1) AS override_sales_units,
                    ROUND(SUM(fmc.override_baseline_sales_units)::decimal, 1) AS override_baseline_sales_units,
                    ROUND(SUM(fmc.override_incremental_sales_units)::decimal, 1) AS override_incremental_sales_units,
                    --
                    ROUND(SUM(fmc.override_revenue)::decimal, 0) AS override_revenue,
                    ROUND(SUM(fmc.override_baseline_revenue)::decimal, 0) AS override_baseline_revenue,
                    ROUND(SUM(fmc.override_incremental_revenue)::decimal, 0) AS override_incremental_revenue,
                    ROUND(SUM(fmc.override_affinity_revenue)::decimal, 0) AS override_affinity_revenue,
                    ROUND(SUM(fmc.override_cannibalization_revenue)::decimal, 0) AS override_cannibalization_revenue,
                    ROUND(SUM(fmc.override_pull_forward_revenue)::decimal, 0) AS override_pull_forward_revenue,
                    --
                    ROUND(SUM(fmc.override_margin)::decimal, 0) AS override_margin,
                    ROUND(SUM(fmc.override_baseline_margin)::decimal, 0) AS override_baseline_margin,
                    ROUND(SUM(fmc.override_incremental_margin)::decimal, 0) AS override_incremental_margin,
                    ROUND(SUM(fmc.override_affinity_margin)::decimal, 0) AS override_affinity_margin,
                    ROUND(SUM(fmc.override_cannibalization_margin)::decimal, 0) AS override_cannibalization_margin,
                    ROUND(SUM(fmc.override_pull_forward_margin)::decimal, 0) AS override_pull_forward_margin,
                    --
                    ROUND(SUM(fmc.override_promo_spend)::decimal, 0) AS override_promo_spend,
                    --STACKED
                    ROUND(SUM(fmc.stacked_sales_units)::decimal, 1) AS stacked_sales_units,
                    ROUND(SUM(fmc.stacked_baseline_sales_units)::decimal, 1) AS stacked_baseline_sales_units,
                    ROUND(SUM(fmc.stacked_incremental_sales_units)::decimal, 1) AS stacked_incremental_sales_units,
                    --
                    ROUND(SUM(fmc.stacked_revenue)::decimal, 0) AS stacked_revenue,
                    ROUND(SUM(fmc.stacked_baseline_revenue)::decimal, 0) AS stacked_baseline_revenue,
                    ROUND(SUM(fmc.stacked_incremental_revenue)::decimal, 0) AS stacked_incremental_revenue,
                    ROUND(SUM(fmc.stacked_affinity_revenue)::decimal, 0) AS stacked_affinity_revenue,
                    ROUND(SUM(fmc.stacked_cannibalization_revenue)::decimal, 0) AS stacked_cannibalization_revenue,
                    ROUND(SUM(fmc.stacked_pull_forward_revenue)::decimal, 0) AS stacked_pull_forward_revenue,
                    --
                    ROUND(SUM(fmc.stacked_margin)::decimal, 0) AS stacked_margin,
                    ROUND(SUM(fmc.stacked_baseline_margin)::decimal, 0) AS stacked_baseline_margin,
                    ROUND(SUM(fmc.stacked_incremental_margin)::decimal, 0) AS stacked_incremental_margin,
                    ROUND(SUM(fmc.stacked_affinity_margin)::decimal, 0) AS stacked_affinity_margin,
                    ROUND(SUM(fmc.stacked_cannibalization_margin)::decimal, 0) AS stacked_cannibalization_margin,
                    ROUND(SUM(fmc.stacked_pull_forward_margin)::decimal, 0) AS stacked_pull_forward_margin,
                    --
                    ROUND(SUM(fmc.stacked_promo_spend)::decimal, 0) AS stacked_promo_spend,
                    --
                    ROUND(SUM(fmc.stacked_override_sales_units)::decimal, 1) AS stacked_override_sales_units,
                    ROUND(SUM(fmc.stacked_override_baseline_sales_units)::decimal, 1) AS stacked_override_baseline_sales_units,
                    ROUND(SUM(fmc.stacked_override_incremental_sales_units)::decimal, 1) AS stacked_override_incremental_sales_units,
                    --
                    ROUND(SUM(fmc.stacked_override_revenue)::decimal, 0) AS stacked_override_revenue,
                    ROUND(SUM(fmc.stacked_override_baseline_revenue)::decimal, 0) AS stacked_override_baseline_revenue,
                    ROUND(SUM(fmc.stacked_override_incremental_revenue)::decimal, 0) AS stacked_override_incremental_revenue,
                    ROUND(SUM(fmc.stacked_override_affinity_revenue)::decimal, 0) AS stacked_override_affinity_revenue,
                    ROUND(SUM(fmc.stacked_override_cannibalization_revenue)::decimal, 0) AS stacked_override_cannibalization_revenue,
                    ROUND(SUM(fmc.stacked_override_pull_forward_revenue)::decimal, 0) AS stacked_override_pull_forward_revenue,
                    --
                    ROUND(SUM(fmc.stacked_override_margin)::decimal, 0) AS stacked_override_margin,
                    ROUND(SUM(fmc.stacked_override_baseline_margin)::decimal, 0) AS stacked_override_baseline_margin,
                    ROUND(SUM(fmc.stacked_override_incremental_margin)::decimal, 0) AS stacked_override_incremental_margin,
                    ROUND(SUM(fmc.stacked_override_affinity_margin)::decimal, 0) AS stacked_override_affinity_margin,
                    ROUND(SUM(fmc.stacked_override_cannibalization_margin)::decimal, 0) AS stacked_override_cannibalization_margin,
                    ROUND(SUM(fmc.stacked_override_pull_forward_margin)::decimal, 0) AS stacked_override_pull_forward_margin
                FROM 
                    price_promo.promo_master pm
                left join 
                        price_promo.event_master em on em.event_id = pm.event_id
                LEFT JOIN 
                    final_metrics_cte fmc ON pm.promo_id = fmc.promo_id
                LEFT JOIN 
                    metaschema.tb_app_sub_master tasm ON pm.recommendation_type_id = tasm.id
                LEFT JOIN
                    override_forecast_metrics_cte tpof ON pm.promo_id = tpof.promo_id AND fmc.scenario_id = tpof.scenario_id
                WHERE 
                    pm.promo_id = {promo_id}
                GROUP BY
                    pm.promo_id,
                    pm.name,
                    pm.recommendation_type_id,
                    tasm.name,
                    pm.last_approved_scenario_id,
                    fmc.scenario_type,
                    fmc.scenario_id,
                    fmc.scenario_order_id,
                    fmc.scenario_name
                    {group_by_from_ctes}
                    ,tpof.is_default
            )
            SELECT
                promo_id,
                promo_name,
                tcm.currency_id,
                tcm.currency_name,
                tcm.currency_symbol,
                scenario_type,
                scenario_id,
                scenario_order_id,
                scenario_name
                {column_selections}
                --
                ,sales_units,
                baseline_sales_units,
                incremental_sales_units,
                --
                revenue,
                baseline_revenue,
                incremental_revenue,
                --
                margin,
                baseline_margin,
                incremental_margin,
                --
                promo_spend,
                CASE 
                    WHEN sales_units = 0 THEN NULL 
                    ELSE ROUND((revenue / sales_units)::decimal, 0)
                END AS aur,
                CASE 
                    WHEN revenue = 0 THEN 0 
                    ELSE ROUND(((margin * 100) / revenue)::decimal, 1) 
                END AS gm_percent,
                --
                override_sales_units,
                override_baseline_sales_units,
                override_incremental_sales_units,
                --
                override_revenue,
                override_baseline_revenue,
                override_incremental_revenue,
                --
                override_margin,
                override_baseline_margin,
                override_incremental_margin,
                --
                override_promo_spend,
                CASE 
                    WHEN override_sales_units = 0 THEN NULL 
                    ELSE ROUND((override_revenue / override_sales_units)::decimal, 0)
                END AS override_aur,
                CASE 
                    WHEN override_revenue = 0 THEN 0 
                    ELSE ROUND(((override_margin * 100) / override_revenue)::decimal, 1) 
                END AS override_gm_percent,
                --
                stacked_sales_units,
                stacked_baseline_sales_units,
                stacked_incremental_sales_units,
                stacked_revenue,
                stacked_baseline_revenue,
                stacked_incremental_revenue,
                stacked_margin,
                stacked_baseline_margin,
                stacked_incremental_margin,
                stacked_promo_spend,
                --
                stacked_override_sales_units,
                stacked_override_baseline_sales_units,
                stacked_override_incremental_sales_units,
                stacked_override_revenue,
                stacked_override_baseline_revenue,
                stacked_override_incremental_revenue,
                stacked_override_margin,
                stacked_override_baseline_margin,
                stacked_override_incremental_margin
            FROM
                agg_metrics_cte
            inner join
                global.tb_currency_master tcm
                on
                    tcm.currency_id = (select target_currency_id from target_currency_cte);
		  ', array_to_string(sce_ids, ',')
        );	

        end if;
        execute query;
        raise notice 'query   -   %', query;
        end
    $$;          
    select * from tb_temp_dsr_data_{promo_id};  

"""

AGGR_SIM_RESULTS_DETAILS_DOWNLOADS_QUERY = """
do 
    $$
    declare 
        sce_ids int[];
        query text;
    begin
        select array_agg(distinct scenario_id) into sce_ids from price_promo.scenario_master where promo_id = {promo_id} 	;
        
        raise notice 'scenario_ids -  %', array_to_string(sce_ids, ',');

        drop table if exists tb_temp_dsr_data_{promo_id};
        
        if array_length(sce_ids::integer[], 1) > 0 then
            query = format(
		'
		create unlogged table tb_temp_dsr_data_{promo_id} as 
            with target_currency_cte AS (
                    SELECT 
                        fn_get_target_currency_id as target_currency_id  
                    from 
                        price_promo.fn_get_target_currency_id(
                            (
                                SELECT array_agg(DISTINCT currency_id) as source_currency_id
                                FROM price_promo.promo_master 
                                WHERE promo_id = {promo_id}
                            ),
                            {target_currency_id}::integer
                    )
                ),
			temp_scenario_results AS (
			SELECT
			    prsa.promo_id,
			    prsa.scenario_id,
			    sm.scenario_order_id
                {column_selections}
			    ,sm.scenario_name,
			    tpof.is_default,
			    sum(prsa.sales_units) as sales_units,
			    sum(prsa.baseline_sales_units) as baseline_sales_units,
			    sum(prsa.incremental_sales_units) as incremental_sales_units,
			    sum(prsa.revenue * pfr.planned_conversion_multiplier) as revenue,
			    sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as baseline_revenue,
			    sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as incremental_revenue,
			    sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as affinity_revenue,
			    sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as cannibalization_revenue,
			    sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as pull_forward_revenue,
			    sum(prsa.margin * pfr.planned_conversion_multiplier) as margin,
			    sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as baseline_margin,
			    sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as incremental_margin,
			    sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as affinity_margin,
			    sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as cannibalization_margin,
			    sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as pull_forward_margin,
			    sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as promo_spend
			FROM
			    price_promo.ps_recommended_scenarios prsa
			    LEFT JOIN price_promo.scenario_master sm
			    ON prsa.promo_id = sm.promo_id
			    AND prsa.scenario_id = sm.scenario_id
			    LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND prsa.scenario_id = tpof.scenario_id
				{join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE 
			      prsa.scenario_id IN (%1$s)
			GROUP BY
			    prsa.promo_id,
			    prsa.scenario_id,
			    sm.scenario_order_id,
			    sm.scenario_name
                {group_by_str}
			    ,tpof.is_default
			),
			temp_ia_reccommend_results AS (
			SELECT
			    prsa.promo_id,
			    tpof.is_default
                {column_selections}
			    ,SUM(prsa.sales_units) AS sales_units,
			    SUM(prsa.baseline_sales_units) AS baseline_sales_units,
			    SUM(prsa.incremental_sales_units) AS incremental_sales_units,
			    SUM(prsa.revenue * pfr.planned_conversion_multiplier) AS revenue,
			    SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier) AS baseline_revenue,
			    SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier) AS incremental_revenue,
			    SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier) AS affinity_revenue,
			    SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) AS cannibalization_revenue,
			    SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) AS pull_forward_revenue,
			    SUM(prsa.margin * pfr.planned_conversion_multiplier) AS margin,
			    SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier) AS baseline_margin,
			    SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier) AS incremental_margin,
			    SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier) AS affinity_margin,
			    SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) AS cannibalization_margin,
			    SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) AS pull_forward_margin,
			    SUM(prsa.promo_spend * pfr.planned_conversion_multiplier) AS promo_spend
			FROM
			    price_promo.ps_recommended_ia_projected prsa
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND tpof.scenario_id = 0
            {join_str}
            inner join 
                global.planned_forex_rate pfr 
                on 
                    prsa.recommendation_date = pfr.date 
                    and pfr.source_currency_id = prsa.currency_id
                    and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.promo_id = {promo_id}
			GROUP BY
			    prsa.promo_id
                {group_by_str}
			    ,tpof.is_default
			),
			temp_override_scenario_results AS (
			SELECT
			    prsa.promo_id,
			    prsa.scenario_id
                {column_selections}
			    ,sum(prsa.sales_units) as override_sales_units,
			    sum(prsa.baseline_sales_units) as override_baseline_sales_units,
			    sum(prsa.incremental_sales_units) as override_incremental_sales_units,
			    sum(prsa.revenue * pfr.planned_conversion_multiplier) as override_revenue,
			    sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as override_baseline_revenue,
			    sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as override_incremental_revenue,
			    sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as override_affinity_revenue,
			    sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as override_cannibalization_revenue,
			    sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as override_pull_forward_revenue,
			    sum(prsa.margin * pfr.planned_conversion_multiplier) as override_margin,
			    sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as override_baseline_margin,
			    sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as override_incremental_margin,
			    sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as override_affinity_margin,
			    sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as override_cannibalization_margin,
			    sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as override_pull_forward_margin,
			    sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as override_promo_spend
			FROM
			    price_promo.ps_recommended_override prsa
			LEFT JOIN price_promo.scenario_master sm
			    ON prsa.promo_id = sm.promo_id
			    AND prsa.scenario_id = sm.scenario_id
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND prsa.scenario_id = tpof.scenario_id
            {join_str}
            inner join 
                global.planned_forex_rate pfr 
                on 
                    prsa.recommendation_date = pfr.date 
                    and pfr.source_currency_id = prsa.currency_id
                    and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.scenario_id in (%1$s)
			GROUP BY
			    prsa.promo_id,
			    prsa.scenario_id,
			    sm.scenario_order_id,
			    sm.scenario_name
                {group_by_str}
			    ,tpof.is_default
			),
			temp_override_ia_reccommend_results AS (
			SELECT
			    prsa.promo_id
                {column_selections}
			    ,SUM(prsa.sales_units) AS override_sales_units,
			    SUM(prsa.baseline_sales_units) AS override_baseline_sales_units,
			    SUM(prsa.incremental_sales_units) AS override_incremental_sales_units,
			    SUM(prsa.revenue * pfr.planned_conversion_multiplier) AS override_revenue,
			    SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier) AS override_baseline_revenue,
			    SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier) AS override_incremental_revenue,
			    SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier) AS override_affinity_revenue,
			    SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) AS override_cannibalization_revenue,
			    SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) AS override_pull_forward_revenue,
			    SUM(prsa.margin * pfr.planned_conversion_multiplier) AS override_margin,
			    SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier) AS override_baseline_margin,
			    SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier) AS override_incremental_margin,
			    SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier) AS override_affinity_margin,
			    SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) AS override_cannibalization_margin,
			    SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) AS override_pull_forward_margin,
			    SUM(prsa.promo_spend * pfr.planned_conversion_multiplier) AS override_promo_spend
			FROM
			    price_promo.ps_recommended_override_ia prsa
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND tpof.scenario_id = 0
            {join_str}
            inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.promo_id = {promo_id}
			GROUP BY
			    prsa.promo_id
                {group_by_str}
			    ,tpof.is_default
			),
			temp_stacked_scenario_results AS (
			SELECT
			    prsa.promo_id,
			    prsa.scenario_id
                {column_selections}
			    ,tpof.is_default,
			    sum(prsa.sales_units) as stacked_sales_units,
			    sum(prsa.baseline_sales_units) as stacked_baseline_sales_units,
			    sum(prsa.incremental_sales_units) as stacked_incremental_sales_units,
			    sum(prsa.revenue * pfr.planned_conversion_multiplier) as stacked_revenue,
			    sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as stacked_baseline_revenue,
			    sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as stacked_incremental_revenue,
			    sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as stacked_affinity_revenue,
			    sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as stacked_cannibalization_revenue,
			    sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as stacked_pull_forward_revenue,
			    sum(prsa.margin * pfr.planned_conversion_multiplier) as stacked_margin,
			    sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as stacked_baseline_margin,
			    sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as stacked_incremental_margin,
			    sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as stacked_affinity_margin,
			    sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as stacked_cannibalization_margin,
			    sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as stacked_pull_forward_margin,
			    sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as stacked_promo_spend
			FROM
			    price_promo.ps_recommended_scenarios_stack prsa
			    LEFT JOIN price_promo.scenario_master sm
			    ON prsa.promo_id = sm.promo_id
			    AND prsa.scenario_id = sm.scenario_id
			    LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND prsa.scenario_id = tpof.scenario_id
                {join_str}
                inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.scenario_id in (%1$s)
			GROUP BY
			    prsa.promo_id,
			    prsa.scenario_id
                {group_by_str}
			    ,tpof.is_default
			),
			temp_stacked_ia_reccommend_results AS (
			SELECT
			    prsa.promo_id,
			    tpof.is_default
                {column_selections}
			    ,SUM(prsa.sales_units) AS stacked_sales_units,
			    SUM(prsa.baseline_sales_units) AS stacked_baseline_sales_units,
			    SUM(prsa.incremental_sales_units) AS stacked_incremental_sales_units,
			    SUM(prsa.revenue * pfr.planned_conversion_multiplier) AS stacked_revenue,
			    SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier) AS stacked_baseline_revenue,
			    SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier) AS stacked_incremental_revenue,
			    SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier) AS stacked_affinity_revenue,
			    SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) AS stacked_cannibalization_revenue,
			    SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) AS stacked_pull_forward_revenue,
			    SUM(prsa.margin * pfr.planned_conversion_multiplier) AS stacked_margin,
			    SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier) AS stacked_baseline_margin,
			    SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier) AS stacked_incremental_margin,
			    SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier) AS stacked_affinity_margin,
			    SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) AS stacked_cannibalization_margin,
			    SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) AS stacked_pull_forward_margin,
			    SUM(prsa.promo_spend * pfr.planned_conversion_multiplier) AS stacked_promo_spend
			FROM
			    price_promo.ps_recommended_stack_ia prsa
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND tpof.scenario_id = 0
            {join_str}
            inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.promo_id = {promo_id}
			GROUP BY
			    prsa.promo_id
                {group_by_str}
			    ,tpof.is_default
			),
			temp_stacked_override_scenario_results AS (
			SELECT
			    prsa.promo_id,
			    prsa.scenario_id
                {column_selections}
			    ,sum(prsa.sales_units) as stacked_override_sales_units,
			    sum(prsa.baseline_sales_units) as stacked_override_baseline_sales_units,
			    sum(prsa.incremental_sales_units) as stacked_override_incremental_sales_units,
			    sum(prsa.revenue * pfr.planned_conversion_multiplier) as stacked_override_revenue,
			    sum(prsa.baseline_revenue * pfr.planned_conversion_multiplier) as stacked_override_baseline_revenue,
			    sum(prsa.incremental_revenue * pfr.planned_conversion_multiplier) as stacked_override_incremental_revenue,
			    sum(prsa.affinity_revenue * pfr.planned_conversion_multiplier) as stacked_override_affinity_revenue,
			    sum(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) as stacked_override_cannibalization_revenue,
			    sum(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) as stacked_override_pull_forward_revenue,
			    sum(prsa.margin * pfr.planned_conversion_multiplier) as stacked_override_margin,
			    sum(prsa.baseline_margin * pfr.planned_conversion_multiplier) as stacked_override_baseline_margin,
			    sum(prsa.incremental_margin * pfr.planned_conversion_multiplier) as stacked_override_incremental_margin,
			    sum(prsa.affinity_margin * pfr.planned_conversion_multiplier) as stacked_override_affinity_margin,
			    sum(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) as stacked_override_cannibalization_margin,
			    sum(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) as stacked_override_pull_forward_margin,
			    sum(prsa.promo_spend * pfr.planned_conversion_multiplier) as stacked_override_promo_spend
			FROM
			    price_promo.ps_recommended_scenarios_stack_override prsa
			LEFT JOIN price_promo.scenario_master sm
			    ON prsa.promo_id = sm.promo_id
			    AND prsa.scenario_id = sm.scenario_id
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND prsa.scenario_id = tpof.scenario_id
            {join_str}
            inner join 
                    global.planned_forex_rate pfr 
                    on 
                        prsa.recommendation_date = pfr.date 
                        and pfr.source_currency_id = prsa.currency_id
                        and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.scenario_id in (%1$s)
			GROUP BY
			    prsa.promo_id,
			    prsa.scenario_id,
			    sm.scenario_order_id,
			    sm.scenario_name
                {group_by_str}
			    ,tpof.is_default
			),
			temp_stacked_override_ia_reccommend_results AS (
			SELECT
			    prsa.promo_id
                {column_selections}
			    ,SUM(prsa.sales_units) AS stacked_override_sales_units,
			    SUM(prsa.baseline_sales_units) AS stacked_override_baseline_sales_units,
			    SUM(prsa.incremental_sales_units) AS stacked_override_incremental_sales_units,
			    SUM(prsa.revenue * pfr.planned_conversion_multiplier) AS stacked_override_revenue,
			    SUM(prsa.baseline_revenue * pfr.planned_conversion_multiplier) AS stacked_override_baseline_revenue,
			    SUM(prsa.incremental_revenue * pfr.planned_conversion_multiplier) AS stacked_override_incremental_revenue,
			    SUM(prsa.affinity_revenue * pfr.planned_conversion_multiplier) AS stacked_override_affinity_revenue,
			    SUM(prsa.cannibalization_revenue * pfr.planned_conversion_multiplier) AS stacked_override_cannibalization_revenue,
			    SUM(prsa.pull_forward_revenue * pfr.planned_conversion_multiplier) AS stacked_override_pull_forward_revenue,
			    SUM(prsa.margin * pfr.planned_conversion_multiplier) AS stacked_override_margin,
			    SUM(prsa.baseline_margin * pfr.planned_conversion_multiplier) AS stacked_override_baseline_margin,
			    SUM(prsa.incremental_margin * pfr.planned_conversion_multiplier) AS stacked_override_incremental_margin,
			    SUM(prsa.affinity_margin * pfr.planned_conversion_multiplier) AS stacked_override_affinity_margin,
			    SUM(prsa.cannibalization_margin * pfr.planned_conversion_multiplier) AS stacked_override_cannibalization_margin,
			    SUM(prsa.pull_forward_margin * pfr.planned_conversion_multiplier) AS stacked_override_pull_forward_margin,
			    SUM(prsa.promo_spend * pfr.planned_conversion_multiplier) AS stacked_override_promo_spend
			FROM
			    price_promo.ps_recommended_stack_override_ia prsa
			LEFT JOIN price_promo.tb_promo_override_forecast tpof
			    ON prsa.promo_id = tpof.promo_id
			    AND tpof.scenario_id = 0
            {join_str}
            inner join 
                global.planned_forex_rate pfr 
                on 
                    prsa.recommendation_date = pfr.date 
                    and pfr.source_currency_id = prsa.currency_id
                    and pfr.target_currency_id = (SELECT target_currency_id FROM target_currency_cte)
			WHERE
			    prsa.promo_id = {promo_id}
			GROUP BY
			    prsa.promo_id
                {group_by_str}
			    ,tpof.is_default
			),
			prod_master_cte AS (
			    SELECT
			        pm.promo_id,
			        pm.name AS promo_name,
			        pm.recommendation_type_id,
			        tasm.name AS recommendation_type,
			        pm.last_approved_scenario_id
			    FROM
			        price_promo.promo_master pm
			        LEFT JOIN metaschema.tb_app_sub_master tasm
			        ON pm.recommendation_type_id = tasm.id
			    WHERE
			        pm.promo_id = {promo_id}
			),
			final_original_metrics_cte AS (
			    SELECT
			        tsr.promo_id,
			        pdmc.promo_name,
			        pdmc.recommendation_type_id,
			        pdmc.recommendation_type,
			        pdmc.last_approved_scenario_id,
			        ''resimulation'' AS scenario_type,
			        tsr.scenario_id,
			        tsr.scenario_order_id,
			        tsr.scenario_name,
			        tsr.is_default
                    {column_selections_in_unions}
			        ,ROUND(SUM(tsr.sales_units)::decimal, 1) AS sales_units,
			        ROUND(SUM(tsr.baseline_sales_units)::decimal, 1) AS baseline_sales_units,
			        ROUND(SUM(tsr.incremental_sales_units)::decimal, 1) AS incremental_sales_units,
			        ROUND(SUM(tsr.revenue)::decimal, 0) AS revenue,
			        ROUND(SUM(tsr.baseline_revenue)::decimal, 0) AS baseline_revenue,
			        ROUND(SUM(tsr.incremental_revenue)::decimal, 0) AS incremental_revenue,
			        ROUND(SUM(tsr.affinity_revenue)::decimal, 0) AS affinity_revenue,
			        ROUND(SUM(tsr.cannibalization_revenue)::decimal, 0) AS cannibalization_revenue,
			        ROUND(SUM(tsr.pull_forward_revenue)::decimal, 0) AS pull_forward_revenue,
			        ROUND(SUM(tsr.margin)::decimal, 0) AS margin,
			        ROUND(SUM(tsr.baseline_margin)::decimal, 0) AS baseline_margin,
			        ROUND(SUM(tsr.incremental_margin)::decimal, 0) AS incremental_margin,
			        ROUND(SUM(tsr.affinity_margin)::decimal, 0) AS affinity_margin,
			        ROUND(SUM(tsr.cannibalization_margin)::decimal, 0) AS cannibalization_margin,
			        ROUND(SUM(tsr.pull_forward_margin)::decimal, 0) AS pull_forward_margin,
			        ROUND(SUM(tsr.promo_spend)::decimal, 0) AS promo_spend,
			        CASE
			            WHEN SUM(tsr.sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tsr.revenue)::decimal, 2) / NULLIF(ROUND(SUM(tsr.sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as aur,
			        CASE
			            WHEN SUM(tsr.sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tsr.margin)::decimal, 2) / NULLIF(ROUND(SUM(tsr.sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as aum,
			        CASE
			            WHEN SUM(tsr.revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tsr.margin)::decimal * 100) / NULLIF(SUM(tsr.revenue), 0)::decimal,
			                1
			            )
			        END as gm_percent
			    FROM
			        temp_scenario_results tsr
			        LEFT JOIN prod_master_cte pdmc
			        ON tsr.promo_id = pdmc.promo_id
			    GROUP BY
			        tsr.promo_id,
			        pdmc.promo_name,
			        pdmc.recommendation_type_id,
			        pdmc.recommendation_type,
			        pdmc.last_approved_scenario_id,
			        tsr.scenario_id,
			        tsr.scenario_order_id,
			        tsr.scenario_name
                    {group_by_in_unions}
			        ,tsr.is_default
			    UNION ALL
			    SELECT
			        tiar.promo_id,
			        pdmc.promo_name,
			        pdmc.recommendation_type_id,
			        pdmc.recommendation_type,
			        pdmc.last_approved_scenario_id,
			        ''optimize'' AS scenario_type,
			        0 AS scenario_id,
			        0 AS scenario_order_id,
			        ''IA Recommend'' AS scenario_name,
			        tiar.is_default
                    {column_selections_in_unions}
			        ,ROUND(SUM(tiar.sales_units)::decimal, 1) AS sales_units,
			        ROUND(SUM(tiar.baseline_sales_units)::decimal, 1) AS baseline_sales_units,
			        ROUND(SUM(tiar.incremental_sales_units)::decimal, 1) AS incremental_sales_units,
			        ROUND(SUM(tiar.revenue)::decimal, 0) AS revenue,
			        ROUND(SUM(tiar.baseline_revenue)::decimal, 0) AS baseline_revenue,
			        ROUND(SUM(tiar.incremental_revenue)::decimal, 0) AS incremental_revenue,
			        ROUND(SUM(tiar.affinity_revenue)::decimal, 0) AS affinity_revenue,
			        ROUND(SUM(tiar.cannibalization_revenue)::decimal, 0) AS cannibalization_revenue,
			        ROUND(SUM(tiar.pull_forward_revenue)::decimal, 0) AS pull_forward_revenue,
			        ROUND(SUM(tiar.margin)::decimal, 0) AS margin,
			        ROUND(SUM(tiar.baseline_margin)::decimal, 0) AS baseline_margin,
			        ROUND(SUM(tiar.incremental_margin)::decimal, 0) AS incremental_margin,
			        ROUND(SUM(tiar.affinity_margin)::decimal, 0) AS affinity_margin,
			        ROUND(SUM(tiar.cannibalization_margin)::decimal, 0) AS cannibalization_margin,
			        ROUND(SUM(tiar.pull_forward_margin)::decimal, 0) AS pull_forward_margin,
			        ROUND(SUM(tiar.promo_spend)::decimal, 0) AS promo_spend,
			        CASE
			            WHEN SUM(tiar.sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tiar.revenue)::decimal, 2) / NULLIF(ROUND(SUM(tiar.sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as aur,
			        CASE
			            WHEN SUM(tiar.sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tiar.margin)::decimal, 2) / NULLIF(ROUND(SUM(tiar.sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as aum,
			        CASE
			            WHEN SUM(tiar.revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tiar.margin)::decimal * 100) / NULLIF(SUM(tiar.revenue), 0)::decimal,
			                1
			            )
			        END as gm_percent
			    FROM
			        temp_ia_reccommend_results tiar
			        LEFT JOIN prod_master_cte pdmc
			        ON tiar.promo_id = pdmc.promo_id
			    GROUP BY
			        tiar.promo_id,
			        pdmc.promo_name,
			        pdmc.recommendation_type_id,
			        pdmc.recommendation_type,
			        pdmc.last_approved_scenario_id
                    {group_by_in_unions}
			        ,tiar.is_default
			),
			final_override_metrics_cte as (
			    SELECT
			        tosr.promo_id,
			        tosr.scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(tosr.override_sales_units)::decimal, 1) AS override_sales_units,
			        ROUND(SUM(tosr.override_baseline_sales_units)::decimal, 1) AS override_baseline_sales_units,
			        ROUND(SUM(tosr.override_incremental_sales_units)::decimal, 1) AS override_incremental_sales_units,
			        ROUND(SUM(tosr.override_revenue)::decimal, 0) AS override_revenue,
			        ROUND(SUM(tosr.override_baseline_revenue)::decimal, 0) AS override_baseline_revenue,
			        ROUND(SUM(tosr.override_incremental_revenue)::decimal, 0) AS override_incremental_revenue,
			        ROUND(SUM(tosr.override_affinity_revenue)::decimal, 0) AS override_affinity_revenue,
			        ROUND(SUM(tosr.override_cannibalization_revenue)::decimal, 0) AS override_cannibalization_revenue,
			        ROUND(SUM(tosr.override_pull_forward_revenue)::decimal, 0) AS override_pull_forward_revenue,
			        ROUND(SUM(tosr.override_margin)::decimal, 0) AS override_margin,
			        ROUND(SUM(tosr.override_baseline_margin)::decimal, 0) AS override_baseline_margin,
			        ROUND(SUM(tosr.override_incremental_margin)::decimal, 0) AS override_incremental_margin,
			        ROUND(SUM(tosr.override_affinity_margin)::decimal, 0) AS override_affinity_margin,
			        ROUND(SUM(tosr.override_cannibalization_margin)::decimal, 0) AS override_cannibalization_margin,
			        ROUND(SUM(tosr.override_pull_forward_margin)::decimal, 0) AS override_pull_forward_margin,
			        ROUND(SUM(tosr.override_promo_spend)::decimal, 0) AS override_promo_spend,
			        CASE
			            WHEN SUM(tosr.override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tosr.override_revenue)::decimal, 2) / NULLIF(ROUND(SUM(tosr.override_sales_units)::decimal, 2), 0)::decimal,0
			            )
			        END as override_aur,
			        CASE
			            WHEN SUM(tosr.override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tosr.override_margin)::decimal, 2) / NULLIF(ROUND(SUM(tosr.override_sales_units)::decimal,2),0)::decimal,0
			            )
			        END as override_aum,
			        CASE
			            WHEN SUM(tosr.override_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tosr.override_margin)::decimal * 100) / NULLIF(SUM(tosr.override_revenue), 0)::decimal,1
			            )
			        END as override_gm_percent
			    FROM
			        temp_override_scenario_results tosr
			        LEFT JOIN prod_master_cte pdmc
			        ON tosr.promo_id = pdmc.promo_id
			    GROUP BY
			        tosr.promo_id
                    {group_by_in_unions}
			        ,tosr.scenario_id
			    UNION ALL
			    SELECT
			        toiar.promo_id,
			        0 AS scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(toiar.override_sales_units)::decimal, 1) AS taoverride_sales_units,
			        ROUND(SUM(toiar.override_baseline_sales_units)::decimal, 1) AS taoverride_baseline_sales_units,
			        ROUND(SUM(toiar.override_incremental_sales_units)::decimal, 1) AS taoverride_incremental_sales_units,
			        ROUND(SUM(toiar.override_revenue)::decimal, 0) AS taoverride_revenue,
			        ROUND(SUM(toiar.override_baseline_revenue)::decimal, 0) AS taoverride_baseline_revenue,
			        ROUND(SUM(toiar.override_incremental_revenue)::decimal, 0) AS taoverride_incremental_revenue,
			        ROUND(SUM(toiar.override_affinity_revenue)::decimal, 0) AS taoverride_affinity_revenue,
			        ROUND(SUM(toiar.override_cannibalization_revenue)::decimal, 0) AS taoverride_cannibalization_revenue,
			        ROUND(SUM(toiar.override_pull_forward_revenue)::decimal, 0) AS taoverride_pull_forward_revenue,
			        ROUND(SUM(toiar.override_margin)::decimal, 0) AS taoverride_margin,
			        ROUND(SUM(toiar.override_baseline_margin)::decimal, 0) AS taoverride_baseline_margin,
			        ROUND(SUM(toiar.override_incremental_margin)::decimal, 0) AS taoverride_incremental_margin,
			        ROUND(SUM(toiar.override_affinity_margin)::decimal, 0) AS taoverride_affinity_margin,
			        ROUND(SUM(toiar.override_cannibalization_margin)::decimal, 0) AS taoverride_cannibalization_margin,
			        ROUND(SUM(toiar.override_pull_forward_margin)::decimal, 0) AS taoverride_pull_forward_margin,
			        ROUND(SUM(toiar.override_promo_spend)::decimal, 0) AS taoverride_promo_spend,
			        CASE
			            WHEN SUM(toiar.override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(toiar.override_revenue)::decimal, 2) / NULLIF(ROUND(SUM(toiar.override_sales_units)::decimal, 2), 0)::decimal,0
			            )
			        END as taoverride_aur,
			        CASE
			            WHEN SUM(toiar.override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(toiar.override_margin)::decimal, 2) / NULLIF(ROUND(SUM(toiar.override_sales_units)::decimal,2),0)::decimal,0
			            )
			        END as taoverride_aum,
			        CASE
			            WHEN SUM(toiar.override_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(toiar.override_margin)::decimal * 100) / NULLIF(SUM(toiar.override_revenue), 0)::decimal,1
			            )
			        END as taoverride_gm_percent
			    FROM
			        temp_override_ia_reccommend_results toiar
			        LEFT JOIN prod_master_cte pdmc
			        ON toiar.promo_id = pdmc.promo_id
			        LEFT JOIN price_promo.tb_promo_override_forecast tpof
			        ON toiar.promo_id = tpof.promo_id
			        AND tpof.scenario_id = 0
			    GROUP BY
			        toiar.promo_id
                    {group_by_in_unions}
			),
			final_stacked_metrics_cte as(
			        SELECT
			        tsr.promo_id,
			        tsr.scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(tsr.stacked_sales_units)::decimal, 1) AS stacked_sales_units,
			        ROUND(SUM(tsr.stacked_baseline_sales_units)::decimal, 1) AS stacked_baseline_sales_units,
			        ROUND(SUM(tsr.stacked_incremental_sales_units)::decimal, 1) AS stacked_incremental_sales_units,
			        ROUND(SUM(tsr.stacked_revenue)::decimal, 0) AS stacked_revenue,
			        ROUND(SUM(tsr.stacked_baseline_revenue)::decimal, 0) AS stacked_baseline_revenue,
			        ROUND(SUM(tsr.stacked_incremental_revenue)::decimal, 0) AS stacked_incremental_revenue,
			        ROUND(SUM(tsr.stacked_affinity_revenue)::decimal, 0) AS stacked_affinity_revenue,
			        ROUND(SUM(tsr.stacked_cannibalization_revenue)::decimal, 0) AS stacked_cannibalization_revenue,
			        ROUND(SUM(tsr.stacked_pull_forward_revenue)::decimal, 0) AS stacked_pull_forward_revenue,
			        ROUND(SUM(tsr.stacked_margin)::decimal, 0) AS stacked_margin,
			        ROUND(SUM(tsr.stacked_baseline_margin)::decimal, 0) AS stacked_baseline_margin,
			        ROUND(SUM(tsr.stacked_incremental_margin)::decimal, 0) AS stacked_incremental_margin,
			        ROUND(SUM(tsr.stacked_affinity_margin)::decimal, 0) AS stacked_affinity_margin,
			        ROUND(SUM(tsr.stacked_cannibalization_margin)::decimal, 0) AS stacked_cannibalization_margin,
			        ROUND(SUM(tsr.stacked_pull_forward_margin)::decimal, 0) AS stacked_pull_forward_margin,
			        ROUND(SUM(tsr.stacked_promo_spend)::decimal, 0) AS stacked_promo_spend,
			        CASE
			            WHEN SUM(tsr.stacked_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tsr.stacked_revenue)::decimal, 2) / NULLIF(ROUND(SUM(tsr.stacked_sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as stacked_aur,
			        CASE
			            WHEN SUM(tsr.stacked_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tsr.stacked_margin)::decimal, 2) / NULLIF(ROUND(SUM(tsr.stacked_sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as stacked_aum,
			        CASE
			            WHEN SUM(tsr.stacked_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tsr.stacked_margin)::decimal * 100) / NULLIF(SUM(tsr.stacked_revenue), 0)::decimal,
			                1
			            )
			        END as stacked_gm_percent
			    FROM
			        temp_stacked_scenario_results tsr
			        LEFT JOIN prod_master_cte pdmc
			        ON tsr.promo_id = pdmc.promo_id
			    GROUP BY
			        tsr.promo_id,
			        tsr.scenario_id
                    {group_by_in_unions}
			        ,tsr.is_default
			    UNION ALL
			    SELECT
			        tiar.promo_id,
			        0 AS scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(tiar.stacked_sales_units)::decimal, 1) AS stacked_sales_units,
			        ROUND(SUM(tiar.stacked_baseline_sales_units)::decimal, 1) AS stacked_baseline_sales_units,
			        ROUND(SUM(tiar.stacked_incremental_sales_units)::decimal, 1) AS stacked_incremental_sales_units,
			        ROUND(SUM(tiar.stacked_revenue)::decimal, 0) AS stacked_revenue,
			        ROUND(SUM(tiar.stacked_baseline_revenue)::decimal, 0) AS stacked_baseline_revenue,
			        ROUND(SUM(tiar.stacked_incremental_revenue)::decimal, 0) AS stacked_incremental_revenue,
			        ROUND(SUM(tiar.stacked_affinity_revenue)::decimal, 0) AS stacked_affinity_revenue,
			        ROUND(SUM(tiar.stacked_cannibalization_revenue)::decimal, 0) AS stacked_cannibalization_revenue,
			        ROUND(SUM(tiar.stacked_pull_forward_revenue)::decimal, 0) AS stacked_pull_forward_revenue,
			        ROUND(SUM(tiar.stacked_margin)::decimal, 0) AS stacked_margin,
			        ROUND(SUM(tiar.stacked_baseline_margin)::decimal, 0) AS stacked_baseline_margin,
			        ROUND(SUM(tiar.stacked_incremental_margin)::decimal, 0) AS stacked_incremental_margin,
			        ROUND(SUM(tiar.stacked_affinity_margin)::decimal, 0) AS stacked_affinity_margin,
			        ROUND(SUM(tiar.stacked_cannibalization_margin)::decimal, 0) AS stacked_cannibalization_margin,
			        ROUND(SUM(tiar.stacked_pull_forward_margin)::decimal, 0) AS stacked_pull_forward_margin,
			        ROUND(SUM(tiar.stacked_promo_spend)::decimal, 0) AS stacked_promo_spend,
			        CASE
			            WHEN SUM(tiar.stacked_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tiar.stacked_revenue)::decimal, 2) / NULLIF(ROUND(SUM(tiar.stacked_sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as stacked_aur,
			        CASE
			            WHEN SUM(tiar.stacked_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tiar.stacked_margin)::decimal, 2) / NULLIF(ROUND(SUM(tiar.stacked_sales_units)::decimal, 2), 0)::decimal,
			                0
			            )
			        END as stacked_aum,
			        CASE
			            WHEN SUM(tiar.stacked_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tiar.stacked_margin)::decimal * 100) / NULLIF(SUM(tiar.stacked_revenue), 0)::decimal,
			                1
			            )
			        END as stacked_gm_percent
			    FROM
			        temp_stacked_ia_reccommend_results tiar
			        LEFT JOIN prod_master_cte pdmc
			        ON tiar.promo_id = pdmc.promo_id
			    GROUP BY
			        tiar.promo_id,
			        pdmc.recommendation_type_id,
			        pdmc.recommendation_type,
			        pdmc.last_approved_scenario_id
                    {group_by_in_unions}
			        ,tiar.is_default
			),
			final_stacked_override_metrics_cte as(
			    SELECT
			        tosr.promo_id,
			        tosr.scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(tosr.stacked_override_sales_units)::decimal, 1) AS stacked_override_sales_units,
			        ROUND(SUM(tosr.stacked_override_baseline_sales_units)::decimal, 1) AS stacked_override_baseline_sales_units,
			        ROUND(SUM(tosr.stacked_override_incremental_sales_units)::decimal, 1) AS stacked_override_incremental_sales_units,
			        ROUND(SUM(tosr.stacked_override_revenue)::decimal, 0) AS stacked_override_revenue,
			        ROUND(SUM(tosr.stacked_override_baseline_revenue)::decimal, 0) AS stacked_override_baseline_revenue,
			        ROUND(SUM(tosr.stacked_override_incremental_revenue)::decimal, 0) AS stacked_override_incremental_revenue,
			        ROUND(SUM(tosr.stacked_override_affinity_revenue)::decimal, 0) AS stacked_override_affinity_revenue,
			        ROUND(SUM(tosr.stacked_override_cannibalization_revenue)::decimal, 0) AS stacked_override_cannibalization_revenue,
			        ROUND(SUM(tosr.stacked_override_pull_forward_revenue)::decimal, 0) AS stacked_override_pull_forward_revenue,
			        ROUND(SUM(tosr.stacked_override_margin)::decimal, 0) AS stacked_override_margin,
			        ROUND(SUM(tosr.stacked_override_baseline_margin)::decimal, 0) AS stacked_override_baseline_margin,
			        ROUND(SUM(tosr.stacked_override_incremental_margin)::decimal, 0) AS stacked_override_incremental_margin,
			        ROUND(SUM(tosr.stacked_override_affinity_margin)::decimal, 0) AS stacked_override_affinity_margin,
			        ROUND(SUM(tosr.stacked_override_cannibalization_margin)::decimal, 0) AS stacked_override_cannibalization_margin,
			        ROUND(SUM(tosr.stacked_override_pull_forward_margin)::decimal, 0) AS stacked_override_pull_forward_margin,
			        ROUND(SUM(tosr.stacked_override_promo_spend)::decimal, 0) AS stacked_override_promo_spend,
			        CASE
			            WHEN SUM(tosr.stacked_override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tosr.stacked_override_revenue)::decimal, 2) / NULLIF(ROUND(SUM(tosr.stacked_override_sales_units)::decimal, 2), 0)::decimal,0
			            )
			        END as stacked_override_aur,
			        CASE
			            WHEN SUM(tosr.stacked_override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(tosr.stacked_override_margin)::decimal, 2) / NULLIF(ROUND(SUM(tosr.stacked_override_sales_units)::decimal,2),0)::decimal,0
			            )
			        END as stacked_override_aum,
			        CASE
			            WHEN SUM(tosr.stacked_override_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(tosr.stacked_override_margin)::decimal * 100) / NULLIF(SUM(tosr.stacked_override_revenue), 0)::decimal,1
			            )
			        END as stacked_override_gm_percent
			    FROM
			        temp_stacked_override_scenario_results tosr
			        LEFT JOIN prod_master_cte pdmc
			        ON tosr.promo_id = pdmc.promo_id
			    GROUP BY
			        tosr.promo_id
                    {group_by_in_unions}
			        ,tosr.scenario_id
			    UNION ALL
			    SELECT
			        toiar.promo_id,
			        0 AS scenario_id
                    {column_selections_in_unions}
			        ,ROUND(SUM(toiar.stacked_override_sales_units)::decimal, 1) AS stacked_override_sales_units,
			        ROUND(SUM(toiar.stacked_override_baseline_sales_units)::decimal, 1) AS stacked_override_baseline_sales_units,
			        ROUND(SUM(toiar.stacked_override_incremental_sales_units)::decimal, 1) AS stacked_override_incremental_sales_units,
			        ROUND(SUM(toiar.stacked_override_revenue)::decimal, 0) AS stacked_override_revenue,
			        ROUND(SUM(toiar.stacked_override_baseline_revenue)::decimal, 0) AS stacked_override_baseline_revenue,
			        ROUND(SUM(toiar.stacked_override_incremental_revenue)::decimal, 0) AS stacked_override_incremental_revenue,
			        ROUND(SUM(toiar.stacked_override_affinity_revenue)::decimal, 0) AS stacked_override_affinity_revenue,
			        ROUND(SUM(toiar.stacked_override_cannibalization_revenue)::decimal, 0) AS stacked_override_cannibalization_revenue,
			        ROUND(SUM(toiar.stacked_override_pull_forward_revenue)::decimal, 0) AS stacked_override_pull_forward_revenue,
			        ROUND(SUM(toiar.stacked_override_margin)::decimal, 0) AS stacked_override_margin,
			        ROUND(SUM(toiar.stacked_override_baseline_margin)::decimal, 0) AS stacked_override_baseline_margin,
			        ROUND(SUM(toiar.stacked_override_incremental_margin)::decimal, 0) AS stacked_override_incremental_margin,
			        ROUND(SUM(toiar.stacked_override_affinity_margin)::decimal, 0) AS stacked_override_affinity_margin,
			        ROUND(SUM(toiar.stacked_override_cannibalization_margin)::decimal, 0) AS stacked_override_cannibalization_margin,
			        ROUND(SUM(toiar.stacked_override_pull_forward_margin)::decimal, 0) AS stacked_override_pull_forward_margin,
			        ROUND(SUM(toiar.stacked_override_promo_spend)::decimal, 0) AS stacked_override_promo_spend,
			        CASE
			            WHEN SUM(toiar.stacked_override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(toiar.stacked_override_revenue)::decimal, 2) / NULLIF(ROUND(SUM(toiar.stacked_override_sales_units)::decimal, 2), 0)::decimal,0
			            )
			        END as stacked_override_aur,
			        CASE
			            WHEN SUM(toiar.stacked_override_sales_units) = 0 THEN NULL
			            ELSE ROUND(
			                ROUND(SUM(toiar.stacked_override_margin)::decimal, 2) / NULLIF(ROUND(SUM(toiar.stacked_override_sales_units)::decimal,2),0)::decimal,0
			            )
			        END as stacked_override_aum,
			        CASE
			            WHEN SUM(toiar.stacked_override_revenue) = 0 THEN NULL
			            ELSE ROUND(
			                (SUM(toiar.stacked_override_margin)::decimal * 100) / NULLIF(SUM(toiar.stacked_override_revenue), 0)::decimal,1
			            )
			        END as stacked_override_gm_percent
			    FROM
			        temp_stacked_override_ia_reccommend_results toiar
			        LEFT JOIN prod_master_cte pdmc
			        ON toiar.promo_id = pdmc.promo_id
			        LEFT JOIN price_promo.tb_promo_override_forecast tpof
			        ON toiar.promo_id = tpof.promo_id
			        AND tpof.scenario_id = 0
			    GROUP BY
			        toiar.promo_id
                    {group_by_in_unions}
			),
			final_metrics_cte AS (
			    SELECT 
                    fom.*,
                    fomc.override_sales_units,
                    fomc.override_baseline_sales_units,
                    fomc.override_incremental_sales_units,
                    fomc.override_revenue,
                    fomc.override_baseline_revenue,
                    fomc.override_incremental_revenue,
                    fomc.override_affinity_revenue,
                    fomc.override_cannibalization_revenue,
                    fomc.override_pull_forward_revenue,
                    fomc.override_margin,
                    fomc.override_baseline_margin,
                    fomc.override_incremental_margin,
                    fomc.override_affinity_margin,
                    fomc.override_cannibalization_margin,
                    fomc.override_pull_forward_margin,
                    fomc.override_promo_spend,
                    fomc.override_aur,
                    fomc.override_aum,
                    fomc.override_gm_percent,
                    fsmc.stacked_sales_units,
                    fsmc.stacked_baseline_sales_units,
                    fsmc.stacked_incremental_sales_units,
                    fsmc.stacked_revenue,
                    fsmc.stacked_baseline_revenue,
                    fsmc.stacked_incremental_revenue,
                    fsmc.stacked_affinity_revenue,
                    fsmc.stacked_cannibalization_revenue,
                    fsmc.stacked_pull_forward_revenue,
                    fsmc.stacked_margin,
                    fsmc.stacked_baseline_margin,
                    fsmc.stacked_incremental_margin,
                    fsmc.stacked_affinity_margin,
                    fsmc.stacked_cannibalization_margin,
                    fsmc.stacked_pull_forward_margin,
                    fsmc.stacked_promo_spend,
                    fsmc.stacked_aur,
                    fsmc.stacked_aum,
                    fsmc.stacked_gm_percent,
                    fsomc.stacked_override_sales_units,
                    fsomc.stacked_override_baseline_sales_units,
                    fsomc.stacked_override_incremental_sales_units,
                    fsomc.stacked_override_revenue,
                    fsomc.stacked_override_baseline_revenue,
                    fsomc.stacked_override_incremental_revenue,
                    fsomc.stacked_override_affinity_revenue,
                    fsomc.stacked_override_cannibalization_revenue,
                    fsomc.stacked_override_pull_forward_revenue,
                    fsomc.stacked_override_margin,
                    fsomc.stacked_override_baseline_margin,
                    fsomc.stacked_override_incremental_margin,
                    fsomc.stacked_override_affinity_margin,
                    fsomc.stacked_override_cannibalization_margin,
                    fsomc.stacked_override_pull_forward_margin,
                    fsomc.stacked_override_promo_spend,
                    fsomc.stacked_override_aur,
                    fsomc.stacked_override_aum,
                    fsomc.stacked_override_gm_percent
                FROM final_original_metrics_cte fom
			    LEFT JOIN final_override_metrics_cte fomc
                on fom.promo_id = fomc.promo_id and fom.scenario_id = fomc.scenario_id
                and 
                {original_override_join_condition}
			    LEFT JOIN final_stacked_metrics_cte fsmc
                on fom.promo_id = fsmc.promo_id and fom.scenario_id = fsmc.scenario_id
                and
                {stacked_join_condition}
			    LEFT JOIN final_stacked_override_metrics_cte fsomc
                on fom.promo_id = fsomc.promo_id and fom.scenario_id = fsomc.scenario_id
                and
                {stacked_override_join_condition}
			)
			SELECT
			    fmc.promo_id,
			    fmc.promo_name,
                tcm.currency_id,
                tcm.currency_name,
                tcm.currency_symbol
                {final_select_columns}
			    ,fmc.scenario_type,
			    fmc.scenario_id,
			    fmc.scenario_order_id,
			    fmc.scenario_name,
			    fmc.sales_units,
			    fmc.sales_units as total_sales_units,
			    fmc.baseline_sales_units,
			    fmc.incremental_sales_units,
			    fmc.revenue,
			    fmc.revenue as total_revenue,
			    fmc.baseline_revenue,
			    fmc.incremental_revenue,
			    fmc.margin,
			    fmc.margin as total_margin,
			    fmc.baseline_margin,
			    fmc.incremental_margin,
			    fmc.promo_spend,
			    fmc.aur,
			    fmc.gm_percent,
			    fmc.override_sales_units,
			    fmc.override_sales_units as override_total_sales_units,
			    fmc.override_baseline_sales_units,
			    fmc.override_incremental_sales_units,
			    fmc.override_revenue,
			    fmc.override_revenue as override_total_revenue,
			    fmc.override_baseline_revenue,
			    fmc.override_incremental_revenue,
			    fmc.override_margin,
			    fmc.override_margin as override_total_margin,
			    fmc.override_baseline_margin,
			    fmc.override_incremental_margin,
			    fmc.override_promo_spend,
			    fmc.override_aur,
			    fmc.override_gm_percent,
			    fmc.stacked_sales_units,
			    fmc.stacked_sales_units as stacked_total_sales_units,
			    fmc.stacked_baseline_sales_units,
			    fmc.stacked_incremental_sales_units,
			    fmc.stacked_revenue,
			    fmc.stacked_revenue as stacked_total_revenue,
			    fmc.stacked_baseline_revenue,
			    fmc.stacked_incremental_revenue,
			    fmc.stacked_margin,
			    fmc.stacked_margin as stacked_total_margin,
			    fmc.stacked_baseline_margin,
			    fmc.stacked_incremental_margin,
			    fmc.stacked_promo_spend,
			    fmc.stacked_aur,
			    fmc.stacked_gm_percent,
			    fmc.stacked_override_sales_units,
			    fmc.stacked_override_sales_units as stacked_override_total_sales_units,
			    fmc.stacked_override_baseline_sales_units,
			    fmc.stacked_override_incremental_sales_units,
			    fmc.stacked_override_revenue,
			    fmc.stacked_override_revenue as stacked_override_total_revenue,
			    fmc.stacked_override_baseline_revenue,
			    fmc.stacked_override_incremental_revenue,
			    fmc.stacked_override_margin,
			    fmc.stacked_override_margin as stacked_override_total_margin,
			    fmc.stacked_override_baseline_margin,
			    fmc.stacked_override_incremental_margin,
			    fmc.stacked_override_promo_spend,
			    fmc.stacked_override_aur,
			    fmc.stacked_override_gm_percent
			FROM
			    final_metrics_cte fmc
            inner join 
                global.tb_currency_master tcm 
                on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte)
            ;
			', array_to_string(sce_ids, ',')
        );	

        end if;
        raise notice 'query   -   %', query;
        execute query;
        end
    $$;          
    select * from tb_temp_dsr_data_{promo_id};  
"""

FETCH_OVERALL_SIM_RESULTS_DEATILS_QUERY = """
    {base_query}
    SELECT
        amc.promo_id,
        amc.promo_name,
        amc.recommendation_type_id,
        amc.recommendation_type,
        amc.last_approved_scenario_id,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        amc.is_default,
        amc.scenario_type, 
        amc.scenario_id,
        amc.scenario_order_id, 
        amc.scenario_name,
        0 as product_id,
        'overall' as product_name,
        --
        ROUND(amc.sales_units::decimal, 2) AS sales_units,
        ROUND(amc.baseline_sales_units::decimal, 2) AS baseline_sales_units, 
        ROUND(amc.incremental_sales_units::decimal, 2) AS incremental_sales_units,
        -- 
        ROUND(amc.sales_units::decimal, 2) AS total_sales_units,
        --
        ROUND(amc.revenue::decimal, 2) AS revenue,
        ROUND(amc.baseline_revenue::decimal, 2) AS baseline_revenue,
        ROUND(amc.incremental_revenue::decimal, 2) AS incremental_revenue,
        ROUND(amc.affinity_revenue::decimal, 2) AS affinity_revenue,
        ROUND(amc.cannibalization_revenue::decimal, 2) AS cannibalization_revenue,
        ROUND(amc.pull_forward_revenue::decimal, 2) AS pull_forward_revenue, 
        ROUND(amc.revenue::decimal, 2) AS total_revenue,
        ---
        ROUND(amc.margin::decimal, 2) AS margin,
        ROUND(amc.baseline_margin::decimal, 2) AS baseline_margin,
        ROUND(amc.incremental_margin::decimal, 2) AS incremental_margin,
        ROUND(amc.affinity_margin::decimal, 2) AS affinity_margin,
        ROUND(amc.cannibalization_margin::decimal, 2) AS cannibalization_margin,
        ROUND(amc.pull_forward_margin::decimal, 2) AS pull_forward_margin, 
        ROUND(amc.margin::decimal, 2) AS total_margin,
        --
        ROUND(amc.promo_spend::decimal, 2) AS promo_spend,
        CASE 
            WHEN amc.sales_units = 0 THEN NULL 
            ELSE ROUND((amc.revenue / amc.sales_units)::decimal, 2)
        END AS aur,
        CASE 
            WHEN amc.sales_units = 0 THEN NULL 
            ELSE ROUND((amc.margin / amc.sales_units)::decimal, 2)
        END AS aum,
        CASE 
            WHEN amc.revenue = 0 THEN 0 
            ELSE ROUND(((amc.margin * 100) / amc.revenue)::decimal, 2) 
        END AS gm_percent,
        amc.total_inventory AS total_inventory,
        amc.finalized_st_percent AS finalized_st_percent
    FROM 
        agg_metrics_cte amc
    inner join 
        global.tb_currency_master tcm 
        on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte);
"""

UPDATE_IS_PROCESSING_QUERY = """
update
    {promo_schema}.promo_master
set
    is_under_processing = {processing_flag}
where
    promo_id = ANY({promo_id_str}::int[])
"""


UPDATE_IS_AUTO_RESIMULATED_QUERY = """
update
    {promo_schema}.promo_master
set
    is_auto_resimulated = {auto_resimulated_flag}
where
    promo_id = ANY({promo_id_str}::int[])
"""

INSERT_ACTION_LOG = """
CREATE TEMP TABLE temp_action_log (action_log_id INTEGER);

DO $$
DECLARE
    _action_log_id integer;
BEGIN

-- Insert into tb_action_log_master returning action_log_id
INSERT INTO {promo_schema}.tb_action_log_master
(promo_ids, screen_name, processing_action, processing_status, created_by, created_at)
values
({promo_id_str}::int[], {screen_type}, {action_name}, {status}, {user_id}, Now())
RETURNING id into _action_log_id;

-- Insert into tb_action_log_payload with the generated action_log_id
INSERT INTO {promo_schema}.tb_action_log_payload
(action_log_id, payload)
values
(_action_log_id, {payload});

-- Insert generated action log id into temp table
INSERT INTO temp_action_log values (_action_log_id);

END $$;

SELECT action_log_id from temp_action_log;
"""


UPDATE_ACTION_LOG_QUERY = """
update
	{promo_schema}.tb_action_log_master
set
	processing_status = {status},
	updated_by = {user_id},
	updated_at = now()
where
	id = {action_log_id}
"""

EXCLUSION_CHECK_VALID_SKU_QUERY = """
    with joined_cte as (
            select 
                user_ip.sku, pm.{id_column}, pm.{cid_column}, pm.{value_column}
            from
                (select unnest(array[{product_ids_list}]) as sku) user_ip
            left join 
                {promo_schema}.product_master pm on pm.{id_column}::text = user_ip.sku::text
        )
        select jsonb_agg(jsonb_build_object('SVS_ID', sku, 'client_product_id', {id_column}::text, 'product_id', {cid_column}, 'product_name', {value_column})) as data, 1 as is_valid from joined_cte where {id_column} is not null 
        union
        select jsonb_agg(jsonb_build_object('SVS_ID', sku, 'client_product_id', {id_column}::text, 'product_id', {cid_column}, 'product_name', {value_column})) as data, 0 as is_valid from joined_cte where {id_column} is null
"""

EXCLUSION_CHECK_HIERARCHY_VALIDITY_QUERY = """
    with user_ip_cte as (
        select 
            {select_str}
    )
    select 
        distinct {main_select_str} 
    from 
        user_ip_cte A
    left join 
        {promo_schema}.tb_product_hierarchy_combination B 
    on 
        {join_str}
"""


INSERT_TB_REPORT_QUERY = """
insert
	into
	{markdown_schema}.tb_report_queries(
		query,
		sheet_name
	)
values (
	'{insert_query}',
	{sheet_name}
) returning id;
"""

FETCH_ELIGIBLE_PROMO_QUERY = """
select
    prs.promo_id
from
    {promo_schema}.ps_recommended_scenarios_agg prs 
where promo_id in ({promo_id_str})
group by promo_id
"""


GET_CONFLICTED_PROMOS = """
    select 
        *
    from 
        {promo_schema}.fn_warning_for_finalize_offer_with_exclusion(array{promo_ids}::integer[])
"""

FETCH_EXMD_TEMPLATE_ID_QUERY = """
SELECT
	template_id as value,
	display_name as label
from
	{promo_schema}.tb_exmd_template_id
where is_active = true;
"""

FETCH_EXMD_PRICE_FILTER_QUERY = """
with promo_filter_id_cte as (
	select distinct 
		pm.promo_id,
		hierarchy_value_name as lifecycle_indicator
	from
		{promo_schema}.included_product_hierarchy iph
	join {promo_schema}.promo_master pm on
		iph.promo_id = pm.promo_id
	where
		hierarchy_level_name = 'Lifecycle Indicator'
		and pm.promo_id = {promo_id}
		and pm.product_selection_type = 2 -- Whole category filter
),
price_filter_id_cte as (
	select
		case 
			when count(pfic.lifecycle_indicator) = 0 or count(pfic.lifecycle_indicator) = (select count(distinct lifecycle_indicator) from global.tb_lifecycle_indicator_config) then array[100]
			when count(pfic.lifecycle_indicator) = 1 and ('REGULAR PRICE' in (select lifecycle_indicator from promo_filter_id_cte)) then array[101, 0]
			when count(pfic.lifecycle_indicator) > 1 and ('REGULAR PRICE' not in (select lifecycle_indicator from promo_filter_id_cte)) then array[102]
			when count(pfic.lifecycle_indicator) = 1 and ('FIRST MARKDOWN' in (select lifecycle_indicator from promo_filter_id_cte)) then array[1]
			when count(pfic.lifecycle_indicator) = 1 and ('SECOND MARKDOWN' in (select lifecycle_indicator from promo_filter_id_cte)) then array[2]
			when count(pfic.lifecycle_indicator) = 1 and ('FINAL SALE PRICE' in (select lifecycle_indicator from promo_filter_id_cte)) then array[3]
			when count(pfic.lifecycle_indicator) >= 2 and ('REGULAR PRICE' in (select lifecycle_indicator from promo_filter_id_cte)) then array[100]
		end	as price_filter_id	
	from promo_filter_id_cte pfic
)
SELECT
    tepf.price_filter_id AS value,
    tepf.display_name AS label
FROM
    {promo_schema}.tb_exmd_price_filter tepf
-- Join promo_master to check product_selection_type
JOIN {promo_schema}.promo_master pm ON pm.promo_id = {promo_id}
-- Apply filtering only when product_selection_type = 2
WHERE 
    (
        pm.product_selection_type = 2 
        AND tepf.price_filter_id = ANY(SELECT UNNEST(price_filter_id) FROM price_filter_id_cte)
    ) 
    OR pm.product_selection_type != 2  -- Show all values when not product_selection_type = 2
    AND tepf.is_active = TRUE
ORDER BY tepf.price_filter_id;
"""

FETCH_EXMD_TARGET_FOLDER_QUERY = """
with promo_cte as (
	select
		start_date,
		end_date
	from
		{promo_schema}.promo_master
	where
		promo_id = {promo_id}
)
select
	folder_id as value,
	display_name as label
from
	{promo_schema}.tb_exmd_target_folder tetf
join 
    promo_cte pc
on
	tetf.start_date <= pc.start_date
	and tetf.end_date >= pc.end_date
	and tetf.is_active = TRUE;
"""

FETCH_EXMD_SFCC_ATS_CHECK_QUERY = """
select
	sfcc_ats_check_id as value,
	display_name as label
from
	{promo_schema}.tb_exmd_sfcc_ats_check
where
    is_active = true;
"""

FETCH_EXMD_SFCC_DROPSHIP_OPTIONS_QUERY = """
select
	sfcc_dropship_id as value,
	display_name as label
from
	price_promo.tb_exmd_sfcc_dropship_options
where
	is_active = true;
"""

SAVE_EXMD_QUERY = """
DO $$
BEGIN
DELETE FROM {promo_schema}.tb_exmd_promo
WHERE promo_id = {promo_id};

INSERT INTO {promo_schema}.tb_exmd_promo (
    promo_id,
    template_id,
    price_filter_id,
    folder_id,
    sfcc_ats_check_id,
    sfcc_dropship_id,
    promo_code,
    receipt_text_eng,
    receipt_text_french,
    sfcc_pip_text,
    sfcc_tender_type_promo_msg,
    sfcc_pip_customer_group,
    sfcc_customer_group,
    sfcc_pip_rank,
    sfcc_rank
) VALUES (
    {promo_id},
    {template_id},
    {price_filter_id},
    {folder_id},
    {sfcc_ats_check_id},
    {sfcc_dropship_id},
    {promo_code},
    {receipt_text_eng},
    {receipt_text_french},
    {sfcc_pip_text},
    {sfcc_tender_type_promo_msg},
    {sfcc_pip_customer_group},
    {sfcc_customer_group},
    {sfcc_pip_rank},
    {sfcc_rank}
);

update {promo_schema}.promo_master set step_count = 4, updated_at = now() where promo_id = {promo_id};
END $$;
"""

FETCH_EXMD_QUERY = """
SELECT
    promo_id,
    ARRAY[json_build_object('label', teti."display_name", 'value', tep.template_id)] AS template_id,
    ARRAY[json_build_object('label', tepf."display_name", 'value', tep.price_filter_id)] AS price_filter_id,
    ARRAY[json_build_object('label', tef."display_name", 'value', tep.folder_id)] AS folder_id,
    CASE
        WHEN tep.sfcc_ats_check_id IS NOT NULL THEN 
            ARRAY[json_build_object('label', tsac."display_name", 'value', tep.sfcc_ats_check_id)]
        ELSE 
            NULL
    END AS sfcc_ats_check_id,
    CASE
        WHEN tep.sfcc_dropship_id IS NOT NULL THEN 
            ARRAY[json_build_object('label', tsdo."display_name", 'value', tep.sfcc_dropship_id)]
        ELSE 
            NULL
    END AS sfcc_dropship_id,
    promo_code,
    receipt_text_eng,
    receipt_text_french,
    sfcc_pip_text,
    sfcc_tender_type_promo_msg,
    sfcc_pip_customer_group,
    sfcc_customer_group,
    sfcc_pip_rank,
    sfcc_rank
FROM {promo_schema}.tb_exmd_promo tep
LEFT JOIN {promo_schema}.tb_exmd_template_id teti
    ON tep.template_id = teti.template_id
LEFT JOIN {promo_schema}.tb_exmd_price_filter tepf
    ON tep.price_filter_id = tepf.price_filter_id
LEFT JOIN {promo_schema}.tb_exmd_target_folder tef
    ON tep.folder_id = tef.folder_id
LEFT JOIN {promo_schema}.tb_exmd_sfcc_ats_check tsac
    ON tep.sfcc_ats_check_id = tsac.sfcc_ats_check_id
LEFT JOIN {promo_schema}.tb_exmd_sfcc_dropship_options tsdo
    ON tep.sfcc_dropship_id = tsdo.sfcc_dropship_id
WHERE promo_id = {promo_id};
"""


BASE_DETAILED_SIMULATION_RESULTS_QUERY = """
    select * from price_promo.fn_get_detailed_simulation_results_base_query({promo_id},{aggregation},{target_currency_id})
"""

FETCH_DETAILED_SIMULATION_RESULTS_QUERY = """
    {base_query}
    SELECT
        fmc.promo_id,
        fmc.promo_name,
        fmc.recommendation_type_id,
        fmc.recommendation_type,
        fmc.last_approved_scenario_id,
        tcm.currency_id,
        tcm.currency_name,
        tcm.currency_symbol,
        fmc.product_id,
        fmc.product_name,
        fmc.scenario_type,
        fmc.scenario_id,
        fmc.scenario_order_id,
        fmc.scenario_name,
        fmc.is_default,
        fmc.sales_units,
        fmc.sales_units as total_sales_units,
        fmc.baseline_sales_units,
        fmc.incremental_sales_units,
        fmc.revenue,
        fmc.revenue as total_revenue,
        fmc.baseline_revenue,
        fmc.incremental_revenue,
        fmc.affinity_revenue,
        fmc.cannibalization_revenue,
        fmc.pull_forward_revenue,
        fmc.margin,
        fmc.margin as total_margin,
        fmc.baseline_margin,
        fmc.incremental_margin,
        fmc.affinity_margin,
        fmc.cannibalization_margin,
        fmc.pull_forward_margin,
        fmc.promo_spend,
        fmc.aur,
        fmc.aum,
        fmc.gm_percent,
        fmc.total_inventory,
        fmc.finalized_st_percent
    FROM
        final_metrics_cte fmc
    inner join 
        global.tb_currency_master tcm 
        on tcm.currency_id = (SELECT target_currency_id FROM target_currency_cte);
"""

CHECK_EXMD_QUERY = """
SELECT
    promo_id
FROM {promo_schema}.tb_exmd_promo
WHERE promo_id in {promo_id_list}
AND price_filter_id is NOT NULL
AND folder_id is NOT NULL;
"""


FETCH_PREVIOUSLY_SYNCED_PROMO_ID_QUERY = """
    Select array_agg(promo_id) as promo_ids from {promo_schema}.promo_master where promo_id in ({promo_id_list}) and last_exmd_synced_time is not null;
"""


OVERRIDE_FORECAST_FOR_A_PROMO = """
    select
    *
    from 
    price_promo.fn_override_forecast_for_a_promo(
        {promo_id},
        {scenario_id},
        {new_sales_units},
        {new_baseline_sales_units},
        {old_sales_units},
        {old_baseline_sales_units},
        {reason},
        {user_id},
        {comment},
        {override_method},
        {from_stacking_view}
    );
"""


GET_OVERRIDE_FORECAST_FOR_A_PROMO = """
    select
    * 
    from 
    price_promo.fn_get_override_forecast_for_a_promo(
        {promo_id},
        {scenario_id},
        {new_sales_units},
        {new_baseline_sales_units},
        {old_sales_units},
        {old_baseline_sales_units},
        {override_method},
        {from_stacking_view},
        {target_currency_id}
    ) override_forecast
"""


FETCH_OVERRIDE_REASON = """
select
	reason as label,
    id as value
from
	{promo_schema}.tb_override_reason;
"""

SET_DEFAULT_FOR_SCENARIO = """
    select
    * 
    from 
    price_promo.fn_set_default_for_a_scenario(
    {promo_id},
    {scenario_id},
    {default},
    {user_id},
    {from_stacking_view}
    )
"""

GET_PROMO_STACKED_OFFERS = """
    select
    * 
    from 
    price_promo.fn_get_promo_stacked_offers(
        {promo_id}
    )
"""

IS_FINALISED_OR_EXECUTION_APPROVED_PROMO_PRESENT = """
    select 
        case when count(*) > 0 then true else false end as is_finalised_or_execution_approved_promo_present
    from 
        {promo_schema}.promo_master pm
    where 
        pm.promo_id in ({promo_ids}) 
        and pm.status in (select status_id from {promo_schema}.promo_status_config psc where psc.status_name in('Finalized', 'Execution Approved'));
"""

UPDATE_STRATEGY_SIMULATION_FLAG = """
    update 
        price_promo.promo_master
    set is_simulation_disabled = {is_simulation_disabled}
    where promo_id = {promo_id}
"""

CHECK_IF_STEP3_IS_DISABLED = """
    select
        is_simulation_disabled as is_step3_disabled
    from    
        {promo_schema}.promo_master
    where promo_id = {promo_id}
"""

IS_FINALIZED_SCENARIO_QUERY = """
    select
        1
    from 
        price_promo.promo_master
    where promo_id = {promo_id}
    and coalesce(last_approved_scenario_id,0) = {scenario_id}
    and status = any(array(select remarks::int2[] from metaschema.tb_app_sub_master where name = 'stacked_offers_eligibility'))
"""

GET_STACKED_OFFERS_OF_PROMO = """
    select
        array_agg(tpsom.promo_id) as stacked_offers
    from    
        price_promo.promo_master pm
    inner join
        price_promo.tb_promo_stacked_offers_mapping tpsom
    on pm.promo_id = tpsom.stacked_promo_id
    where tpsom.stacked_promo_id = {promo_id} and pm.step_count >= 3
"""

CREATE_PLACEHOLDER_PROMO = """
    select 
        price_promo.fn_create_placeholder_promo(
            {promo_name},
            {start_date}::date,
            {end_date}::date,
            {metrics}::jsonb,
            {user_id},
            {event_id}
        ) promo_id
"""

UPDATE_PLACEHOLDER_PROMO = """
    select
        price_promo.fn_update_placeholder_promo(
            {promo_id},
            {promo_name},
            {start_date}::date,
            {end_date}::date,
            {metrics}::jsonb,
            {user_id},
            {event_id}
        ) promo_id
"""


REOPTIMISATION_PRE_PROCESS = """
do
$$
declare
    _tables_to_be_deleted text[];
    _table text;
    _last_recommendation_type text;
begin

    select
        tasm.name into _last_recommendation_type
    from price_promo.promo_master pm
    left join metaschema.tb_app_sub_master tasm on pm.recommendation_type_id = tasm.id
    where promo_id = {promo_id};

    if _last_recommendation_type = 'optimise' then
        _tables_to_be_deleted = array[
            'ia_scenario_master', 'ia_ps_scenario_discounts', 'ps_recommended_ia_projected',
            'ps_recommended_ia_projected_agg', 'ps_recommended_finalized', 'ps_recommended_finalized_agg',
            'ps_recommended_override_ia', 'ps_recommended_override_ia_agg'
        ]::text[];
    else
        _tables_to_be_deleted = array[
            'ps_recommended_override_ia', 'ps_recommended_override_ia_agg', 'tb_promo_override_forecast'
        ]::text[];
    end if;

    for _table in (select unnest(_tables_to_be_deleted) table_name)
    loop
        execute format(
        'delete from price_promo.%1$s where promo_id = %2$s',
        _table,
        {promo_id}
        );
    end loop;

    delete from price_promo.tb_promo_override_forecast
    where promo_id = {promo_id} and scenario_id=0;

    if (
        _last_recommendation_type = 'optimise' 
    ) then
        update price_promo.promo_master
        set status = 0,
            last_approved_scenario_id = null,
            recommendation_type_id = null
        where promo_id = {promo_id};
    end if;

end;
$$;
"""


UPDATE_PROMO_STATUS_AND_REFRESH_STACKED_PROMOS_MAPPING = """
    update price_promo.promo_master pm
        set status = {status},
        is_overridden_scenario_finalized = (
            case
                when exists(
                    select 1 
                    from price_promo.tb_promo_override_forecast
                    where promo_id = pm.promo_id 
                    and scenario_id = pm.last_approved_scenario_id
                    and is_default is true
                ) then true else false
            end
        )
    where promo_id = any({promo_ids}::int[]);
    select price_promo.fn_update_promo_stacked_offers_mapping_after_finalize({promo_ids}::int[]);
"""


FILTERED_PROMOTIONS = """
    with filtered_promos_cte as (
        select price_promo.fn_filter_promos(
            {start_date},
            {end_date},
            {product_hierarchies},
            {store_hierarchies},
            {event_ids},
            {show_partially_overlapping_events}
        ) as promos
    )
    select
        promo_id as value,
        name as label
    from price_promo.promo_master,
    filtered_promos_cte
    where promo_id = any(
        filtered_promos_cte.promos
    )
    {finalized_condition}
"""

VALIDATE_COPY_OFFER_QUERY = """
    select 
        * 
    from 
        price_promo.fn_validate_copy_offers(
            {request_obj}::jsonb
        )
"""

GET_VENDOR_FUNDING_TYPES = """
    select 
        enum_range(null::price_promo.vendor_funding_types)::text[] as vendor_funding_types
"""

PROMOS_PROCESS_CHECK = """
select 
    'Offer' as plan
from 
    price_promo.promo_master as pm
where pm.is_under_processing != 0 and promo_id = any(array{selected_promos}::integer[])
"""

GET_SPECIAL_OFFERS_LIST_QUERY = """
    SELECT 
        offer_display_name as label,
        offer_name as value
    FROM 
        price_promo.tb_special_offers
    WHERE 
        is_active = true
    ORDER BY 
        offer_name
"""

GET_SPECIAL_OFFER_DETAILS_QUERY = """
    SELECT 
        A.id,
        A.offer_name as name,
        A.offer_display_name as display_name,
        A.customer_reach,
        A.customer_redemption_rate,
        A.discount_type_id,
        B.name as discount_type,
        A.discount_value
    FROM 
        price_promo.tb_special_offers A
    left join 
        metaschema.tb_app_sub_master B on A.discount_type_id = B.id
    WHERE 
        A.is_active = true 
        and A.offer_name = {offer_identifier}
"""

GET_EXPECTED_TIME_FOR_SIMULATION_QUERY = """
    select 
        time_in_mins,
        promo_duration,
        original_store_count as store_count,
        original_product_count as product_count,
        scenario_count_out as scenario_count
     from price_promo_opt.calculate_estimated_time_simulation(
        {promo_id},
        {store_count},
        {product_count},
        {scenario_count}
    );
"""

GET_EXPECTED_TIME_FOR_OPTIMIZATION_QUERY = """
    select * from price_promo_opt.calculate_estimated_time_optimization({promo_id});
"""

GET_PROMO_DISCOUNTS_TABLE_METADATA = """
    select jsonb_build_object(
        'product_level_columns',jsonb_build_object(
            'columns',(
                select 
                    array_agg(
                        jsonb_build_object(
                            'column_name', dlc.discount_level_value,
                            'id_key', dlc.id_key,
                            'value_key', dlc.value_key 
                        )
                    )
                from 
                (
                    select unnest(product_discount_level) as discount_level_id
                    from price_promo.ps_rules 
                    where promo_id = {promo_id}
                ) psr
                inner join price_promo.discount_level_config dlc
                on psr.discount_level_id = dlc.discount_level_id
                where dlc.category='product' and psr.discount_level_id != -200
            )
        ),
        'store_level_columns',jsonb_build_object(
            'columns',(
                select 
                    array_agg(
                        jsonb_build_object(
                            'column_name', dlc.discount_level_value,
                            'id_key', dlc.id_key,
                            'value_key', dlc.value_key 
                        )
                    )
                from 
                (
                    select unnest(store_discount_level) as discount_level_id
                    from price_promo.ps_rules 
                    where promo_id = {promo_id}
                ) psr
                inner join price_promo.discount_level_config dlc
                on psr.discount_level_id = dlc.discount_level_id
                where dlc.category='store' and psr.discount_level_id != -200
            )
        ),
        'customer_level_columns',jsonb_build_object(
            'columns',(
                select 
                    array_agg(
                        jsonb_build_object(
                            'column_name', dlc.discount_level_value,
                            'id_key', dlc.id_key,
                            'value_key', dlc.value_key 
                        )
                    )
                from 
                (
                    select unnest(customer_discount_level) as discount_level_id
                    from price_promo.ps_rules 
                    where promo_id = {promo_id}
                ) psr
                inner join price_promo.discount_level_config dlc
                on psr.discount_level_id = dlc.discount_level_id
                where dlc.category='customer' and psr.discount_level_id != -200
            )
        ),
        'remaining_columns', jsonb_build_object(
            'columns',(
                select
                    array_agg(
                        jsonb_build_object(
                            'column_name', s.column_name,
                            'id_key', s.id_key,
                            'value_key', s.value_key 
                        )
                    )
                from 
                (
                   select 
                        'ia_recommended' as id_key,
                        'ia_recommended' as value_key,
                        'IA Recommended' as column_name 
                    union all
                    select 
                        format('scenario_%1$s',scenario_order_id) as id_key,
                        format('scenario_%1$s',scenario_order_id) as value_key,
                        scenario_name as column_name
                    from price_promo.scenario_master sm
                    where sm.promo_id = {promo_id}
                ) s
            ) 
        )
    ) response
"""

GET_PROMO_DISCOUNTS = """
    select
    * 
    from
    price_promo.fn_get_promo_discounts(
        {promo_id},
        {page},
        {limit},
        {sort_key},
        {sort_order},
        {filters},
        {include_temporary_saved_changes},
        {user_id}
    )
"""

GET_TIER_DATA = """
    select
        jsonb_object_agg(
            tier_id,
            tier_name
        ) as tier_data
    from price_promo.tier_master
    where promo_id = {promo_id}

"""

GET_SCENARIO_DATA = """
    select
        jsonb_object_agg(
            scenario_id,scenario_name
        ) as scenario_data
    from price_promo.scenario_master
    where promo_id = {promo_id}
"""

GET_NEW_SCENARIO_ID = """
    select nextval('price_promo.scenario_master_scenario_id_seq'::regclass) new_scenario_id
"""

CREATE_NEW_SCENARIOS_DATA = """
    insert into price_promo.scenario_master
    (promo_id,scenario_id,scenario_name,scenario_order_id,created_by,updated_at)
    values
    {values_list};
"""

VALIDATE_MISSING_DISCOUNT_VALUES = """
    select price_promo.fn_validate_missing_discounts(
        {promo_id},
        {user_id},
        {include_temporary_saved_changes},
        {new_scenario_order_ids}::int[],
        {row_ids_to_skip}::int[]
    ) has_missing_discount_values
"""

UPDATE_DISCOUNT_VALUES = """
    do 
    $$
    begin

    {new_scenarios_insertion_query}
    if {include_temporary_saved_changes} and {has_discount_values} then
        -- if there are any more changes in the discount values, then we need to persist them in the temp table
        perform price_promo.fn_bulk_edit_discounts(
            {promo_id},
            null,
            null,
            null,
            null::jsonb,
            {discount_values},
            (select session_id from price_promo.tb_user_promo_session where user_id = {user_id} and promo_id = {promo_id}),
            {user_id}
        );
    end if;

    perform price_promo.fn_update_discount_values(
        {promo_id},
        {discount_values}::jsonb,
        {updated_scenario_ids}::int[],
        {user_id},
        {include_temporary_saved_changes}
    );

    end
    $$;
"""

GET_PROMO_SCENARIO_IDS = """
    select 
        coalesce(
            array_agg(scenario_id),
            (select array_agg(scenario_id) from price_promo.scenario_master where promo_id = {promo_id})
        ) as scenario_ids
    from price_promo.scenario_master sm
    where promo_id = {promo_id}
    {updated_scenario_ids_condition}
"""

BULK_EDIT_DISCOUNTS = """
    select 
        price_promo.fn_bulk_edit_discounts(
            {promo_id},
            {selected_rows}::int[],
            {unselected_rows}::int[],
            {filters}::jsonb,
            {bulk_edit_data}::jsonb,
            {discounts_data}::jsonb,
            {session_id},
            {user_id},
            {include_temporary_saved_changes}
        )
"""

GET_PROMO_OFFER_TYPE_DETAILS = """
    select
        pr.discount_type_id offer_type_id,
        tasm.name as offer_type_value
    from price_promo.ps_rules pr
    inner join metaschema.tb_app_sub_master tasm
    on pr.discount_type_id = tasm.id
    where promo_id = {promo_id}
    """
    
GET_PRODUCT_DETAILS_OF_PROMO = """
   select
        pm.*,
        cm.currency_symbol as currency_symbol,
        tlia.total_inventory,
        ROUND(prf.revenue::decimal,2) as finalized_revenue,
        ROUND(prf.margin::decimal,2) as finalized_margin,
        ROUND(prf.units::decimal,2) as finalized_units,
        ROUND(((prf.margin * 100) / nullif(prf.revenue,0))::decimal, 2) as finalized_gm_percent,
        ROUND(pra.revenue::decimal,2) as actual_revenue,
        ROUND(pra.margin::decimal,2) as actual_margin,
        ROUND(pra.units::decimal,2) as actual_units,
        ROUND(((pra.margin * 100) / nullif(pra.revenue,0))::decimal, 2) as actual_gm_percent
    from
        (
            SELECT
                product_id
            FROM
                {promo_schema}.promo_product
            WHERE
                promo_id = {promo_id}
        ) as pp
        left join {promo_schema}.product_master as pm on pp.product_id = pm.product_id
        left join {global_schema}.tb_latest_inventory_agg as tlia on pm.product_id = tlia.product_id
        join global.tb_currency_master as cm using (currency_id)
        left join (
            select
                product_id,
                avg(revenue) as revenue,
                avg(margin) as margin,
                avg(sales_units) as units
            from
                {promo_schema}.ps_recommended_finalized
            where
                promo_id = {promo_id}
            group by
                product_id
        ) as prf on prf.product_id = pm.product_id
        left join (
            select
                product_id,
                avg(revenue) as revenue,
                avg(margin) as margin,
                avg(sales_units) as units
            from
                {promo_schema}.ps_recommended_actuals
            where
                promo_id = {promo_id}
            group by
                product_id
        ) as pra on pra.product_id = pm.product_id;

"""

GET_STORE_DETAILS_OF_PROMO = """
    WITH
        store_selection AS (
            SELECT store_selection_type
            FROM {promo_schema}.promo_master
            WHERE promo_id = {promo_id}
        )
    SELECT
        tsm.store_id,
        tsm.store_name,
        tsm.s0_name,
        tsm.s1_name,
        tsm.s2_name,
        tsm.s3_name,
        tsm.s4_name
    FROM
        {global_schema}.tb_store_master tsm
    WHERE
        (SELECT store_selection_type FROM store_selection) = 1
        OR tsm.store_id IN (
            SELECT store_id
            FROM {promo_schema}.promo_store
            WHERE promo_id = {promo_id}
        )
"""

FETCH_PROMO_PRICE_FILE = """
    select 
        *
    from price_promo_opt.fn_step3_price_file({promo_id});
"""

FETCH_PRODUCT_COUNT_QUERY = """
    select 
        {column_name}
    from 
        (select product_level_id from price_promo.{table_name} {where_str} ) psd
    join
        price_promo.tb_discount_level_products dlp
        on psd.product_level_id = dlp.product_level_id
"""

FETCH_STORE_COUNT_QUERY = """
    select 
        {column_name}
    from 
        (select store_level_id from price_promo.{table_name} {where_str} ) psd
    join
        price_promo.tb_discount_level_stores dls
        on psd.store_level_id = dls.store_level_id
"""

FETCH_OVERALL_PRODUCT_AND_STORE_COUNT_QUERY = """
    select 
        {column_name}
    from 
        price_promo.promo_master
    where promo_id = {promo_id}
"""

FETCH_PRODUCT_AND_STORE_DISCOUNT_LEVEL_QUERY = """
    select 
        product_discount_level, 
        store_discount_level
    from 
        price_promo.ps_rules
    where promo_id = {promo_id}
"""

FETCH_SCENARIO_COUNT_FOR_GIF_DATA_QUERY = """
    with promo_timestamps as (
        select 
            coalesce(last_simulation_time, created_at) as last_simulated_timestamp
        from price_promo.promo_master 
        where promo_id = {promo_id}
    )
    select 
        max(
            case 
                when ((scenario_data->'1')::jsonb->>'updated_at')::timestamp > pt.last_simulated_timestamp
                and ((scenario_data->'2')::jsonb->>'updated_at')::timestamp > pt.last_simulated_timestamp
                then 2
                else 1
            end
        ) as scenario_count
    from price_promo.tb_user_promo_temp_bulk_edit_data_{promo_id}_{user_id}
    cross join promo_timestamps pt
"""

APPROVE_IA_SCENARIO_QUERY = """
    select
        price_promo.fn_approve_ia_scenario(
            {promo_id},
            {scenario_id},
            {user_id}
        )
"""

COPY_DISCOUNTS = """
    select 
        {promo_schema}.fn_copy_discounts(
            {promo_id},
            {source},
            {source_scenario_id},
            {target},
            {target_scenario_id},
            {selected_rows}::int[],
            {unselected_rows}::int[],
            {filters}::jsonb,
            {session_id},
            {user_id},
            {include_temporary_saved_changes}
        )
"""

CHECK_UPTO_DISCOUNT_OFFERTYPE = """
   
    WITH expanded AS (
        SELECT 
            (value->>'scenario_id')::int AS scenario_id,
            (value->>'offer_type_id')::int AS offer_type_id
        FROM price_promo.{table_name} s,
            jsonb_each(s.scenario_data) AS scenarios(key, value)
        WHERE promo_id = {promo_id} and
        not s.id = any({row_ids_to_ignore}::integer[]) 
    ),
    aggregated AS (
        SELECT 
            scenario_id,
            COUNT(*) FILTER (WHERE offer_type_id = {offer_type_id}) > 0 AS has_offertype_upto,
            COUNT(DISTINCT offer_type_id) = 1 AS all_same
        FROM expanded
        GROUP BY scenario_id
    )
    SELECT jsonb_object_agg(
        scenario_id,
        jsonb_build_object(
            'contains_upto_offer_type', has_offertype_upto,
            'all_same', all_same
        )
    ) AS result
    FROM aggregated;

"""

GET_NON_PAST_PROMOS_QUERY = """
        SELECT promo_id
        FROM price_promo.promo_master 
        WHERE promo_id = ANY({promo_ids})
        AND end_date >= date(timezone({client_timezone}, now()));
    """

GET_PROMOS_NOT_YET_STARTED_QUERY = """
    WITH config_values_cte AS (
        SELECT config_value as execution_approve_promos_buffer_days
        FROM price_promo.tb_tool_configurations
        WHERE module = '{tb_tool_module}' AND config_name = '{tb_tool_config_name}'
    )
    SELECT promo_id
    FROM price_promo.promo_master 
    CROSS JOIN config_values_cte cv
    WHERE promo_id = ANY({promo_ids})
    AND start_date >= date(timezone({client_timezone}, now())) + (COALESCE(cv.execution_approve_promos_buffer_days, '0') || ' days')::interval;
    """

IS_PROMO_PRODUCT_DISCOUNT_SKU_LEVEL = """
    select 
        case 
            when 
                (select discount_level_id from price_promo.discount_level_config where id_key = 'product_id') = any(
                    array(select product_discount_level from price_promo.ps_rules where promo_id = {promo_id})
                )
            then true
            else false
        end as is_sku_discount_level
"""


IS_PROMO_STORE_DISCOUNT_STORE_LEVEL = """
    select 
        case
            when 
                (select discount_level_id from price_promo.discount_level_config where id_key = 'store_id') = any(
                    array(select store_discount_level from price_promo.ps_rules where promo_id = {promo_id})
                )
            then true
            else false
        end as is_store_discount_level
"""

FETCH_PRIORITY_NUMBER_QUERY = """
    select 
        * 
    from 
        price_promo.fn_step2_get_priority_number(
            {promo_id},
            {product_level_ids},
            {store_level_ids}
        )
"""

FETCH_VALID_OFFER_TYPES_BY_PRIORITY_QUERY = """
    select 
        * 
    from 
        price_promo.fn_step2_get_offer_types_by_priority(
            {promo_id}, {product_level_ids}, {store_level_ids}, {priority_number})
"""

FETCH_SIMPLE_OFFERS_QUERY = """
    select 
        array_agg(promo_id) as promo_ids
    from 
        price_promo.ps_rules
    where 
        promo_id = any({promo_ids})
        and priority_number = 1;
"""

LOG_DOWNSTREAM_INTEGRATION_ACTION = """
    SELECT price_promo.fn_integration_0_create_request({action}, {promo_ids}, {user_id}) as integration_id
"""

SIMPLE_OFFERS_DOWNSTREAM_QUERY = """
    select 
        *
    from 
        price_promo.fn_integration_1_insert_data({integration_id}); 
    """

DOWNSTREAM_INTEGRATION_SELECT_QUERY = """
    SELECT 
        event_name as "event name",
        offer_name as "offer name",
        null as "strategy name",
        "price start date",
        "price end date",
        brand,
        productcode,
        brandsku,
        productprice,
        saleprice,
        currency,
        "user",
        "user mail",
        module,
        offer_type as "offer type",
        "offer value"
    FROM price_promo.tb_integration_data 
    WHERE integration_id = {integration_id};
"""

GET_SCENARIO_NAME = """
    select 
        scenario_name
    from 
        price_promo.scenario_master
    where 
        scenario_id = {scenario_id}
"""

INSERT_DOWNSTREAM_QUERY = """
    insert into global.tb_downstream_queries(query) values ({query}) returning id;
"""

GET_MIN_MAX_PRICE_VALUE = """
    select 
        *
    from 
        price_promo.fn_step3_get_max_allowed_price_value(
            {promo_id},
            {selected_rows}::int[],
            {unselected_rows}::int[],
            {filters}::jsonb,
            {user_id},
            {include_temporary_saved_changes}
        )
"""