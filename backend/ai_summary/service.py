from ai_summary.models import PromptGeneratorFactory
from ai_summary.open_ai_client import OpenAIClient


async def analyze_payload(payload1: str, payload2: str, payload3: str, summary_type: str) -> str:
    prompt_generator = PromptGeneratorFactory.create_summary(summary_type)
    prompt = prompt_generator.generate_prompt(payload1=payload1, payload2=payload2, payload3=payload3)
    open_ai_client = OpenAIClient()
    response = await open_ai_client.call_open_ai(prompt=prompt)
    return response