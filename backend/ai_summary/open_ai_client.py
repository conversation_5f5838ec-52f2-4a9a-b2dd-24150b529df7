from typing import Optional
import os
from openai import AsyncOpenAI
import json
from configuration.environment import environment


class OpenAIClient:
    _open_ai_instance = None
    
    def __new__(cls):
        if cls._open_ai_instance is None:
            cls._open_ai_instance = super().__new__(cls)
            print(f"OPENAI_API_KEY: {environment.OPENAI_API_KEY}")
            cls._open_ai_instance = AsyncOpenAI(api_key=environment.OPENAI_API_KEY)
        return cls

    @classmethod
    def get_instance(cls) -> 'OpenAIClient':
        """
        Get the singleton instance of OpenAIClient.
        Raises ValueError if client hasn't been initialized.
        """
        if cls._open_ai_instance is None:
            raise ValueError("OpenAIClient must be initialized with an API key first")
        return cls._open_ai_instance
    
    @classmethod
    async def call_open_ai(cls, prompt):
        response = await cls._open_ai_instance.chat.completions.create(
            model="gpt-4-turbo-preview",  # Using the latest GPT-4 model
            messages=[
                {"role": "system", "content": "You are a data analysis expert."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1000,
            response_format={"type": "json_object"}
        )
        return json.loads(response.choices[0].message.content)

