short_summary_prompt = """
        {payload_str}
        {payload_str1}
        {payload_str2}
        You are a promotion planning assistant. Based on three structured JSONs representing a retail promotion, scenario configurations, and their performance forecasts, generate a concise executive summary to support business scenario selection.

        ---

        📌 What to do:

        - Derive actual values from the JSONs
        - Select the best scenario using the top-priority metric in JSON 3 (e.g., `units_priority`, `revenue_priority`, etc.)
        - Exclude IA Recommended if it performs worst on that priority metric
        - Return the summary using the **exact JSON structure** below
        - The `best_scenario` field must contain structured keys

        ---

        Output Format:

        ---json
        {{
        "summary_type": "short",
        "tone": "executive",
        "presentation": {{
            "layout": "key_value",
            "show_icons": false,
            "show_scenario_labels": true
        }},
        "sections": [
            {{
            "title": "Promo Summary",
            "content": {{
                "promo_name": "<Derived from JSON 1>",
                "date_range": "<Start Date> – <End Date>",
                "best_scenario": {{
                "scenario_name": "<e.g., Scenario 1>",
                "discount_type": "<e.g., percent_off, bxgy, tiered_offer>",
                "discount_value": "<e.g., 40, Buy 1 Get 1, Tiered offer>"
                }},
                "top_metric": "<e.g., sales_units = 28,739>",
                "reason": "<Why this scenario was selected (1 line)>",
                "risk": [
                "<e.g., Negative margin –$50k>",
                "<e.g., Promo spend is high relative to lift>"
                ]
            }}
            }}
        ]
        }}

        ✅ Important Notes for the LLM:  
        The above format is a template — you must derive all values from JSON inputs
        ✅ Do not change field names or nesting  
        ✅ discount_value can be a string or a number, depending on the offer type  
        ✅ Return structured data only — not markdown, emojis, or rendered text  
        ✅ top_metric: Professional label , rounded value and Dollar Formatting wherever applicable, e.g., "Sales Units – 28,739"
        ✅ All numeric values should be rounded to the nearest integer — do not display decimal points

        🧠 Prompt: Generate BMSM Offer Description [discount_value]
        You are given structured input for a bmsm (Buy More Save More) promotion:

        offer_x_type: "unit" or "dollar"
        offer_y_type: "percent_off", "dollar_off", or "at_dollar"
        offer_x_value: number
        offer_y_value: number

        Your task is to return a clean, human-readable offer description based on the following rules:

        🎯 Rules
        If offer_x_type = "dollar":
        percent_off → "Buy $<x> Get <y>% Off"
        dollar_off → "Buy $<x> Get $<y> Off"
        If offer_x_type = "unit":
        percent_off → "Buy <x> Get <y>% Off"
        dollar_off → "Buy <x> Get $<y> Off"
        at_dollar → "Buy <x> At $<y>"

        """

medium_summary_prompt = """
        {payload_str}
        {payload_str1}
        {payload_str2}

        You are a promotion planning assistant. Based on three JSONs representing a retail promotion, its scenarios, and performance metrics, generate a structured, analyst-style summary to support comparison and scenario selection.

        ---

        📌 Instructions:

        - Use actual values from the provided JSONs
        - Rank scenarios by the top-priority field in JSON 3:
        - Priority order: `units_priority`, `revenue_priority`, `margin_priority`
        - If none are defined, fall back to: `margin`, then `gm_percent`, then `revenue`
        - Include all scenarios — including “IA Recommended” — unless it ranks last
        - Populate all fields in the provided JSON structure
        - **Do not fabricate metric values** — everything must be calculated or interpreted from the input
        - The format below is for structure reference only — actual output must reflect real data
        - top_metric: Professional label , rounded value and Dollar Formatting wherever applicable, e.g., "Sales Units – 28,739"
        - All numeric values should be rounded to the nearest integer — do not display decimal points

        ---

        📦 Output Format Specification:

        ---json
        {{
        "summary_type": "medium",
        "tone": "analyst",
        "presentation": {{
            "layout": "sectioned",
            "show_icons": true,
            "show_scenario_labels": true
        }},
        "sections": [
            {{
            "title": "Promotion Overview",
            "content": {{
                "promo_name": "<Value from JSON 1>",
                "date_range": "<Start Date> – <End Date>",
                "objective": "<Optional - describe the business intent in 1 line if derivable>"
            }}
            }},
            {{
            "title": "Scenario Comparison",
            "content": [
                {{
                "scenario_name": "Scenario 1",
                "sales_units": 28739,
                "revenue": 59934.02,
                "margin": -33222.09,
                "is_best": true
                }},
                {{
                "scenario_name": "Scenario 2",
                "sales_units": 28460,
                "revenue": 69414.29,
                "margin": -22748.70,
                "is_best": false
                }},
                {{
                "scenario_name": "IA Recommended",
                "sales_units": 29082,
                "revenue": 50752.38,
                "margin": -43732.97,
                "is_best": false
                }}
            ]
            }},
            {{
            "title": "Recommendation",
            "content": {{
                "selected_scenario": "Scenario 1",
                "reason": "Ranks highest on sales_units (units_priority = 1)",
                "top_metric": "sales_units = 28,739",
                "risk": [
                "⚠️ Negative margin of –$33K",
                "⚠️ High promo spend with low profitability"
                ]
            }}
            }}
        }}



        ✅ Important Notes for the LLM:
        The above format is a template — you must derive all values from JSON inputs


        "scenario_name" must match what's provided (e.g., "Scenario 1", "IA Recommended")


        Return "is_best": true for the top scenario only

        """

long_summary_prompt = """
        {payload_str}
        {payload_str1}
        {payload_str2}

        You are a consultative promotion planning assistant. You’ve been provided with three structured JSONs representing a retail promotion and its scenario performance forecasts.

        Your task is to extract, reason over, and compare all metrics to generate a structured long-form summary, suitable for dashboards and executive reporting.

        ---

        📌 Evaluation Logic:

        1. Join scenarios using `scenario_id` across JSON 1 and JSON 2.
        2. Use the top-priority field in JSON 3 (`units_priority`, `revenue_priority`, `margin_priority`).
        3. If no priorities are defined, fall back to: `margin`, then `gm_percent`, then `revenue`.
        4. Rank scenarios in descending order of the top-priority metric.
        5. Highlight the best-performing scenario with `"is_best": true`.
        6. top_metric: Professional label , rounded value and Dollar Formatting wherever applicable, e.g., "Sales Units – 28,739"
        7. All numeric values should be rounded to the nearest integer — do not display decimal points
        8. In summary of all scenarios, show whichever scenario is best at the top

        ---

        📦 Output Format (Structure Reference Only)

        ⚠️ The structure below is fixed — populate the values by reasoning from the actual input JSONs.

        ---json
        {{
        "summary_type": "long",
        "tone": "consultative",
        "presentation": {{
            "layout": "narrative",
            "show_icons": true,
            "show_scenario_labels": true
        }},
        "promotion_overview": [
            "<Promo name> running from <Start Date> to <End Date> aims to drive [units/revenue] through promotional pricing."
        ],
        "summary_of_all_scenarios": [
            {{
            "scenario_name": "IA Recommended",
            "sales_units": <int>,
            "revenue": <float>,
            "margin": <float>,
            "gm_percent": <float>,
            "is_best": false,
            "excluded": true
            }},
            {{
            "scenario_name": "Scenario 1",
            "sales_units": <int>,
            "revenue": <float>,
            "margin": <float>,
            "gm_percent": <float>,
            "is_best": true
            }},
            {{
            "scenario_name": "Scenario 2",
            "sales_units": <int>,
            "revenue": <float>,
            "margin": <float>,
            "gm_percent": <float>,
            "is_best": false
            }}
        ],
        "generic_comparison_insights": [
            "<Insight 1>",
            "<Insight 2>",
            "<Insight 3>"
        ],
        "tradeoff_summary": [
            "<IA vs Scenario 1 tradeoff comparison>",
            "<Guidance based on business goals (volume vs profitability)>"
        ],
        "risks": [
            "<Risk 1>",
            "<Risk 2>"
        ],
        "final_recommendation": [
            "✅ Proceed with <Best Scenario>",
            "🎯 Justification: Ranks highest on <priority metric>",
            "📉 Margin/revenue trade-off is acceptable given goals"
        ]
        }}


"""