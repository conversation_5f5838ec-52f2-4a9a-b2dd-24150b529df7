from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from openai import OpenAI
from enum import Enum
from ai_summary.prompts import short_summary_prompt, medium_summary_prompt, long_summary_prompt
from pydantic import BaseModel


class SummaryModel(BaseModel):
    summary_type: str
    json_1: Any
    json_2: Any
    json_3: Any


class SummaryPromptGenerator(ABC):
    
    @abstractmethod
    def generate_prompt(self, **kwargs):
        pass

class ShortSummaryPromptGenerator(SummaryPromptGenerator):
    
    def generate_prompt(self, **kwargs):
        prompt = short_summary_prompt.format(
            payload_str=kwargs['payload1'],
            payload_str1=kwargs['payload2'],
            payload_str2=kwargs['payload3']
        )
        return prompt
    

class MediumSummaryPromptGenerator(SummaryPromptGenerator):
    
    def generate_prompt(self, **kwargs):
        prompt = medium_summary_prompt.format(
            payload_str=kwargs['payload1'],
            payload_str1=kwargs['payload2'],
            payload_str2=kwargs['payload3']
        )
        return prompt
    

class LargeSummaryPromptGenerator(SummaryPromptGenerator):
    
    def generate_prompt(self, **kwargs):
        prompt = long_summary_prompt.format(
            payload_str=kwargs['payload1'],
            payload_str1=kwargs['payload2'],
            payload_str2=kwargs['payload3']
        )
        return prompt

class NullSummaryPromptGenerator(SummaryPromptGenerator):
    
    def generate_prompt(self, **kwargs):
        pass
    

class PromptType(Enum):
    """Enum for different types of summaries"""
    SHORT = "short"
    MEDIUM = "medium"
    LONG = "long"


class PromptGeneratorFactory:
    """Factory class for creating summary objects"""
    
    @staticmethod
    def create_summary(summary_type: str) -> Optional[SummaryPromptGenerator]:
        """
        Create and return a summary object based on the specified type
        
        Args:
            summary_type (str): Type of summary to create ('short', 'medium', 'large')
            
        Returns:
            AISummary: Instance of the specified summary class
            
        Raises:
            ValueError: If an invalid summary type is provided
        """
        summary_type = summary_type.lower()
        
        if summary_type == PromptType.SHORT.value:
            return ShortSummaryPromptGenerator()
        elif summary_type == PromptType.MEDIUM.value:
            return MediumSummaryPromptGenerator()
        elif summary_type == PromptType.LONG.value:
            return LargeSummaryPromptGenerator()
        else:
            return NullSummaryPromptGenerator()