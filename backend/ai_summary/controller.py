from fastapi import Request, status
from fastapi.routing import APIRouter
import ai_summary.models as ai_summary_models
import ai_summary.service as ai_summary_service
from pricesmart_common import utils as common_utils
ai_summary_router = APIRouter()

@ai_summary_router.post(
    path="/ai-summary"
)
async def get_ai_summary(
    request: Request, request_payload: ai_summary_models.SummaryModel
):
    """
    Get AI summary details based on request criteria.
    Args:
        request (Request): FastAPI request object
        request_payload (AISummary): AISummary json payload
    Returns:
        str: Response containing:
            summary of all json payloads
    """
    user_id = request.state.user_id
    _message = "Successful"
    _status = status.HTTP_200_OK
    ai_summary_response = await ai_summary_service.analyze_payload(
        request_payload.json_1,
        request_payload.json_2,
        request_payload.json_3,
        request_payload.summary_type
    )
    return common_utils.create_response(_message, _status, user_id, ai_summary_response)