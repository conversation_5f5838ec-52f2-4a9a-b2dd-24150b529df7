GET_ACTION_MODULE_DICT = """
    SELECT ramm.action_code, ramm.module_code  
    FROM global.role_action_module_mapping AS ramm
    WHERE ramm.role_code IN (
    SELECT am.role_code
    FROM global.user_access_hierarchy_mapping uahm
    LEFT JOIN global.acl_master am ON am.acl_code = uahm.acl_code
    WHERE uahm.user_code = {user_id} and am.application_code = {application_code}
);
"""
GET_ACTIONS_DICT = """SELECT jsonb_object_agg(action_code::text, action) AS action_json
FROM global.action_master;
"""
GET_MODULE_DICT = """
    SELECT jsonb_object_agg(module_code::text, module_name) AS mod_json
    FROM global.module_master mm where mm.application_code = {application_code} ;
    """

GET_MODULE_LIST_BY_APPN_CODE = """
    select ARRAY_AGG(module_code) as module_code from global.module_master where application_code = {application_code} ;
    """
