from fastapi import APIRouter, Request, status as http_status
from user_management.models import UserScreenActions, UserScreenActionsResponse
from user_management.service import get_user_screen_actions

user_management_router = APIRouter()

@user_management_router.post(
    path="/user-screen-actions",
    response_model=UserScreenActionsResponse,
)
async def user_screen_actions_endpoint(items: UserScreenActions, request: Request):
    """
    Get user screen actions.
    """
    user_id = items.user_id
    try:
        result = await get_user_screen_actions(user_id)
    except Exception as e:
        print("error in get_user_screen_actions", e)
        result = {}
    return UserScreenActionsResponse(
        message="Success", status=http_status.HTTP_200_OK, user_id=user_id, data=result
    ) 