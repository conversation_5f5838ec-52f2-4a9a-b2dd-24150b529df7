from pricesmart_common.utils import async_execute_query
from user_management import queries as user_management_queries
from pricesmart_common import constants as common_constants




async def get_action_module_dict(user_id: int):
    query = user_management_queries.GET_ACTION_MODULE_DICT.format(
        user_id = user_id,
        application_code = common_constants.application_code
    )
    return await async_execute_query(query)
async def get_actions_dict():
    query = user_management_queries.GET_ACTIONS_DICT
    return await async_execute_query(query)
async def get_module_dict():
    query = user_management_queries.GET_MODULE_DICT.format(
        application_code = common_constants.application_code
    )
    return await async_execute_query(query)

async def get_module_list_by_appn_code():
    query = user_management_queries.GET_MODULE_LIST_BY_APPN_CODE.format(
        application_code=common_constants.application_code
    )
    return await async_execute_query(query)