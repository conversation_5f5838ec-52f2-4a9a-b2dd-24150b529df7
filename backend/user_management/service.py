from user_management import models as models
from user_management import data as user_management_data_module

async def get_user_screen_actions(user_id):
    action_module_dict = await user_management_data_module.get_action_module_dict(
        user_id
    )
    actions_dict = await user_management_data_module.get_actions_dict()
    actions_dict = actions_dict[0]["action_json"]
    module_dict = await user_management_data_module.get_module_dict()
    module_dict = module_dict[0]["mod_json"]
    module_dict_by_appn_code = (
        await user_management_data_module.get_module_list_by_appn_code()
    )
    correct_module_code = module_dict_by_appn_code[0]["module_code"]
    response = {}
    for dictionary in action_module_dict:
        for module in dictionary["module_code"]:
            if module in correct_module_code:
                nested_dict = {}
                nested_actions = []
                for action in dictionary["action_code"]:
                    nested_actions.append(actions_dict[str(action)])
                nested_dict[module_dict[str(module)]] = nested_actions
                response.update(nested_dict)
    return response