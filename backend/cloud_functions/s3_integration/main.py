import functions_framework
from utils import (
    process_s3_integration_data,
    list_uploaded_files,
    get_file_content,
    verify_file_format,
    download_file,
    upload_file_content,
    upload_csv_data,
    upload_csv_content_direct,
    upload_text_content_direct,
    upload_file_binary
)
import asyncio
from custom_types import IntegrationRequestModel


@functions_framework.http
def s3_integration_function(request):
    """
    Cloud function for S3 integration and file operations.
    Supports: upload, list_files, get_content, verify_format, download, manual_upload, file_upload
    """
    print(f'S3 Integration function hit with method: {request.method}, content-type: {request.content_type}')
    
    # Handle multipart/form-data file uploads
    if request.content_type and 'multipart/form-data' in request.content_type:
        return handle_form_file_upload(request)
    
    # Handle JSON requests
    request_json: IntegrationRequestModel = request.get_json(silent=True)
    print('S3 Integration function hit with request:', request_json)

    try:
        # Determine operation type
        operation = request_json.get("operation", "upload")
        bucket_name = request_json.get("bucket_name")
        
        if not bucket_name:
            return {
                "status": "failed",
                "message": "bucket_name is required",
                "error": "Missing required parameter: bucket_name"
            }

        # Handle different operations
        if operation == "upload":
            return handle_upload_operation(request_json)
        elif operation == "list_files":
            return handle_list_files_operation(request_json)
        elif operation == "get_content":
            return handle_get_content_operation(request_json)
        elif operation == "verify_format":
            return handle_verify_format_operation(request_json)
        elif operation == "download":
            return handle_download_operation(request_json)
        elif operation == "manual_upload":
            return handle_manual_upload_operation(request_json)
        else:
            return {
                "status": "failed",
                "message": f"Unsupported operation: {operation}",
                "supported_operations": ["upload", "list_files", "get_content", "verify_format", "download", "manual_upload"]
            }
            
    except Exception as e:
        print('Exception in S3 integration function:', e)
        return {
            "status": "failed",
            "message": f"Error processing request: {str(e)}",
            "error": str(e),
        }


def handle_upload_operation(request_json: IntegrationRequestModel):
    """Handle automated file upload operation."""
    try:

        result = asyncio.run(process_s3_integration_data(
                request_json
            )
        )
        
        if result["status"] == "success":
            return {
                "status": "success",
                "message": f"Downstream Integration: {result['files_uploaded']} file(s) have been successfully uploaded to S3.",
                "files_uploaded": result["files_uploaded"],
                "file_names": result["file_names"]
            }
        else:
            return {
                "status": "failed",
                "message": "Downstream Integration Failed: One or more files failed to upload. Please retry or contact support.",
                "error": result.get("error", "Unknown error")
            }
            
    except KeyError as e:
        return {
            "status": "failed",
            "message": f"Missing required parameter: {str(e)}",
            "required_parameters": ["bucket_name", "file_format", "query"]
        }


def handle_list_files_operation(request_json):
    """Handle list files operation."""
    try:
        bucket_name = request_json["bucket_name"]
        date_filter = request_json.get("date_filter")
        brand_filter = request_json.get("brand_filter")
        
        result = list_uploaded_files(bucket_name, date_filter, brand_filter)
        
        return result
        
    except Exception as e:
        return {
            "status": "failed",
            "message": f"Error listing files: {str(e)}"
        }


def handle_get_content_operation(request_json):
    """Handle get file content operation."""
    try:
        bucket_name = request_json["bucket_name"]
        filename = request_json["filename"]
        max_rows = request_json.get("max_rows", 100)
        
        result = get_file_content(bucket_name, filename, max_rows)
        
        return result
        
    except KeyError as e:
        return {
            "status": "failed",
            "message": f"Missing required parameter: {str(e)}",
            "required_parameters": ["bucket_name", "filename"]
        }


def handle_verify_format_operation(request_json):
    """Handle verify file format operation."""
    try:
        bucket_name = request_json["bucket_name"]
        filename = request_json["filename"]
        
        result = verify_file_format(bucket_name, filename)
        
        return result
        
    except KeyError as e:
        return {
            "status": "failed",
            "message": f"Missing required parameter: {str(e)}",
            "required_parameters": ["bucket_name", "filename"]
        }


def handle_download_operation(request_json):
    """Handle download file operation."""
    try:
        bucket_name = request_json["bucket_name"]
        filename = request_json["filename"]
        
        result = download_file(bucket_name, filename)
        
        return result
        
    except KeyError as e:
        return {
            "status": "failed",
            "message": f"Missing required parameter: {str(e)}",
            "required_parameters": ["bucket_name", "filename"]
        }


def handle_manual_upload_operation(request_json):
    """Handle manual file upload operation."""
    try:
        bucket_name = request_json["bucket_name"]
        filename = request_json.get("filename")
        content_type = request_json.get("content_type", "application/octet-stream")
        
        # Check if CSV content is provided directly
        if "csv_content" in request_json:
            csv_content = request_json["csv_content"]
            brand_name = request_json.get("brand_name", "MANUAL")
            
            if filename:
                # Use provided filename with CSV content
                result = upload_csv_content_direct(
                    bucket_name=bucket_name,
                    filename=filename,
                    csv_content=csv_content,
                    content_type="text/csv"
                )
            else:
                # Use auto-naming
                result = upload_csv_data(
                    bucket_name=bucket_name,
                    csv_data=csv_content,
                    brand_name=brand_name
                )
        
        # Check if text content is provided directly
        elif "text_content" in request_json:
            text_content = request_json["text_content"]
            if not filename:
                return {
                    "status": "failed",
                    "message": "filename is required when using text_content",
                    "required_parameters": ["bucket_name", "filename", "text_content"]
                }
            
            result = upload_text_content_direct(
                bucket_name=bucket_name,
                filename=filename,
                text_content=text_content,
                content_type=content_type
            )
        
        # Fallback to base64 content (original method)
        elif "file_content_base64" in request_json:
            file_content_base64 = request_json["file_content_base64"]
            if not filename:
                return {
                    "status": "failed",
                    "message": "filename is required when using file_content_base64",
                    "required_parameters": ["bucket_name", "filename", "file_content_base64"]
                }
            
            result = upload_file_content(
                bucket_name=bucket_name,
                filename=filename,
                file_content_base64=file_content_base64,
                content_type=content_type
            )
        
        else:
            return {
                "status": "failed",
                "message": "One of csv_content, text_content, or file_content_base64 is required",
                "supported_content_types": {
                    "csv_content": "Direct CSV text (pipe-separated)",
                    "text_content": "Direct text content",
                    "file_content_base64": "Base64 encoded file content"
                }
            }
        
        return result
        
    except KeyError as e:
        return {
            "status": "failed",
            "message": f"Missing required parameter: {str(e)}",
            "required_parameters": ["bucket_name", "one of: csv_content/text_content/file_content_base64"]
        }


def handle_form_file_upload(request):
    """Handle file upload from multipart/form-data."""
    try:
        # Parse form data
        form = request.form
        files = request.files
        
        # Get required parameters
        bucket_name = form.get('bucket_name')
        if not bucket_name:
            return {
                "status": "failed",
                "message": "bucket_name is required in form data",
                "error": "Missing required parameter: bucket_name"
            }
        
        # Check if file is uploaded
        if 'file' not in files:
            return {
                "status": "failed",
                "message": "No file uploaded",
                "error": "File field named 'file' is required"
            }
        
        uploaded_file = files['file']
        if uploaded_file.filename == '':
            return {
                "status": "failed",
                "message": "No file selected",
                "error": "Empty filename"
            }
        
        # Get optional parameters
        custom_filename = form.get('filename')  # Optional custom filename
        brand_name = form.get('brand_name', 'MANUAL')  # For auto-naming
        use_auto_naming = form.get('use_auto_naming', 'false').lower() == 'true'
        
        # Read file content
        file_content = uploaded_file.read()
        original_filename = uploaded_file.filename
        content_type = uploaded_file.content_type or 'application/octet-stream'
        
        # Reset file pointer for potential re-reading
        uploaded_file.seek(0)
        
        # Determine upload strategy
        if use_auto_naming and content_type == 'text/csv':
            # For CSV files with auto-naming
            csv_content = file_content.decode('utf-8')
            result = upload_csv_data(
                bucket_name=bucket_name,
                csv_data=csv_content,
                brand_name=brand_name
            )
        elif custom_filename:
            # Use custom filename
            result = upload_file_binary(
                bucket_name=bucket_name,
                filename=custom_filename,
                file_content=file_content,
                content_type=content_type
            )
        else:
            # Use original filename
            result = upload_file_binary(
                bucket_name=bucket_name,
                filename=original_filename,
                file_content=file_content,
                content_type=content_type
            )
        
        return result
        
    except Exception as e:
        print(f'Exception in form file upload: {e}')
        return {
            "status": "failed",
            "message": f"Error processing file upload: {str(e)}",
            "error": str(e)
        } 