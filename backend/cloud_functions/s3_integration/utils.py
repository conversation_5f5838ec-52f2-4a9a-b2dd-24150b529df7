import json
from google.cloud import secretmanager
import asyncpg
import boto3
from json import loads
from settings import settings
from constants import SPECIAL_CHARACTER_MAPPING, CONTENT_TYPE_MAPPING, DEFAULT_CONTENT_TYPE
from typing import Dict, Optional, List, Any
import datetime
import base64
import pandas as pd
from io import String<PERSON>
from google.cloud import storage
from custom_types import IntegrationRequestModel


def get_secret_data():
    """Get secret data from Google Secret Manager."""
    client_secret_manager = secretmanager.SecretManagerServiceClient()
    SECRET_NAME = f"projects/{settings.SECRET_PROJECT}/secrets/{settings.SECRET_ID}/versions/latest"
    response = client_secret_manager.access_secret_version(name=SECRET_NAME)
    return loads(response.payload.data.decode("UTF-8"))


SECRET_DATA = get_secret_data()


class DbConnection:
    """Database connection context manager."""
    
    async def __aenter__(self):
        self.conn = await asyncpg.connect(
            user=SECRET_DATA["db_user"],
            password=SECRET_DATA["db_password"],
            host=SECRET_DATA["db_host"],
            port=SECRET_DATA["db_port"],
            database=SECRET_DATA["db_name"]
        )
        return self.conn

    async def __aexit__(self, exc_type, exc_value, exc_tb):
        await self.conn.close()


class S3Connection:
    """Singleton S3 connection class."""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(S3Connection, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=SECRET_DATA["aws_access_key_id"],
                aws_secret_access_key=SECRET_DATA["aws_secret_access_key"],
                region_name=settings.S3_REGION
            )
            self._initialized = True
    
    def get_client(self):
        """Get S3 client instance."""
        return self.s3_client


# Data processing functions
async def process_s3_integration_data(
    request_json: IntegrationRequestModel
) -> Dict[str, Any]:
    """
    Main function to process S3 integration data.
    
    Args:
        bucket_name: S3 bucket name
        file_format: File format ('csv', etc.)
        query_id: Query id to fetch offer data
        folder_name: Folder name to upload the file to
    Returns:
        Dictionary containing processing results
    """
    try:
        await update_log(
            request_json,
            "in_progress",
            [],
            "Processing"
        )

        from models import FileFormatterFactory
        
        print(f"Starting S3 integration process with bucket: {request_json['bucket_name']}, format: {request_json['file_format']}")

        query = await execute_query(f"select query from global.tb_downstream_queries where id = {request_json['query_id']}")
        query = query[0]["query"]
        
        # Execute query to get offer data
        data = await execute_query(query)
        print(f"Retrieved {len(data)} records from database")
        
        if not data:
            await update_log(
                request_json,
                status="failed",
                file_names=[],
                message = "No data found to process"
            )
            return {
                "status": "success",
                "message": "No data found to process",
                "files_uploaded": 0,
                "file_names": []
            }
        
        # Group data by brand
        brand_groups = group_data_by_brand(data)
        print(f"Grouped data into {len(brand_groups)} brands: {list(brand_groups.keys())}")
        
        # Process and upload files for each brand
        upload_results = []
        successful_uploads = 0
        failed_uploads = 0
        error_messages = {}
        for brand, brand_data in brand_groups.items():
            print(f"Processing brand: {brand} with {len(brand_data)} records")
            
            # Create formatter using factory
            formatter = FileFormatterFactory.create_formatter(request_json['file_format'], brand, brand_data)
            
            # Process and upload
            result = formatter.process_and_upload(request_json['bucket_name'], request_json['folder_name'])
            upload_results.append(result)
            
            if result["success"]:
                successful_uploads += 1
                print(f"Successfully uploaded file for brand: {brand}")
            else:
                failed_uploads += 1
                error_messages[brand] = result["error"]
                print(f"Failed to upload file for brand: {brand} - {result.get('error', 'Unknown error')}")
        
        # Log execution details to database
        await update_log(
            request_json,
            status="completed" if failed_uploads == 0 else "failed",
            file_names=[result["filename"] for result in upload_results if result["success"]],
            message = (
                "\n".join(f"{brand}: {error_messages[brand]}" for brand in error_messages)
                if error_messages 
                else "Success"
            )
        )
        
        # Prepare response
        if failed_uploads == 0:
            return {
                "status": "success",
                "files_uploaded": successful_uploads,
                "file_names": [result["filename"] for result in upload_results if result["success"]],
                "upload_details": upload_results
            }
        else:
            return {
                "status": "partial_failure" if successful_uploads > 0 else "failed",
                "files_uploaded": successful_uploads,
                "file_names": [result["filename"] for result in upload_results if result["success"]],
                "upload_details": upload_results,
                "error": f"{failed_uploads} out of {len(brand_groups)} file uploads failed"
            }
            
    except Exception as e:
        print(f"Error in S3 integration process: {e}")

        await update_log(
            request_json,
            "failed",
            [],
            repr(e)
        )
        
        # Log failure to database
        # await log_execution_details(
        #     query=query,
        #     total_records=0,
        #     priority_one_records=0,
        #     brands_processed=0,
        #     successful_uploads=0,
        #     failed_uploads=1,
        #     upload_results=[],
        #     error=str(e)
        # )
        
        return {
            "status": "failed",
            "files_uploaded": 0,
            "file_names": [],
            "error": str(e)
        }
    
async def update_log(
    request_json: IntegrationRequestModel,
    status: str,
    file_names: List[str],
    message: str
) -> None:
    
    await execute_query(
        """
            update price_promo.tb_integration_logs
            set file_names_uploaded = {file_names},
                status = {status},
                message = {message},
                request_payload = {request_json}
            where integration_id = {integration_id}
        """.format(
            integration_id = get_str_repr(request_json["integration_id"]),
            status=get_str_repr(status),
            file_names=get_str_repr(",".join(file_names)),
            message=get_str_repr(message),
            request_json=get_str_repr(request_json)
        )
    )

def get_str_repr(val: Any) -> str:
    val = escape_quotes(val)
    if val is None:
        return "null"
    elif isinstance(val, bool):
        return str(val).lower()
    elif isinstance(val, str):
        return f"'{val.strip()}'"
    elif isinstance(val, list) or isinstance(val, set) or isinstance(val, tuple):
        return "(" + ",".join(get_str_repr(v) for v in val) + ")"
    elif isinstance(val, datetime.datetime) or isinstance(val, datetime.date):
        return f"'{str(val)}'"
    elif isinstance(val, dict):
        return f"'{json.dumps(val, default=str)}'"
    else:
        return str(val)


def escape_quotes(value):
    if not value:
        return value
    if isinstance(value, list):
        mod_value = []
        for val in value:
            if val or val == 0:
                mod_value.append(
                    val.replace("'", "''") if isinstance(val, str) else val
                )
            else:
                mod_value.append(None)
        return mod_value
    elif isinstance(value, str):
        return value.replace("'", "''")
    
    return value




async def execute_query(query: str) -> List[Dict]:
    """
    Execute the provided SQL query and return results.
    
    Args:
        query: SQL query to execute
        
    Returns:
        List of dictionaries containing query results
    """
    try:
        async with DbConnection() as conn:
            results = await conn.fetch(query)
            return [dict(record) for record in results]
    except Exception as e:
        print(f"Error executing query: {e}")
        raise


def backup_to_gcs(data: List[Dict], execution_id: str) -> None:
    """Backup processed data to Google Cloud Storage for audit purposes."""
    try:
        df = pd.DataFrame(data)
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, index=False)
        csv_data = csv_buffer.getvalue()
        
        client = storage.Client()
        bucket = client.bucket(settings.S3_BUCKET_NAME)
        
        backup_blob_name = f"s3_integration_backup/execution_{execution_id}_{datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%d_%H%M%S')}.csv"
        blob = bucket.blob(backup_blob_name)
        blob.upload_from_string(csv_data, content_type="text/csv")
        
        print(f"Data backed up to GCS: {backup_blob_name}")
        
    except Exception as e:
        print(f"Error backing up data to GCS: {e}")


# Manual upload functions
def upload_file_content(
    bucket_name: str, 
    filename: str, 
    file_content_base64: str,
    content_type: str = "application/octet-stream",
    custom_folder: str = None
) -> Dict[str, any]:
    """Upload file content to S3 from base64 encoded data."""
    try:
        s3_connection = S3Connection()
        max_file_size_mb = 100
        
        print(f"📤 Starting manual upload: {filename} to bucket {bucket_name}")
        
        # Decode base64 content
        try:
            file_content = base64.b64decode(file_content_base64)
        except Exception as e:
            return {
                "status": "failed",
                "message": "Invalid base64 content",
                "error": f"Base64 decode error: {str(e)}"
            }
        
        # Validate file size
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > max_file_size_mb:
            return {
                "status": "failed",
                "message": f"File too large: {file_size_mb:.2f} MB (max: {max_file_size_mb} MB)",
                "file_size_mb": file_size_mb,
                "max_size_mb": max_file_size_mb
            }
        
        # Auto-detect content type if not provided or generic
        if content_type == "application/octet-stream":
            content_type = detect_content_type(filename)
        
        # Determine S3 key path
        folder_path = custom_folder if custom_folder else settings.DOWNSTREAM_FOLDER
        s3_key = f"{folder_path}/{filename}"
        
        # Upload to S3
        s3_client = s3_connection.get_client()
        
        upload_metadata = {
            'UploadedBy': 'ManualUpload',
            'UploadTimestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'OriginalFilename': filename,
            'FileSize': str(len(file_content))
        }
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=content_type,
            Metadata=upload_metadata
        )
        
        print(f"✅ Successfully uploaded {filename} to S3")
        
        return {
            "status": "success",
            "message": f"File '{filename}' successfully uploaded to S3",
            "filename": filename,
            "bucket_name": bucket_name,
            "s3_key": s3_key,
            "file_size_bytes": len(file_content),
            "file_size_mb": round(file_size_mb, 3),
            "content_type": content_type,
            "upload_timestamp": upload_metadata['UploadTimestamp'],
            "s3_url": f"s3://{bucket_name}/{s3_key}"
        }
        
    except Exception as e:
        print(f"❌ Error uploading file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error uploading file: {str(e)}",
            "filename": filename,
            "error": str(e)
        }


def upload_csv_data(bucket_name: str, csv_data: str, brand_name: str = "MANUAL") -> Dict[str, any]:
    """Upload CSV data directly (pipe-separated format)."""
    try:
        # Validate CSV format (check for pipe separator)
        if '|' not in csv_data:
            return {
                "status": "failed",
                "message": "CSV data must be pipe-separated (|)",
                "recommendation": "Convert your CSV to use pipe (|) as separator"
            }
        
        # Encode CSV data to base64
        csv_bytes = csv_data.encode('utf-8')
        csv_base64 = base64.b64encode(csv_bytes).decode('utf-8')
        
        # Generate filename with timestamp
        timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%dT%H%M%SZ')
        filename = f"{brand_name.upper()}-{timestamp}.csv"
        
        # Upload with auto-naming
        return upload_file_content(
            bucket_name=bucket_name,
            filename=filename,
            file_content_base64=csv_base64,
            content_type="text/csv"
        )
        
    except Exception as e:
        return {
            "status": "failed",
            "message": f"Error uploading CSV data: {str(e)}",
            "error": str(e)
        }


def upload_csv_content_direct(bucket_name: str, filename: str, csv_content: str, content_type: str = "text/csv") -> Dict[str, any]:
    """Upload CSV content directly without base64 encoding."""
    try:
        s3_connection = S3Connection()
        max_file_size_mb = 100
        
        print(f"📤 Starting direct CSV upload: {filename} to bucket {bucket_name}")
        
        # Validate CSV format (check for pipe separator)
        if '|' not in csv_content:
            return {
                "status": "failed",
                "message": "CSV data must be pipe-separated (|)",
                "recommendation": "Convert your CSV to use pipe (|) as separator"
            }
        
        # Validate file size
        file_content = csv_content.encode('utf-8')
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > max_file_size_mb:
            return {
                "status": "failed",
                "message": f"File too large: {file_size_mb:.2f} MB (max: {max_file_size_mb} MB)",
                "file_size_mb": file_size_mb,
                "max_size_mb": max_file_size_mb
            }
        
        # Determine S3 key path
        s3_key = f"{settings.DOWNSTREAM_FOLDER}/{filename}"
        
        # Upload to S3
        s3_client = s3_connection.get_client()
        
        upload_metadata = {
            'UploadedBy': 'ManualUpload-DirectCSV',
            'UploadTimestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'OriginalFilename': filename,
            'FileSize': str(len(file_content))
        }
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=content_type,
            Metadata=upload_metadata
        )
        
        print(f"✅ Successfully uploaded {filename} to S3")
        
        return {
            "status": "success",
            "message": f"CSV file '{filename}' successfully uploaded to S3",
            "filename": filename,
            "bucket_name": bucket_name,
            "s3_key": s3_key,
            "file_size_bytes": len(file_content),
            "file_size_mb": round(file_size_mb, 3),
            "content_type": content_type,
            "upload_timestamp": upload_metadata['UploadTimestamp'],
            "s3_url": f"s3://{bucket_name}/{s3_key}"
        }
        
    except Exception as e:
        print(f"❌ Error uploading CSV file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error uploading CSV file: {str(e)}",
            "filename": filename,
            "error": str(e)
        }


def upload_text_content_direct(bucket_name: str, filename: str, text_content: str, content_type: str = "text/plain") -> Dict[str, any]:
    """Upload text content directly without base64 encoding."""
    try:
        s3_connection = S3Connection()
        max_file_size_mb = 100
        
        print(f"📤 Starting direct text upload: {filename} to bucket {bucket_name}")
        
        # Validate file size
        file_content = text_content.encode('utf-8')
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > max_file_size_mb:
            return {
                "status": "failed",
                "message": f"File too large: {file_size_mb:.2f} MB (max: {max_file_size_mb} MB)",
                "file_size_mb": file_size_mb,
                "max_size_mb": max_file_size_mb
            }
        
        # Auto-detect content type if not provided or generic
        if content_type == "application/octet-stream":
            content_type = detect_content_type(filename)
        
        # Determine S3 key path
        s3_key = f"{settings.DOWNSTREAM_FOLDER}/{filename}"
        
        # Upload to S3
        s3_client = s3_connection.get_client()
        
        upload_metadata = {
            'UploadedBy': 'ManualUpload-DirectText',
            'UploadTimestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'OriginalFilename': filename,
            'FileSize': str(len(file_content))
        }
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=content_type,
            Metadata=upload_metadata
        )
        
        print(f"✅ Successfully uploaded {filename} to S3")
        
        return {
            "status": "success",
            "message": f"Text file '{filename}' successfully uploaded to S3",
            "filename": filename,
            "bucket_name": bucket_name,
            "s3_key": s3_key,
            "file_size_bytes": len(file_content),
            "file_size_mb": round(file_size_mb, 3),
            "content_type": content_type,
            "upload_timestamp": upload_metadata['UploadTimestamp'],
            "s3_url": f"s3://{bucket_name}/{s3_key}"
        }
        
    except Exception as e:
        print(f"❌ Error uploading text file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error uploading text file: {str(e)}",
            "filename": filename,
            "error": str(e)
        }


def upload_file_binary(bucket_name: str, filename: str, file_content: bytes, content_type: str = "application/octet-stream") -> Dict[str, any]:
    """Upload binary file content directly to S3."""
    try:
        s3_connection = S3Connection()
        max_file_size_mb = 100
        
        print(f"📤 Starting binary file upload: {filename} to bucket {bucket_name}")
        
        # Validate file size
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > max_file_size_mb:
            return {
                "status": "failed",
                "message": f"File too large: {file_size_mb:.2f} MB (max: {max_file_size_mb} MB)",
                "file_size_mb": file_size_mb,
                "max_size_mb": max_file_size_mb
            }
        
        # Auto-detect content type if not provided or generic
        if content_type == "application/octet-stream":
            content_type = detect_content_type(filename)
        
        # Determine S3 key path
        s3_key = f"{settings.DOWNSTREAM_FOLDER}/{filename}"
        
        # Upload to S3
        s3_client = s3_connection.get_client()
        
        upload_metadata = {
            'UploadedBy': 'ManualUpload-Binary',
            'UploadTimestamp': datetime.datetime.now(datetime.timezone.utc).isoformat(),
            'OriginalFilename': filename,
            'FileSize': str(len(file_content))
        }
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=content_type,
            Metadata=upload_metadata
        )
        
        print(f"✅ Successfully uploaded {filename} to S3")
        
        return {
            "status": "success",
            "message": f"Binary file '{filename}' successfully uploaded to S3",
            "filename": filename,
            "bucket_name": bucket_name,
            "s3_key": s3_key,
            "file_size_bytes": len(file_content),
            "file_size_mb": round(file_size_mb, 3),
            "content_type": content_type,
            "upload_timestamp": upload_metadata['UploadTimestamp'],
            "s3_url": f"s3://{bucket_name}/{s3_key}"
        }
        
    except Exception as e:
        print(f"❌ Error uploading binary file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error uploading binary file: {str(e)}",
            "filename": filename,
            "error": str(e)
        }


# File verification functions
def list_uploaded_files(bucket_name: str, date_filter: str = None, brand_filter: str = None) -> Dict[str, any]:
    """List files uploaded to S3 bucket with optional filters."""
    try:
        s3_connection = S3Connection()
        s3_client = s3_connection.get_client()
        
        # List all objects in the downstream folder
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=f"{settings.DOWNSTREAM_FOLDER}/"
        )
        
        if 'Contents' not in response:
            return {
                "status": "success",
                "message": "No files found in the bucket",
                "files": [],
                "total_files": 0
            }
        
        files = []
        for obj in response['Contents']:
            file_key = obj['Key']
            filename = file_key.split('/')[-1]  # Get filename from full path
            
            # Skip if it's just the folder
            if filename == '':
                continue
            
            # Apply date filter if provided
            if date_filter and not matches_date_filter(filename, date_filter):
                continue
            
            # Apply brand filter if provided
            if brand_filter and not matches_brand_filter(filename, brand_filter):
                continue
            
            file_info = {
                "filename": filename,
                "full_path": file_key,
                "size_bytes": obj['Size'],
                "size_mb": round(obj['Size'] / (1024 * 1024), 2),
                "last_modified": obj['LastModified'].isoformat(),
                "upload_date": obj['LastModified'].strftime('%Y-%m-%d'),
                "upload_time": obj['LastModified'].strftime('%H:%M:%S UTC')
            }
            
            # Extract additional info from filename if possible
            file_info.update(parse_filename_info(filename))
            
            files.append(file_info)
        
        # Sort files by last modified (newest first)
        files.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return {
            "status": "success",
            "message": f"Found {len(files)} files",
            "files": files,
            "total_files": len(files),
            "bucket_name": bucket_name,
            "folder_path": settings.DOWNSTREAM_FOLDER
        }
        
    except Exception as e:
        print(f"Error listing S3 files: {e}")
        return {
            "status": "failed",
            "message": f"Error listing files: {str(e)}",
            "files": [],
            "total_files": 0
        }


def get_file_content(bucket_name: str, filename: str, max_rows: int = 100) -> Dict[str, any]:
    """Get the content of a specific file for verification."""
    try:
        s3_connection = S3Connection()
        s3_client = s3_connection.get_client()
        
        # Construct the full S3 key
        s3_key = f"{settings.DOWNSTREAM_FOLDER}/{filename}"
        
        # Get the file content
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        file_content = response['Body'].read().decode('utf-8')
        
        # Parse CSV content
        if filename.lower().endswith('.csv'):
            content_analysis = analyze_csv_content(file_content, max_rows)
        else:
            content_analysis = {
                "file_type": "non-csv",
                "raw_content": file_content[:1000] + "..." if len(file_content) > 1000 else file_content,
                "content_length": len(file_content)
            }
        
        return {
            "status": "success",
            "filename": filename,
            "bucket_name": bucket_name,
            "file_size": response['ContentLength'],
            "content_type": response.get('ContentType', 'unknown'),
            "last_modified": response['LastModified'].isoformat(),
            "content_analysis": content_analysis
        }
        
    except Exception as e:
        print(f"Error reading file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error reading file: {str(e)}",
            "filename": filename
        }


def verify_file_format(bucket_name: str, filename: str) -> Dict[str, any]:
    """Verify if the file format matches expected pipe-separated CSV format."""
    try:
        file_content_result = get_file_content(bucket_name, filename, max_rows=50)
        
        if file_content_result["status"] != "success":
            return file_content_result
        
        content_analysis = file_content_result["content_analysis"]
        
        verification_results = {
            "filename": filename,
            "format_valid": True,
            "issues": [],
            "recommendations": []
        }
        
        if filename.lower().endswith('.csv'):
            # Check if it's pipe-separated
            if content_analysis.get("separator") != "|":
                verification_results["format_valid"] = False
                verification_results["issues"].append(f"Expected pipe (|) separator, found '{content_analysis.get('separator', 'unknown')}'")
            
            # Check for minimum expected columns
            if content_analysis.get("column_count", 0) < 5:
                verification_results["format_valid"] = False
                verification_results["issues"].append(f"Too few columns: {content_analysis.get('column_count', 0)} (expected at least 5)")
            
            # Check for data rows
            if content_analysis.get("data_rows", 0) == 0:
                verification_results["issues"].append("No data rows found (only headers)")
            
            # Add recommendations
            if verification_results["format_valid"]:
                verification_results["recommendations"].append("File format appears correct")
            else:
                verification_results["recommendations"].append("Please check file generation logic")
        
        return {
            "status": "success",
            "verification": verification_results,
            "content_summary": content_analysis
        }
        
    except Exception as e:
        return {
            "status": "failed",
            "message": f"Error verifying file format: {str(e)}",
            "filename": filename
        }


def download_file(bucket_name: str, filename: str) -> Dict[str, any]:
    """Download a file from S3 and return its content for local saving."""
    try:
        s3_connection = S3Connection()
        s3_client = s3_connection.get_client()
        
        # Construct the full S3 key
        s3_key = f"{settings.DOWNSTREAM_FOLDER}/{filename}"
        
        # Get the file content
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        file_content = response['Body'].read()
        
        # Encode content as base64 for JSON response
        encoded_content = base64.b64encode(file_content).decode('utf-8')
        
        return {
            "status": "success",
            "filename": filename,
            "bucket_name": bucket_name,
            "file_size": response['ContentLength'],
            "content_type": response.get('ContentType', 'unknown'),
            "last_modified": response['LastModified'].isoformat(),
            "file_content_base64": encoded_content,
            "download_instructions": {
                "step1": "Copy the 'file_content_base64' value",
                "step2": "Decode using: echo 'base64_content' | base64 --decode > filename",
                "step3": "Or use the provided curl command below"
            }
        }
        
    except Exception as e:
        print(f"Error downloading file {filename}: {e}")
        return {
            "status": "failed",
            "message": f"Error downloading file: {str(e)}",
            "filename": filename
        }


# Helper functions
def detect_content_type(filename: str) -> str:
    """Detect content type based on file extension."""
    extension = filename.split('.')[-1].lower() if '.' in filename else ''
    return CONTENT_TYPE_MAPPING.get(extension, DEFAULT_CONTENT_TYPE)


def matches_date_filter(filename: str, date_filter: str) -> bool:
    """Check if filename matches date filter."""
    try:
        # Extract date from filename (format: BRAND-YYYYMMDDTHHMMSSZ.ext)
        if '-' in filename and 'T' in filename:
            date_part = filename.split('-')[1].split('T')[0]  # Extract YYYYMMDD
            file_date = datetime.datetime.strptime(date_part, '%Y%m%d').strftime('%Y-%m-%d')
            return file_date == date_filter
    except:
        pass
    return False


def matches_brand_filter(filename: str, brand_filter: str) -> bool:
    """Check if filename matches brand filter."""
    try:
        # Extract brand from filename (format: BRAND-YYYYMMDDTHHMMSSZ.ext)
        if '-' in filename:
            brand_part = filename.split('-')[0]
            return brand_filter.upper() in brand_part.upper()
    except:
        pass
    return False


def parse_filename_info(filename: str) -> Dict[str, str]:
    """Parse additional information from filename."""
    info = {}
    try:
        if '-' in filename and 'T' in filename:
            parts = filename.split('-')
            if len(parts) >= 2:
                info["brand"] = parts[0]
                
                # Extract timestamp
                timestamp_part = parts[1].split('.')[0]  # Remove file extension
                if 'T' in timestamp_part and 'Z' in timestamp_part:
                    # Parse timestamp: YYYYMMDDTHHMMSSZ
                    timestamp_str = timestamp_part.replace('Z', '')
                    parsed_time = datetime.datetime.strptime(timestamp_str, '%Y%m%dT%H%M%S')
                    info["generated_date"] = parsed_time.strftime('%Y-%m-%d')
                    info["generated_time"] = parsed_time.strftime('%H:%M:%S UTC')
    except:
        pass
    
    return info


def analyze_csv_content(content: str, max_rows: int) -> Dict[str, any]:
    """Analyze CSV content and return summary."""
    try:
        lines = content.strip().split('\n')
        
        if not lines:
            return {"error": "Empty file"}
        
        # Detect separator
        first_line = lines[0]
        separators = [',', ';', '\t', '|']
        separator_counts = {sep: first_line.count(sep) for sep in separators}
        detected_separator = max(separator_counts.items(), key=lambda x: x[1])[0]
        
        # Parse with pandas
        csv_buffer = StringIO(content)
        df = pd.read_csv(csv_buffer, sep=detected_separator, nrows=max_rows)
        
        analysis = {
            "separator": detected_separator,
            "total_lines": len(lines),
            "column_count": len(df.columns),
            "data_rows": len(df),
            "columns": list(df.columns),
            "sample_data": df.head(min(5, len(df))).to_dict('records') if len(df) > 0 else [],
            "data_types": df.dtypes.astype(str).to_dict()
        }
        
        return analysis
        
    except Exception as e:
        return {
            "error": f"Failed to analyze CSV: {str(e)}",
            "raw_preview": content[:500] + "..." if len(content) > 500 else content
        }


# Original utility functions
async def validate_priority_one_offers(data: list) -> list:
    """
    Validate that only Priority = 1 offers are processed.
    Fetches priority_number from price_promo.ps_rules table.
    """
    if not data:
        return []
    
    # Extract promo_ids from the records
    promo_ids = [record.get('promo_id') for record in data if record.get('promo_id')]
    
    if not promo_ids:
        print("No promo_ids found in records")
        return []
    
    print(f"Checking priority for promo_ids: {promo_ids}")
    
    # Query to get priority_number for the promo_ids
    priority_query = """
        SELECT promo_id, priority_number 
        FROM price_promo.ps_rules 
        WHERE promo_id = ANY($1)
    """
    
    try:
        async with DbConnection() as conn:
            priority_results = await conn.fetch(priority_query, promo_ids)
            
        # Create a mapping of promo_id to priority_number
        priority_mapping = {row['promo_id']: row['priority_number'] for row in priority_results}
        print(f"Priority mapping: {priority_mapping}")
        
        # Filter records that have priority_number = 1
        filtered_records = []
        for record in data:
            promo_id = record.get('promo_id')
            priority = priority_mapping.get(promo_id)
            
            if priority == 1:
                filtered_records.append(record)
                print(f"Including promo_id {promo_id} with priority {priority}")
            else:
                print(f"Excluding promo_id {promo_id} with priority {priority}")
        
        print(f"Filtered {len(filtered_records)} records out of {len(data)} total records")
        return filtered_records
        
    except Exception as e:
        print(f"Error validating priority one offers: {e}")
        # Return empty list if query fails to be safe
        return []


def group_data_by_brand(data: list) -> Dict[str, list]:
    """
    Group offer data by brand.
    
    Args:
        data: List of offer records
        
    Returns:
        Dictionary with brand as key and list of records as value
    """
    brand_groups = {}
    for record in data:
        brand = record.get('brand', 'Unknown')
        if brand not in brand_groups:
            brand_groups[brand] = []
        brand_groups[brand].append(record)
    
    return brand_groups


def replace_imputed_characters(value: str) -> str:
    """Replace special characters that were imputed during data processing."""
    for special_char, replacement in SPECIAL_CHARACTER_MAPPING.items():
        value = value.replace(special_char, replacement)
    return value


def impute_special_characters(data: Dict) -> Dict:
    """Apply special character imputation to data dictionary."""
    new_data = {}
    for key, value in data.items():
        new_key = replace_imputed_characters(key) if isinstance(key, str) else key
        new_value = replace_imputed_characters(value) if isinstance(value, str) else value
        new_data[new_key] = new_value
    return new_data 