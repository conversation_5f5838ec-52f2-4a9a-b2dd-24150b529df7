from pydantic import BaseModel
import configparser
import os


class Settings(BaseModel):
    SERVICE_ACCOUNT_EMAIL: str
    SECRET_PROJECT: str
    SECRET_ID: str
    DOWNSTREAM_FOLDER: str
    S3_BUCKET_NAME: str
    S3_REGION: str


config = configparser.ConfigParser()
config.optionxform = str # type: ignore

config.read("environment.ini")

environment = os.getenv("ENV") or "local"
print(f"Environment: {environment}")
config = {**config[environment]}
settings = Settings(**config) 