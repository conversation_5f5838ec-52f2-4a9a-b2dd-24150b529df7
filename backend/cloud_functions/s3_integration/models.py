from abc import ABC, abstractmethod
import datetime
import pandas as pd
from io import String<PERSON>
from typing import List, Dict
from utils import S3Connection, impute_special_characters, detect_content_type
from settings import settings



class FileFormatter(ABC):
    """Abstract base class for file formatters."""
    
    def __init__(self, brand: str, data: List[Dict]):
        self.brand = brand
        self.data = data
        self.s3_connection = S3Connection()
    
    @abstractmethod
    def format_data(self) -> str:
        """Format data according to specific file format."""
        pass
    
    @abstractmethod
    def generate_filename(self) -> str:
        """Generate filename according to naming convention."""
        pass
    
    def get_content_type(self) -> str:
        """
        Get content type based on file extension.
        
        Returns:
            Appropriate MIME type for the file format
        """
        filename = self.generate_filename()
        return detect_content_type(filename)
    
    def upload_to_s3(self, bucket_name: str, formatted_data: str, content_type: str = 'text/plain', folder_name: str = None) -> bool:
        """
        Upload formatted data to S3 bucket.
        
        Args:
            bucket_name: S3 bucket name
            formatted_data: Formatted data to upload
            content_type: Content type for the file
            
        Returns:
            True if upload successful, False otherwise
        """
        try:
            filename = self.generate_filename()
            s3_key = f"{settings.DOWNSTREAM_FOLDER}{folder_name}/{filename}"
            
            s3_client = self.s3_connection.get_client()
            
            # Upload the file
            s3_client.put_object(
                Bucket=bucket_name,
                Key=s3_key,
                Body=formatted_data,
                ContentType=content_type
            )
            
            print(f"Successfully uploaded {filename} to S3 bucket {bucket_name}")
            return True
            
        except Exception as e:
            print(f"Error uploading to S3: {e}")
            return False
    
    def process_and_upload(self, bucket_name: str, folder_name: str) -> Dict[str, any]:
        """
        Process data, format it, and upload to S3.
        
        Args:
            bucket_name: S3 bucket name
            
        Returns:
            Dictionary containing upload status and metadata
        """
        try:
            # Format the data
            formatted_data = self.format_data()
            
            # Generate filename
            filename = self.generate_filename()
            
            # Upload to S3 with appropriate content type
            content_type = self.get_content_type()
            success = self.upload_to_s3(bucket_name, formatted_data, content_type, folder_name)
            
            return {
                "success": success,
                "filename": filename,
                "brand": self.brand,
                "record_count": len(self.data)
            }
            
        except Exception as e:
            print(f"Error processing and uploading data for brand {self.brand}: {e}")
            return {
                "success": False,
                "filename": None,
                "brand": self.brand,
                "record_count": len(self.data),
                "error": repr(e)
            }


class CSVFormatter(FileFormatter):
    """CSV file formatter with pipe separator."""
    
    def format_data(self) -> str:
        """
        Format data as CSV with pipe separator.
        
        Returns:
            Formatted CSV string
        """
        # Clean data by applying special character imputation
        cleaned_data = [impute_special_characters(dict(record)) for record in self.data]
        
        # Convert to DataFrame
        df = pd.DataFrame(cleaned_data)
        
        # Convert DataFrame to CSV with pipe separator
        csv_buffer = StringIO()
        df.to_csv(csv_buffer, sep='|', index=False, header=True)
        
        return csv_buffer.getvalue()
    
    def generate_filename(self) -> str:
        """
        Generate filename with format: <Geo>-<Timestamp in UTC>.csv
        Note: Using brand as Geo for now, can be modified based on actual geo field
        
        Returns:
            Generated filename
        """
        timestamp = datetime.datetime.now(datetime.timezone.utc).strftime('%Y%m%dT%H%M%SZ')
        # Using brand as Geo - this can be modified if there's a specific geo field
        geo = self.brand.upper().replace(' ', '_')
        return f"{geo}-{timestamp}.csv"


class FileFormatterFactory:
    """Factory class to create file formatters based on format type."""
    
    @staticmethod
    def create_formatter(file_format: str, brand: str, data: List[Dict]) -> FileFormatter:
        """
        Create a file formatter based on the specified format.
        
        Args:
            file_format: Format type ('csv', etc.)
            brand: Brand name
            data: Data to format
            
        Returns:
            FileFormatter instance
            
        Raises:
            ValueError: If unsupported file format is specified
        """
        if file_format.lower() == 'csv':
            return CSVFormatter(brand, data)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")
    
    @staticmethod
    def get_supported_formats() -> List[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List of supported format strings
        """
        return ['csv'] 