# Special character mapping for data imputation
SPECIAL_CHARACTER_MAPPING = {
    "__ia_char_01": "'", "__ia_char_02": '"', "__ia_char_03": "/", "__ia_char_04": "\\",
    "__ia_char_05": "`", "__ia_char_06": "~", "__ia_char_07": "!", "__ia_char_08": "@",
    "__ia_char_09": "#", "__ia_char_10": "$", "__ia_char_11": "%", "__ia_char_12": "^",
    "__ia_char_13": "&", "__ia_char_14": "*", "__ia_char_15": "(", "__ia_char_16": ")",
    "__ia_char_19": "=", "__ia_char_20": "+", "__ia_char_21": "{", "__ia_char_22": "}",
    "__ia_char_23": "[", "__ia_char_24": "]", "__ia_char_25": "|", "__ia_char_26": ":",
    "__ia_char_27": ";", "__ia_char_28": "<", "__ia_char_29": ">", "__ia_char_30": ",",
    "__ia_char_31": ".", "__ia_char_32": "?"
}

# File format constants
SUPPORTED_FILE_FORMATS = ['csv']

# Default values
DEFAULT_FILE_FORMAT = 'csv'
DEFAULT_REGION = 'us-east-1'

# Content type mapping for different file extensions (AWS S3 standard MIME types)
CONTENT_TYPE_MAPPING = {
    # Text/Data formats
    'csv': 'text/csv',
    'json': 'application/json',
    'xml': 'application/xml',
    'txt': 'text/plain',
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    
    # Microsoft Office formats
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'doc': 'application/msword',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'ppt': 'application/vnd.ms-powerpoint',
    
    # Document formats
    'pdf': 'application/pdf',
    
    # Archive formats
    'zip': 'application/zip',
    'tar': 'application/x-tar',
    'gz': 'application/gzip',
    
    # Image formats
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'svg': 'image/svg+xml',
    'ico': 'image/x-icon',
    
    # Font formats
    'woff': 'font/woff',
    'woff2': 'font/woff2',
    'ttf': 'font/ttf',
    
    # Media formats
    'mp4': 'video/mp4',
    'mp3': 'audio/mpeg',
    'wav': 'audio/wav'
}

# Default content type for unknown file extensions
DEFAULT_CONTENT_TYPE = 'application/octet-stream'

# S3 configuration
MAX_FILE_SIZE_MB = 100
MAX_RETRIES = 3

# Validation constants
REQUIRED_PRIORITY = 1

# Logging levels
LOG_LEVELS = {
    'DEBUG': 10,
    'INFO': 20,
    'WARNING': 30,
    'ERROR': 40,
    'CRITICAL': 50
} 