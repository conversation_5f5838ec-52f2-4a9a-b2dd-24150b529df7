from psycopg2.errors import UniqueViolation
from pricesmart_common.utils import async_execute_query, get_array_format, get_str_repr
from configuration.environment import environment
from filters import constants as filter_constants
from filters import models as filter_models, queries as filter_queries
from filters.helpers import FilterModel
from events import data as event_data_module
from logger.logger import logger
from events.enums import EventProductSelectionType,EventStoreSelectionType
from pricesmart_common.data import get_config_value
from enums.Enums import ConfigModuleEnum, ConfigKeyEnum
from filters.types import HierarchyFiltersInfoType
from pricesmart_common import constants as common_constants
from product import utils as product_utils
from typing import List, Optional, Dict, Any
from filters import exceptions as filters_exceptions
from filters.constants import CUSTOM_FILTER_VALID_SCREENS

async def get_product_hierarchy_data(items: filter_models.HierarchyFilters):
    
    filters = items.filters.model_dump()
    filter_obj = FilterModel(filters)
    table_name = filter_constants.PRODUCT_TABLE
    table_alias = filter_constants.PRODUCT_TABLE_ALIAS
    build_config = {"table_name": table_name, "table_alias": table_alias}

    filter_obj.build_where_arr(build_config)
    filter_obj.build_where_str()

    select_name = filter_constants.HIERARCHY_NAME_MAPPING[items.hierarchy_type]
    select_id = filter_constants.HIERARCHY_ID_MAPPING[items.hierarchy_type]
    select_str = (
        f"{table_alias}.{select_id} as value, {table_alias}.{select_name} as label"
    )
    group_by_str = f"{table_alias}.{select_id}, {table_alias}.{select_name}"
    order_by_str = f"{table_alias}.{select_name}"
    not_null_condition = f"{table_alias}.{select_id} is not null"

    if items.allow_only_active_products:
        filter_obj.extend_where_str(f"{table_alias}.is_active = 1")
    
        
    filter_obj.extend_where_str(not_null_condition)
    where_str = filter_obj.where_str

    if items.event_id:
        event_data = await event_data_module.get_event_details(items.event_id,"product_inclusion_type")
    else:
        event_data = {}

    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        query = filter_queries.GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT_TYPE.format(
            select_str = select_str,
            table_name = table_name,
            table_alias = table_alias,
            where_str = where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
            event_id = items.event_id
        )
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        filter_obj.extend_where_str(
            f"""
                pm.product_id in (
                    select product_id 
                    from price_promo.included_event_products
                    where event_id = {items.event_id}
                )
            """
        )
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
            select_str=select_str,
            table_name=table_name,
            table_alias=table_alias,
            where_str=filter_obj.where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    else:
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
            select_str=select_str,
            table_name=table_name,
            table_alias=table_alias,
            where_str=where_str,
            group_by_str=group_by_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    logger.info(query)
    result = await async_execute_query(query)
    return result

async def get_store_hierarchy_data(request_payload: filter_models.HierarchyFilters):
    
    filters = request_payload.filters.model_dump()
    filter_obj = FilterModel(filters)
    table_name = filter_constants.STORE_TABLE
    table_alias = filter_constants.STORE_TABLE_ALIAS

    filter_obj.build_where_arr(
        {"table_name": table_name, "table_alias": table_alias}
    )
    filter_obj.build_where_str()

    select_name = filter_constants.HIERARCHY_NAME_MAPPING[request_payload.hierarchy_type]
    select_id = filter_constants.HIERARCHY_ID_MAPPING[request_payload.hierarchy_type]
    not_null_condition = f"{table_alias}.{select_id} is not null"

    filter_obj.extend_where_str(not_null_condition)

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"store_selection_type")
    else:
        event_data = {}

    if (event_data.get("store_selection_type") or EventStoreSelectionType.ALL_STORES.value) != EventStoreSelectionType.ALL_STORES.value:
        filter_obj.extend_where_str(
            f"""
                {table_alias}.store_id
                in (
                    select store_id
                    from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                )
            """
        )
    
    query = filter_queries.GET_HIERARCHY_FILTERS_QUERY.format(
        select_str=(
            f"{table_alias}.{select_id} as value, {table_alias}.{select_name} as label"
        ),
        table_name=table_name,
        table_alias=table_alias,
        where_str=filter_obj.where_str,
        group_by_str=f"{table_alias}.{select_id}, {table_alias}.{select_name}",
        order_by_str=f"{table_alias}.{select_name}",
        client_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query)
    return result


async def get_lifecycle_indicators(request: filter_models.HierarchyFilters):
    filters = request.filters.model_dump()
    is_specific = request.is_specific
    allow_only_active_products = request.allow_only_active_products

    table_name = filter_constants.PRODUCT_TABLE
    table_alias = filter_constants.PRODUCT_TABLE_ALIAS
    select_id = "lifecycle_indicator"

    filter_obj = FilterModel(filters)
    build_config = {"table_name": table_name, "table_alias": table_alias}
    filter_obj.build_where_arr(build_config)
    filter_obj.build_where_str()

    not_null_condition = f"{table_alias}.{select_id} is not null"

    if allow_only_active_products:
        filter_obj.extend_where_str(
            f"{filter_constants.PRODUCT_TABLE_ALIAS}.is_active = 1"
        )

    filter_obj.extend_where_str(not_null_condition)

    if request.event_id:
        event_data = await event_data_module.get_event_details(request.event_id,"product_inclusion_type")
    else:
        event_data = {}
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        filter_obj.extend_where_str(
            f"""
            (
                not exists (
                    select 1 from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id} and hierarchy_level_id = -2
                ) or 
                gb.id in (
                    select 
                        hierarchy_value_id
                    from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id}
                    and hierarchy_level_id = -2
                )
            )
            """
        )
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        filter_obj.extend_where_str(
            f"""
                pm.product_id in (
                    select product_id
                    from price_promo.included_event_products
                    where event_id = {request.event_id} 
                )
            """
        )

    where_str = filter_obj.where_str
    curr_cte = None
    if is_specific:
        curr_cte = "specific_cte"
    else:
        curr_cte = "non_specific_cte"

    if request.event_id and event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        query = filter_queries.GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT.format(
            where_str=where_str,
            curr_cte=curr_cte,
            event_id=request.event_id
        )
    else:
        query = filter_queries.GET_LIFECYCLE_INDIACTORS.format(
            where_str=where_str, curr_cte=curr_cte
        )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_lifecycle_indicators_v2(request: filter_models.LifeCycleIndicatorsRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    where_condition = [
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request.hierarchy_filters,
            filter_constants.PRODUCT_TABLE_ALIAS
        )
    ] if request.hierarchy_filters else []

    event_data = (
        await event_data_module.get_event_details(request.event_id,"product_inclusion_type")
        if request.event_id
        else {}
    )

    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        lifecycle_indicator_level_id = await product_utils.get_lifecycle_indicator_level_id()
        where_condition.append(
            f"""
            (
                not exists (
                    select 1 from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id} and hierarchy_level_id = {lifecycle_indicator_level_id}
                ) or 
                gb.id in (
                    select 
                        hierarchy_value_id
                    from price_promo.included_event_product_hierarchy
                    where event_id = {request.event_id}
                    and hierarchy_level_id = {lifecycle_indicator_level_id}
                )
            )
            """
        )
    
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        where_condition.append(
            f"""
                pm.product_id in (
                    select product_id
                    from price_promo.included_event_products
                    where event_id = {request.event_id} 
                )
            """
        )


    if request.event_id and event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        return await get_lifecycle_indicators_based_on_whole_category_event(
            request.event_id,
            where_condition,
            hierarchy_filters_info
        )
    else:
        where_str = common_constants.AND.join(where_condition) if where_condition else "true"
        query = filter_queries.GET_LIFECYCLE_INDIACTORS_V2.format(
            where_str=where_str
        )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_lifecycle_indicators_based_on_whole_category_event(
    event_id: int,
    where_condition: list[str],
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType]
):
    where_condition.append(get_event_where_condition(hierarchy_filters_info,filter_constants.PRODUCT_TABLE_ALIAS))

    where_str = common_constants.AND.join(where_condition)
    query = filter_queries.GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT_V2.format(
        where_str=where_str,
        event_id=event_id,
        event_select_str=get_event_select_str(hierarchy_filters_info)
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

async def get_product_filters(request_payload: filter_models.ProductFiltersRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    where_condition = []
    where_condition.append(
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request_payload.hierarchy_filters,
            filter_constants.PRODUCT_TABLE_ALIAS
        )
    ) if request_payload.hierarchy_filters else []

    select_str = f"""
        {filter_constants.PRODUCT_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["value_column"]} as label,
        {filter_constants.PRODUCT_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["id_column"]} as value
    """

    order_by_str = f"""{hierarchy_filters_info[request_payload.query_column]["value_column"]}"""

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"product_inclusion_type")
    else:
        event_data = {}

    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    
    if event_data.get("product_inclusion_type") == EventProductSelectionType.WHOLE_CATEGORY.value:
        return (
            await get_product_filters_based_on_whole_category_event(
                request_payload,
                where_condition,
                select_str,
                order_by_str
            )
        )
        
    elif event_data.get("product_inclusion_type") == EventProductSelectionType.SPECIFIC_PRODUCTS.value:
        where_condition.append(
            f"""
                {filter_constants.PRODUCT_TABLE_ALIAS}.product_id in (
                    select product_id 
                    from price_promo.included_event_products
                    where event_id = {request_payload.event_id}
                )
            """
        )
        where_str = common_constants.AND.join(where_condition)
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
            select_str=select_str,
            table_name=filter_constants.PRODUCT_TABLE,
            table_alias=filter_constants.PRODUCT_TABLE_ALIAS,
            value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
            where_str=where_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    else:
        query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
            select_str=select_str,
            table_name=filter_constants.PRODUCT_TABLE,
            table_alias=filter_constants.PRODUCT_TABLE_ALIAS,
            value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
            where_str=where_str,
            order_by_str=order_by_str,
            client_schema=environment.promo_schema,
        )
    logger.info(query)
    result = await async_execute_query(query)
    return result

def generate_hierarchy_where_condition(
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType],
    hierarchy_filters: dict[str,list[int]],
    table_alias: str
):
    where_condition = []
    for hierarchy_filter_key, hierarchy_filter_values in hierarchy_filters.items():
        if not hierarchy_filter_values:
            continue

        id_column = hierarchy_filters_info[hierarchy_filter_key]["id_column"]
        where_condition.append(
            f""" 
                {table_alias}.{id_column} = any(
                    {get_array_format(hierarchy_filter_values)}::int[]
                )
            """
        )
    return common_constants.AND.join(where_condition)

async def get_product_filters_based_on_whole_category_event(
    request_payload: filter_models.ProductFiltersRequest,
    where_condition: list[str],
    select_str: str,
    order_by_str: str
):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )

    where_condition.append(
        get_event_where_condition(hierarchy_filters_info,filter_constants.PRODUCT_TABLE_ALIAS)
    )

    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    query = filter_queries.GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT.format(
        select_str = select_str,
        event_select_str = get_event_select_str(hierarchy_filters_info),
        table_name = filter_constants.PRODUCT_TABLE,
        table_alias = filter_constants.PRODUCT_TABLE_ALIAS,
        value_column = hierarchy_filters_info[request_payload.query_column]["value_column"],
        where_str = where_str,
        order_by_str=order_by_str,
        client_schema=environment.promo_schema,
        event_id = request_payload.event_id
    )
    logger.info(query)
    data = await async_execute_query(query)
    return data

def get_event_select_str(hierarchy_filters_info: dict[str,HierarchyFiltersInfoType]):
    return ",".join([
        f"array_agg(hierarchy_value_id) filter (where hierarchy_level_id = {hierarchy_filter['id']}) as {hierarchy_filter['id_column']}"
        for hierarchy_filter in hierarchy_filters_info.values()
        if hierarchy_filter["is_linked_to_event"]
    ])

def get_event_where_condition(hierarchy_filters_info: dict[str,HierarchyFiltersInfoType],table_alias: str):
    return " and ".join([
        f"(ehc.{hierarchy_filter['id_column']} is null or {table_alias}.{hierarchy_filter['id_column']} = any(ehc.{hierarchy_filter['id_column']}))"
        for hierarchy_filter in hierarchy_filters_info.values()
        if hierarchy_filter["is_linked_to_event"]
    ])

async def get_store_filters(request_payload: filter_models.StoreFiltersRequest):
    hierarchy_filters_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.HIERARCHY_FILTERS
    )

    where_condition = [
        generate_hierarchy_where_condition(
            hierarchy_filters_info,
            request_payload.hierarchy_filters,
            filter_constants.STORE_TABLE_ALIAS
        )
    ] if request_payload.hierarchy_filters else []

    select_str = f"""
        {filter_constants.STORE_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["value_column"]} as label,
        {filter_constants.STORE_TABLE_ALIAS}.{hierarchy_filters_info[request_payload.query_column]["id_column"]} as value
    """

    order_by_str = f"""{hierarchy_filters_info[request_payload.query_column]["value_column"]}"""

    if request_payload.event_id:
        event_data = await event_data_module.get_event_details(request_payload.event_id,"store_selection_type")
    else:
        event_data = {}

    if (event_data.get("store_selection_type") or EventStoreSelectionType.ALL_STORES.value) != EventStoreSelectionType.ALL_STORES.value:
        where_condition.append(
            f"""
                {filter_constants.STORE_TABLE_ALIAS}.store_id
                in (
                    select store_id
                    from price_promo.included_event_stores
                    where event_id = {request_payload.event_id}
                )
            """
        )
    
    where_str = common_constants.AND.join(where_condition) if where_condition else "true"
    query = filter_queries.GET_HIERARCHY_FILTERS_QUERY_V2.format(
        select_str=select_str,
        table_name=filter_constants.STORE_TABLE,
        table_alias=filter_constants.STORE_TABLE_ALIAS,
        value_column=hierarchy_filters_info[request_payload.query_column]["value_column"],
        where_str=where_str,
        order_by_str=order_by_str,
        client_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query)
    return result
    
async def get_currency_filter(request_payload: filter_models.CurrencyFilter):
    if request_payload.promo_id:
        query = filter_queries.GET_CURENCY_IDS_USING_PROMO_ID.format(
            promo_id = request_payload.promo_id
        )
    else:
        query = filter_queries.GET_CURRENCY_FILTER.format(
            country_ids = get_array_format(request_payload.country_ids)
        )
    data = await async_execute_query(query)
    return data

async def get_priority_filter(request_payload: filter_models.PriorityFilter):
    where_str = get_priority_filter_where_str(request_payload)
    query = filter_queries.GET_PRIORITY_FILTER.format(
        where_str = where_str
    )
    data = await async_execute_query(query)
    return data

def get_priority_filter_where_str(request_payload: filter_models.PriorityFilter):
    start_date=request_payload.start_date.strftime(
        common_constants.PYTHON_DATE_FORMAT
    ),
    end_date=request_payload.end_date.strftime(
        common_constants.PYTHON_DATE_FORMAT
    )
    event_ids = get_array_format(request_payload.event_ids) if request_payload.event_ids else "null"
    if request_payload.promo_ids:
        return f" pm.promo_id = ANY({get_array_format(request_payload.promo_ids)})"
    else:
        return f""" pm.promo_id = any(array(select * from price_promo.fn_filter_promos(
                {get_str_repr(start_date)},
                {get_str_repr(end_date)},
                {get_str_repr(request_payload.product_hierarchies)},
                {get_str_repr(request_payload.store_hierarchies)},
                {event_ids},
                {request_payload.show_partially_overlapping_events}
            ))) """


# Custom Filters Data Functions


async def create_custom_filter(
    filter_config: filter_models.CustomFilter,
    user_id: int
) -> Dict[str, Any]:
    """
    Create a new custom filter in the database.
    
    This function inserts a new filter record into the custom_filters table and optionally
    sets it as the user's default filter for the specified screen.
    
    Args:
        filter_config (CustomFilter): The filter configuration object containing:
            - filter_name: Unique name for the filter
            - description: Optional description
            - scope: "personal" or "global"
            - screen_name: Specific screen where filter applies
            - is_multi_screen: Whether filter applies to all screens
            - filter_config: JSON configuration data
            - is_default: Whether to set as default for the screen
        user_id (int): The user ID creating the filter
        
    Returns:
        Dict[str, Any]: Dictionary containing the created filter's ID
        
    Raises:
        CustomFilterNameAlreadyExistsException: If filter name already exists for the user
        
    Note:
        - The filter is always created as non-deleted (is_deleted = FALSE)
        - If is_default is True and screen_name is provided, the filter is set as default
        - Global filters require appropriate permissions (checked in service layer)
        - Database enforces uniqueness constraints on filter names per user
    """
    # Insert the filter (database will enforce uniqueness)
    query = filter_queries.INSERT_CUSTOM_FILTER.format(
        promo_schema=environment.promo_schema,
        filter_name=get_str_repr(filter_config.filter_name),
        description=get_str_repr(filter_config.description),
        scope=get_str_repr(filter_config.scope),
        screen_name=get_str_repr(filter_config.screen_name),
        is_multi_screen=get_str_repr(filter_config.is_multi_screen),
        filter_config=get_str_repr(filter_config.filter_config),
        created_by=user_id,
        timezone=get_str_repr(common_constants.CLIENT_TIMEZONE)
    )
    
    result = await handle_custom_filter_operation(
        query=query,
        filter_name=filter_config.filter_name
    )
    
    # Set user default filter if this is marked as default
    await handle_user_default_filter_preferences(filter_config.is_default, filter_config.screen_name, filter_config.is_multi_screen, result[0]["filter_id"], user_id)
    
    return {"filter_id": result[0]["filter_id"]}


async def get_custom_filters(
    user_id: int,
    scope: Optional[str] = None,
    screen_name: Optional[str] = None,
    is_default: Optional[bool] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve custom filters from the database with optional filtering.
    
    This function queries the custom_filters table and returns filters that the user can access,
    with support for filtering by scope, screen name, and default status. The is_default field
    is determined by joining with the custom_filters_defaults table.
    
    Args:
        user_id (int): The user ID requesting the filters
        scope (Optional[str]): Filter by scope - "personal", "global", or None for all
        screen_name (Optional[str]): Filter by specific screen name
        is_default (Optional[bool]): Filter by default status - True/False/None for all
        
    Returns:
        List[Dict[str, Any]]: List of filter objects with details including is_default status
        
    Note:
        - Personal filters are only returned for their creators
        - Global filters are returned for all users
        - is_default is determined by checking the custom_filters_defaults table
        - Multi-screen filters are included when filtering by screen_name
        - Results are ordered by creation date (newest first)
        - Only non-deleted filters are returned
    """
    # Build query filters
    scope_filter = ""
    if scope == "personal":
        scope_filter = f"AND (cf.scope = {get_str_repr(scope)} AND cf.created_by = {user_id})"
    elif scope == "global":
        scope_filter = f"AND cf.scope = {get_str_repr(scope)}"
    else:
        scope_filter = f"AND ((cf.scope = 'personal' AND cf.created_by = {user_id}) OR cf.scope = 'global')"
    
    screen_filter = ""
    if screen_name:
        screen_filter = f"AND (cf.screen_name = {get_str_repr(screen_name)} OR cf.is_multi_screen = TRUE)"
    
    # Build query
    query = filter_queries.GET_CUSTOM_FILTERS.format(
        promo_schema=environment.promo_schema,
        user_id=user_id,
        scope_filter=scope_filter,
        screen_filter=screen_filter
    )
    
    logger.info(f"Getting custom filters - Query: {query}")
    
    result = await async_execute_query(query)
    
    # Filter by is_default if requested
    if is_default is not None:
        result = [filter_data for filter_data in result if filter_data["is_default"] == is_default]
    
    return result


async def get_custom_filter_by_id(filter_id: int, user_id: int) -> Optional[Dict[str, Any]]:
    """
    Retrieve a specific custom filter by its ID.
    
    This function queries the custom_filters table for a specific filter and includes
    the is_default status by joining with the custom_filters_defaults table.
    
    Args:
        filter_id (int): The unique identifier of the filter to retrieve
        user_id (int): The user ID requesting the filter
        
    Returns:
        Optional[Dict[str, Any]]: Complete filter details including is_default status, or None if not found
        
    Note:
        - Users can only access their own personal filters
        - Global filters are accessible to all users
        - is_default is determined by checking the custom_filters_defaults table
        - Only non-deleted filters are returned
        - Returns None if filter doesn't exist or user lacks access
    """
    query = filter_queries.GET_CUSTOM_FILTER_BY_ID.format(
        promo_schema=environment.promo_schema,
        filter_id=filter_id,
        user_id=user_id
    )
    
    logger.info(f"Getting custom filter by ID {filter_id} - Query: {query}")
    
    result = await async_execute_query(query)
    logger.info(f"Retrieved custom filter by ID {filter_id}: {result}")
    if not result:
        return None
    
    return result[0]


async def update_custom_filter(
    filter_id: int,
    filter_config: filter_models.UpdateCustomFilterRequest,
    user_id: int
) -> Dict[str, Any]:
    """
    Update an existing custom filter in the database.
    
    This function updates a filter's configuration and optionally manages its default status.
    The function handles partial updates by merging with existing data and manages
    default filter preferences when is_default is specified.
    
    Args:
        filter_id (int): The unique identifier of the filter to update
        filter_config (UpdateCustomFilterRequest): The updated filter data (all fields optional):
            - filter_name: New name for the filter
            - description: Updated description
            - scope: "personal" or "global"
            - screen_name: Updated screen name
            - is_multi_screen: Updated multi-screen setting
            - filter_config: Updated JSON configuration
            - is_default: Whether to set as default for the screen
        user_id (int): The user ID updating the filter
        
    Returns:
        Dict[str, Any]: Updated filter details
        
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Only the creator can update personal filters
        - Global filters require appropriate permissions (checked in service layer)
        - is_default changes are handled separately from database updates
        - Partial updates are supported (only provided fields are updated)
        - Database enforces uniqueness constraints on filter names
    """
    # Get existing filter to check if screen_name is being updated
    existing_filter = await get_custom_filter_by_id(filter_id, user_id)
    if not existing_filter:
        raise filters_exceptions.CustomFilterNotFoundException()
    
    # Validate permissions for global scope (both existing and new scope)
    if (filter_config.scope == "global" or existing_filter["scope"] == "global"):
        await validate_global_filter_write_permissions(user_id)
    
    # Handle default filter changes
    if filter_config.is_default is not None:
        screen_name = filter_config.screen_name if filter_config.screen_name is not None else existing_filter["screen_name"]
        is_multi_screen = filter_config.is_multi_screen if filter_config.is_multi_screen is not None else existing_filter["is_multi_screen"]
        await handle_user_default_filter_preferences(filter_config.is_default, screen_name, is_multi_screen, filter_id, user_id)
    
    # Build update data by merging with existing data
    update_fields = []
    
    # Get field names from the model (excluding None values and is_default)
    model_fields = filter_config.model_dump(exclude_none=True)
    if "is_default" in model_fields:
        del model_fields["is_default"]  # Remove is_default from database update
    
    for field, value in model_fields.items():
        if field == "filter_config":
            update_fields.append(f"{field} = {get_str_repr(value)}::jsonb")
        else:
            update_fields.append(f"{field} = {get_str_repr(value)}")
    
    if not update_fields:
        raise filters_exceptions.CustomFilterUpdateFailedException()
    
    # Build the update query
    query = filter_queries.UPDATE_CUSTOM_FILTER_PARTIAL.format(
        promo_schema=environment.promo_schema,
        update_fields=", ".join(update_fields),
        filter_id=filter_id,
        user_id=user_id,
        updated_by=user_id,
        timezone=get_str_repr(common_constants.CLIENT_TIMEZONE)
    )
    
    await handle_custom_filter_operation(
        query=query,
        filter_name=filter_config.filter_name or "unknown"
    )


async def delete_custom_filter(filter_id: int, user_id: int) -> Dict[str, Any]:
    """
    Delete a custom filter from the database.
    
    This function soft deletes a filter by updating its is_deleted status.
    It also cleans up user default filter preferences if the deleted filter
    was a default filter for any user.
    
    Args:
        filter_id (int): The unique identifier of the filter to delete
        user_id (int): The user ID deleting the filter
        
    Returns:
        Dict[str, Any]: Details of the deleted filter
        
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Only the creator can delete personal filters
        - Global filters require appropriate permissions
        - Soft deletion is used to maintain data integrity
    """
    # Get existing filter to check scope and validate permissions
    existing_filter = await get_custom_filter_by_id(filter_id, user_id)
    if not existing_filter:
        raise filters_exceptions.CustomFilterNotFoundException()
    
    # Validate permissions for global scope
    if existing_filter["scope"] == "global":
        await validate_global_filter_write_permissions(user_id)
    
    # Soft delete the filter
    query = filter_queries.DELETE_CUSTOM_FILTER.format(
        promo_schema=environment.promo_schema,
        filter_id=filter_id,
        user_id=user_id,
        updated_by=user_id,
        timezone=get_str_repr(common_constants.CLIENT_TIMEZONE)
    )
    
    logger.info(f"Deleting custom filter {filter_id} - Query: {query}")
    
    await async_execute_query(query)
    
    await remove_user_default_filter_preference_by_filter_id(user_id, filter_id)

async def copy_custom_filter(
    copy_filter_data: filter_models.CopyFilterRequest,
    existing_filter: Dict[str, Any],
    user_id: int
) -> Dict[str, Any]:
    """
    Create a copy of an existing custom filter.
    
    This function duplicates an existing filter with a new name and optional modifications.
    The copied filter inherits all configuration from the original but becomes a personal
    filter owned by the user performing the copy operation.
    
    Args:
        copy_filter_data (CopyFilterRequest): The copy configuration:
            - filter_name: New name for the copied filter (required)
            - screen_name: Screen name for the copy (optional)
            - is_default: Whether to set the copy as default (optional)
        existing_filter (Dict[str, Any]): The existing filter data to copy from
        user_id (int): The user ID creating the copy
        
    Returns:
        Dict[str, Any]: Dictionary containing the new filter's ID
        
    Raises:
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - The copied filter becomes a personal filter owned by the user
        - All configuration data is duplicated from the original
        - Users can copy both personal and global filters
        - The copy inherits all settings except name, screen_name, and default status
        - If is_default is True, the copy is set as default for the specified screen
    """
    # Validate permissions for global scope
    if existing_filter["scope"] == "global":
        await validate_global_filter_write_permissions(user_id)
    
    # Determine the values to use for screen_name and is_default
    # Use the request values if provided, otherwise use existing filter values
    screen_name = copy_filter_data.screen_name if copy_filter_data.screen_name is not None else existing_filter.get("screen_name")
    is_default = copy_filter_data.is_default if copy_filter_data.is_default is not None else existing_filter.get("is_default")

    # Copy the filter with the specified overrides (database will enforce uniqueness)
    query = filter_queries.INSERT_CUSTOM_FILTER.format(
        promo_schema=environment.promo_schema,
        filter_name=get_str_repr(copy_filter_data.filter_name),
        description=get_str_repr(existing_filter["description"]),
        scope=get_str_repr(existing_filter["scope"]),
        screen_name=get_str_repr(screen_name),
        is_multi_screen=get_str_repr(existing_filter["is_multi_screen"]),
        filter_config=get_str_repr(existing_filter["filter_config"]),
        created_by=user_id,
        timezone=get_str_repr(common_constants.CLIENT_TIMEZONE)
    )
    
    result = await handle_custom_filter_operation(
        query=query,
        filter_name=copy_filter_data.filter_name
    )
    
    # Set user preference if this is marked as default
    if is_default and screen_name:
        filter_id = result[0]["filter_id"]
        await handle_user_default_filter_preferences(is_default, screen_name, existing_filter["is_multi_screen"], filter_id, user_id)
    
    return {"filter_id": result[0]["filter_id"]}


# Check if user has write permissions for global filters (configurable role codes via CTE)
async def validate_global_filter_write_permissions(user_id: int) -> None:
    """
    Validate if a user has permissions to create or modify global filters.
    
    This function checks the user's role codes against the configured global filter
    write permissions to determine if they can create or modify global filters.
    
    Args:
        user_id (int): The user ID to check permissions for
        
    Returns:
        None
        
    Raises:
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Uses configurable role codes from tb_tool_configurations
        - Raises exception if user has no eligible roles
        - Used by data layer to enforce global filter permissions
    """
    query = filter_queries.CHECK_GLOBAL_FILTER_WRITE_PERMISSIONS.format(
        global_schema=environment.global_schema,
        promo_schema=environment.promo_schema,
        user_id=user_id,
        application_code=common_constants.application_code,
        filter_module=get_str_repr(ConfigModuleEnum.FILTER.value),
        global_filter_write_eligible_role_codes_config=get_str_repr(ConfigKeyEnum.GLOBAL_FILTER_WRITE_ELIGIBLE_ROLE_CODES.value)
    )

    logger.info(f"validate_global_filter_write_permissions query: {query}")
    
    logger.info(f"Checking global filter write permissions for user {user_id}")
    
    result = await async_execute_query(query)
    
    if not result:
        raise filters_exceptions.CustomFilterInsufficientPermissionsException()

# Generic function to handle custom filter operations with consistent error handling
async def handle_custom_filter_operation(
    query: str,
    filter_name: str
) -> Dict[str, Any]:
    """
    Execute a custom filter database operation with consistent error handling.
    
    This function provides a centralized way to execute filter-related database queries
    with proper exception handling for common database constraint violations.
    
    Args:
        query (str): The SQL query to execute
        filter_name (str): The filter name for error reporting
        
    Returns:
        Dict[str, Any]: Query result data
        
    Raises:
        CustomFilterNameAlreadyExistsException: If database constraint violation occurs
    """
    try:
        logger.info(f"Executing query: {query}")
        return await async_execute_query(query)        
    except UniqueViolation:
        # Handle database constraint violation for duplicate names
        raise filters_exceptions.CustomFilterNameAlreadyExistsException(filter_name)

async def set_user_default_filter_preference(
    screen_names: list[str],
    filter_id: int,
    user_id: int
) -> None:
    """
    Set default filter preferences for multiple screens using a single optimized query.
    
    This function efficiently sets a filter as the default for multiple screens by using
    a DELETE and INSERT operation in a single query. It first removes any existing
    default filters for the specified screens, then inserts the new default preferences.
    
    Args:
        screen_names (list[str]): List of screen names to set as default for this filter
        filter_id (int): The filter ID to set as default
        user_id (int): The user ID for whom to set the default preferences
        
    Returns:
        None
        
    Note:
        - Uses a single DELETE and INSERT query for better performance
        - Handles both single screen and multiple screens efficiently
        - Replaces any existing default filters for the specified screens
        - No error is raised if the filter doesn't exist (referential integrity handled by DB)
        - Logs the query for debugging purposes
        - Empty screen_names list is handled gracefully (no operation)
    """
    if not screen_names:
        return
        
    insert_values = []
    screen_values = []
    for screen_name in screen_names:
        insert_values.append(f"({user_id}, {get_str_repr(screen_name)}, {filter_id})")
        screen_values.append(get_str_repr(screen_name))
    
    query = filter_queries.DELETE_AND_INSERT_CUSTOM_FILTER_DEFAULTS.format(
        promo_schema=environment.promo_schema,
        user_id=user_id,
        screen_values=", ".join(screen_values),
        insert_values=", ".join(insert_values)
    )
    
    logger.info(f"Setting default filter {filter_id} for user {user_id} across {len(screen_names)} screens with query: {query}")
    await async_execute_query(query)

async def remove_user_default_filter_preference(
    screen_names: list[str],
    filter_id: int,
    user_id: int
) -> None:
    """
    Remove default filter preferences for multiple screens using a single optimized query.
    
    This function efficiently removes default filter preferences for multiple screens
    by using a single DELETE query with an IN clause. It removes all default
    preferences for the specified screens and user.
    
    Args:
        screen_names (list[str]): List of screen names to remove default preferences from
        filter_id (int): The filter ID (used for logging purposes)
        user_id (int): The user ID for whom to remove the default preferences
        
    Returns:
        None
        
    Note:
        - Uses a single DELETE query with IN clause for better performance
        - Handles both single screen and multiple screens efficiently
        - Removes all default filters for the specified screens and user
        - No error is raised if no default filters exist
        - Logs the query for debugging purposes
        - Empty screen_names list is handled gracefully (no operation)
        - The filter_id parameter is used for logging but not in the query
    """
    if not screen_names:
        return
    
    screen_names_str = ", ".join([get_str_repr(screen_name) for screen_name in screen_names])
    
    query = filter_queries.DELETE_CUSTOM_FILTER_DEFAULTS.format(
        promo_schema=environment.promo_schema,
        user_id=user_id,
        filter_id=filter_id,
        screen_names=screen_names_str
    )
    
    logger.info(f"Removing default filters for user {user_id} across {len(screen_names)} screens with query: {query}")
    await async_execute_query(query)

async def remove_user_default_filter_preference_by_filter_id(
    user_id: int,
    filter_id: int
) -> None:
    """
    Remove a specific user's default filter for a specific screen and filter.
    
    This function removes a specific filter from the user's default preferences
    for a given screen, providing more precise control than the general removal function.
    
    Args:
        screen_name (str): The screen name to remove default filter from
        user_id (int): The user ID
        filter_id (int): The specific filter ID to remove from defaults
        
    Note:
        - Removes only the specified filter from defaults for the screen
        - No error is raised if the specific filter is not set as default
        - Provides more granular control than remove_user_default_filter_preference
    """
    query = filter_queries.DELETE_CUSTOM_FILTER_DEFAULT_BY_FILTER_ID.format(
        promo_schema=environment.promo_schema,
        user_id=user_id,
        filter_id=filter_id
    )
    
    logger.info(f"Removing specific default filters for user {user_id} and filter {filter_id}")
    await async_execute_query(query)

async def get_default_filter_for_screen(screen_name: str, user_id: int) -> Optional[Dict[str, Any]]:
    """
    Get the default filter for a screen using the preferences table.
    
    This function retrieves the user's default filter for a specific screen by querying
    the custom_filters_defaults table. It returns the complete filter details including
    configuration data.
    
    Args:
        screen_name (str): The screen name to get the default filter for
        user_id (int): The user ID
        
    Returns:
        Optional[Dict[str, Any]]: The default filter data with complete details, or None if no default exists
        
    Note:
        - Uses the custom_filters_defaults table for user preferences
        - Only returns non-deleted filters
        - Returns None if no default filter is set for the screen
        - Includes complete filter configuration and metadata
        - Results are ordered by creation date (newest first)
    """
    query = filter_queries.GET_DEFAULT_FILTER_FOR_SCREEN.format(
        promo_schema=environment.promo_schema,
        screen_name=get_str_repr(screen_name),
        user_id=user_id
    )
    
    logger.info(f"Getting default filter for screen '{screen_name}' and user {user_id} - Query: {query}")
    
    result = await async_execute_query(query)
    
    if not result:
        logger.info(f"No default filter found for screen '{screen_name}' and user {user_id}")
        return None
    
    logger.info(f"Found default filter {result[0]['filter_id']} for screen '{screen_name}' and user {user_id}")
    return result[0]

async def handle_user_default_filter_preferences(is_default: bool, screen_name: str, is_multi_screen: bool, filter_id: int, user_id: int):
    """
    Common function to handle user default filter preferences using dynamic function selection.
    
    This function provides a unified interface for setting or removing default filter
    preferences. It dynamically selects the appropriate function based on the is_default
    parameter and handles both single screen and multi-screen scenarios efficiently.
    
    Args:
        is_default (bool): Whether to set (True) or remove (False) default preferences
        screen_name (str): The specific screen name for single screen operations
        is_multi_screen (bool): Whether the filter applies to multiple screens
        filter_id (int): The filter ID to set or remove as default
        user_id (int): The user ID for whom to manage default preferences
        
    Returns:
        None
        
    Note:
        - Uses dynamic function selection for cleaner code
        - Handles both single screen and multi-screen scenarios
        - For multi-screen filters, operates on all valid screens (CUSTOM_FILTER_VALID_SCREENS)
        - For single screen filters, operates only on the specified screen
        - No operation is performed if is_default is None
        - Uses optimized bulk operations for better performance
        - Gracefully handles empty screen_name for multi-screen scenarios
    """
    if is_default is None:
        return
    set_unset_default_filter_fn = set_user_default_filter_preference if is_default else remove_user_default_filter_preference
    if is_multi_screen:
        await set_unset_default_filter_fn(CUSTOM_FILTER_VALID_SCREENS, filter_id, user_id)
    elif screen_name:
        await set_unset_default_filter_fn([screen_name], filter_id, user_id)
