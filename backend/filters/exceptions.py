from exceptions.custom_base_exception import CustomBaseException
from fastapi import status


class FilterBaseException(CustomBaseException):
    """Base exception for all Filter related exceptions"""
    pass

class CustomFilterBaseException(FilterBaseException):
    """Base exception for all Custom Filter related exceptions"""
    pass


class CustomFilterNotFoundException(CustomFilterBaseException):
    def __init__(self, message: str = "Custom filter not found or access denied") -> None:
        super().__init__(message, status.HTTP_404_NOT_FOUND)


class CustomFilterNameAlreadyExistsException(CustomFilterBaseException):
    def __init__(self, filter_name: str) -> None:
        message = f"Filter name '{filter_name}' already exists"
        super().__init__(message, status.HTTP_400_BAD_REQUEST)


class CustomFilterInsufficientPermissionsException(CustomFilterBaseException):
    def __init__(self, message: str = "Insufficient permissions to create/modify global filters") -> None:
        super().__init__(message, status.HTTP_403_FORBIDDEN)


class CustomFilterUpdateFailedException(CustomFilterBaseException):
    def __init__(self, message: str = "Failed to update custom filter") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomFilterDeleteFailedException(CustomFilterBaseException):
    def __init__(self, message: str = "Failed to delete custom filter") -> None:
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR) 