# Product Attributes
CHANNEL_ECOMMERCE = "Ecom"
CHANNEL_BNM = "B&M"
CHANNEL_VALUES = [CHANNEL_BNM, CHANNEL_ECOMMERCE]

STORE_TABLE = "tb_store_master"
STORE_TABLE_ALIAS = "sm"
PRODUCT_TABLE = "product_master"
PRODUCT_TABLE_ALIAS = "pm"

STORE_HIERARCHY = "store_hierarchy"
PRODUCT_HIERARCHY = "product_hierarchy"
HIERARCHY_TYPES = [PRODUCT_HIERARCHY, STORE_HIERARCHY]

BRAND = "brand"
COLOR = "color_id"
STYLE_COLOR = "style_color"
PROD_HIERARCHY0 = "l0_ids"
PROD_HIERARCHY1 = "l1_ids"
PROD_HIERARCHY2 = "l2_ids"
PROD_HIERARCHY3 = "l3_ids"
PROD_HIERARCHY4 = "l4_ids"
PROD_HIERARCHY5 = "l5_ids"

LIFECYCLE_INDICATOR = "lifecycle"

STORE_HIERARCHY0 = "s0_ids"
STORE_HIERARCHY1 = "s1_ids"
STORE_HIERARCHY2 = "s2_ids"
STORE_HIERARCHY3 = "s3_ids"
STORE_HIERARCHY4 = "s4_ids"
STORE_HIERARCHY5 = "s5_ids"
STORE_HIERARCHY6 = "store_code"

NUM_BRAND = 6
NUM_COLOR = 7
NUM_STYLE_COLOR = 8
NUM_PROD_HIERARCHY0 = 0
NUM_PROD_HIERARCHY1 = 1
NUM_PROD_HIERARCHY2 = 2
NUM_PROD_HIERARCHY3 = 3
NUM_PROD_HIERARCHY4 = 4
NUM_PROD_HIERARCHY5 = 5
NUM_STORE_HIERARCHY0 = 10
NUM_STORE_HIERARCHY1 = 11
NUM_STORE_HIERARCHY2 = 12
NUM_STORE_HIERARCHY3 = 13
NUM_STORE_HIERARCHY4 = 14
NUM_STORE_HIERARCHY5 = 15
NUM_STORE_HIERARCHY6 = 16

STR_NUM_BRAND = "6"
STR_NUM_COLOR = "7"
STR_NUM_STYLE_COLOR = "8"
STR_NUM_PROD_HIERARCHY0 = "0"
STR_NUM_PROD_HIERARCHY1 = "1"
STR_NUM_PROD_HIERARCHY2 = "2"
STR_NUM_PROD_HIERARCHY3 = "3"
STR_NUM_PROD_HIERARCHY4 = "4"
STR_NUM_PROD_HIERARCHY5 = "5"
STR_NUM_STORE_HIERARCHY0 = "10"
STR_NUM_STORE_HIERARCHY1 = "11"
STR_NUM_STORE_HIERARCHY2 = "12"
STR_NUM_STORE_HIERARCHY3 = "13"
STR_NUM_STORE_HIERARCHY4 = "14"
STR_NUM_STORE_HIERARCHY5 = "15"
STR_NUM_STORE_HIERARCHY6 = "16"

PRODUCT_HIERARCHIES = [
    BRAND,
    COLOR,
    STYLE_COLOR,
    PROD_HIERARCHY0,
    PROD_HIERARCHY1,
    PROD_HIERARCHY2,
    PROD_HIERARCHY3,
    PROD_HIERARCHY4,
    PROD_HIERARCHY5,
    LIFECYCLE_INDICATOR,
    NUM_BRAND,
    NUM_COLOR,
    NUM_STYLE_COLOR,
    NUM_PROD_HIERARCHY0,
    NUM_PROD_HIERARCHY1,
    NUM_PROD_HIERARCHY2,
    NUM_PROD_HIERARCHY3,
    NUM_PROD_HIERARCHY4,
    NUM_PROD_HIERARCHY5,
    STR_NUM_BRAND,
    STR_NUM_COLOR,
    STR_NUM_STYLE_COLOR,
    STR_NUM_PROD_HIERARCHY0,
    STR_NUM_PROD_HIERARCHY1,
    STR_NUM_PROD_HIERARCHY2,
    STR_NUM_PROD_HIERARCHY3,
    STR_NUM_PROD_HIERARCHY4,
    STR_NUM_PROD_HIERARCHY5,
]

STORE_HIERARCHIES = [
    STORE_HIERARCHY0,
    STORE_HIERARCHY1,
    STORE_HIERARCHY2,
    STORE_HIERARCHY3,
    STORE_HIERARCHY4,
    STORE_HIERARCHY5,
    STORE_HIERARCHY6,
    NUM_STORE_HIERARCHY0,
    NUM_STORE_HIERARCHY1,
    NUM_STORE_HIERARCHY2,
    NUM_STORE_HIERARCHY3,
    NUM_STORE_HIERARCHY4,
    NUM_STORE_HIERARCHY5,
    NUM_STORE_HIERARCHY6,
    STR_NUM_STORE_HIERARCHY0,
    STR_NUM_STORE_HIERARCHY1,
    STR_NUM_STORE_HIERARCHY2,
    STR_NUM_STORE_HIERARCHY3,
    STR_NUM_STORE_HIERARCHY4,
    STR_NUM_STORE_HIERARCHY5,
    STR_NUM_STORE_HIERARCHY6,
]

PH_ID_MAPPING = {
    BRAND: "brand_cid",
    NUM_BRAND: "brand_cid",
    STR_NUM_BRAND: "brand_cid",
    COLOR: "color_id",
    NUM_COLOR: "color_id",
    STR_NUM_COLOR: "color_id",
    STYLE_COLOR: "product_id",
    NUM_STYLE_COLOR: "product_id",
    STR_NUM_STYLE_COLOR: "product_id",
    PROD_HIERARCHY0: "l0_cid",
    PROD_HIERARCHY1: "l1_cid",
    PROD_HIERARCHY2: "l2_cid",
    PROD_HIERARCHY3: "l3_cid",
    PROD_HIERARCHY4: "l4_cid",
    PROD_HIERARCHY5: "l5_cid",
    LIFECYCLE_INDICATOR: "lifecycle_indicator",
    NUM_PROD_HIERARCHY0: "l0_cid",
    NUM_PROD_HIERARCHY1: "l1_cid",
    NUM_PROD_HIERARCHY2: "l2_cid",
    NUM_PROD_HIERARCHY3: "l3_cid",
    NUM_PROD_HIERARCHY4: "l4_cid",
    NUM_PROD_HIERARCHY5: "l5_cid",
    STR_NUM_PROD_HIERARCHY0: "l0_cid",
    STR_NUM_PROD_HIERARCHY1: "l1_cid",
    STR_NUM_PROD_HIERARCHY2: "l2_cid",
    STR_NUM_PROD_HIERARCHY3: "l3_cid",
    STR_NUM_PROD_HIERARCHY4: "l4_cid",
    STR_NUM_PROD_HIERARCHY5: "l5_cid",
}

PH_NAME_MAPPING = {
    BRAND: "brand",
    NUM_BRAND: "brand",
    STR_NUM_BRAND: "brand",
    COLOR: "color_id",
    NUM_COLOR: "color_id",
    STR_NUM_COLOR: "color_id",
    STYLE_COLOR: "product_id",
    NUM_STYLE_COLOR: "product_id",
    STR_NUM_STYLE_COLOR: "product_id",
    PROD_HIERARCHY0: "l0_cuq",
    PROD_HIERARCHY1: "l1_cuq",
    PROD_HIERARCHY2: "l2_cuq",
    PROD_HIERARCHY3: "l3_cuq",
    PROD_HIERARCHY4: "l4_cuq",
    PROD_HIERARCHY5: "l5_cuq",
    LIFECYCLE_INDICATOR: "lifecycle_indicator",
    NUM_PROD_HIERARCHY0: "l0_cuq",
    NUM_PROD_HIERARCHY1: "l1_cuq",
    NUM_PROD_HIERARCHY2: "l2_cuq",
    NUM_PROD_HIERARCHY3: "l3_cuq",
    NUM_PROD_HIERARCHY4: "l4_cuq",
    NUM_PROD_HIERARCHY5: "l5_cuq",
    STR_NUM_PROD_HIERARCHY0: "l0_cuq",
    STR_NUM_PROD_HIERARCHY1: "l1_cuq",
    STR_NUM_PROD_HIERARCHY2: "l2_cuq",
    STR_NUM_PROD_HIERARCHY3: "l3_cuq",
    STR_NUM_PROD_HIERARCHY4: "l4_cuq",
    STR_NUM_PROD_HIERARCHY5: "l5_cuq",
}

SH_ID_MAPPING = {
    STORE_HIERARCHY0: "s0_id",
    STORE_HIERARCHY1: "s1_id",
    STORE_HIERARCHY2: "s2_id",
    STORE_HIERARCHY3: "s3_id",
    STORE_HIERARCHY4: "s4_id",
    STORE_HIERARCHY5: "s5_id",
    STORE_HIERARCHY6: "store_code",
    NUM_STORE_HIERARCHY0: "s0_id",
    NUM_STORE_HIERARCHY1: "s1_id",
    NUM_STORE_HIERARCHY2: "s2_id",
    NUM_STORE_HIERARCHY3: "s3_id",
    NUM_STORE_HIERARCHY4: "s4_id",
    NUM_STORE_HIERARCHY5: "s5_id",
    NUM_STORE_HIERARCHY6: "store_code",
    STR_NUM_STORE_HIERARCHY0: "s0_id",
    STR_NUM_STORE_HIERARCHY1: "s1_id",
    STR_NUM_STORE_HIERARCHY2: "s2_id",
    STR_NUM_STORE_HIERARCHY3: "s3_id",
    STR_NUM_STORE_HIERARCHY4: "s4_id",
    STR_NUM_STORE_HIERARCHY5: "s5_id",
    STR_NUM_STORE_HIERARCHY6: "store_code",
}

SH_NAME_MAPPING = {
    STORE_HIERARCHY0: "s0_name",
    STORE_HIERARCHY1: "s1_name",
    STORE_HIERARCHY2: "s2_name",
    STORE_HIERARCHY3: "s3_name",
    STORE_HIERARCHY4: "s4_name",
    STORE_HIERARCHY5: "s5_name",
    STORE_HIERARCHY6: "store_name",
    NUM_STORE_HIERARCHY0: "s0_name",
    NUM_STORE_HIERARCHY1: "s1_name",
    NUM_STORE_HIERARCHY2: "s2_name",
    NUM_STORE_HIERARCHY3: "s3_name",
    NUM_STORE_HIERARCHY4: "s4_name",
    NUM_STORE_HIERARCHY5: "s5_name",
    NUM_STORE_HIERARCHY6: "store_name",
    STR_NUM_STORE_HIERARCHY0: "s0_name",
    STR_NUM_STORE_HIERARCHY1: "s1_name",
    STR_NUM_STORE_HIERARCHY2: "s2_name",
    STR_NUM_STORE_HIERARCHY3: "s3_name",
    STR_NUM_STORE_HIERARCHY4: "s4_name",
    STR_NUM_STORE_HIERARCHY5: "s5_name",
    STR_NUM_STORE_HIERARCHY6: "store_name",
}

HIERARCHY_NAME_MAPPING = {**PH_NAME_MAPPING, **SH_NAME_MAPPING}
HIERARCHY_ID_MAPPING = {**PH_ID_MAPPING, **SH_ID_MAPPING}

## Custom filter constants
# Valid screen names for custom filters
CUSTOM_FILTER_VALID_SCREENS = [
    "decision_dashboard",
    "workbench", 
    "marketing_calendar",
    "reporting",
    "product_configuration",
    "store_configuration"
]

 
