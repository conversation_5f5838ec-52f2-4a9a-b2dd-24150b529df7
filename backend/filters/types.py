from typing import TypedDict


class HierarchyFiltersInfoType(TypedDict):
    id_column: str
    value_column: str
    name_column: str
    id: int
    is_linked_to_event: bool
    is_linked_to_promo: bool
    is_linked_to_downloads: bool
    is_hierarchy: bool
    label: str

class ExcelConfigInfoType(TypedDict):
    value_column: str
    order: int

class ExclusionUploadConfigInfoType(TypedDict):
    value_column: str
    order: int