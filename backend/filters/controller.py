from fastapi import Request
from fastapi import status as http_status
from fastapi.routing import APIRouter
from filters import models as filter_models
from filters import service as filter_service
from pricesmart_common import constants as global_constants
from pricesmart_common import utils as common_utils
from app.dependencies import UserDependency
from typing import Optional

filters_router = APIRouter(tags=[global_constants.FILTERS_API_TAG])

@filters_router.post(
    path="/filters",
    response_model=filter_models.HierarchyFilterDataResponse,
)
async def get_hierarchies(items: filter_models.HierarchyFilters, request: Request):
    """
    Get hierarchy filter data.
    
    Args:
        items (HierarchyFilters): Filter criteria for hierarchies
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Hierarchy filter data
            - message (str): Success/error message
            - status (int): HTTP status code
            - user_id (str): User identifier
    """
    user_id = request.state.user_id

    result = await filter_service.get_hierarchy_service(items)
    return filter_models.HierarchyFilterDataResponse(
        message="Success", status=http_status.HTTP_200_OK, user_id=user_id, data=result
    )


@filters_router.post(
    path="/lifecycle",
    response_model=filter_models.HierarchyFilterDataResponse,
)
async def lifecycle_indicator(items: filter_models.HierarchyFilters, request: Request):
    """
    Get lifecycle indicator data.
    
    Args:
        items (HierarchyFilters): Filter criteria for lifecycle indicators
        request (Request): FastAPI request object
        
    Returns:
        HierarchyFilterDataResponse: Response containing:
            - data (dict): Lifecycle indicator data
            - message (str): Success/error message
            - status (int): HTTP status code
            - user_id (str): User identifier
    """
    user_id = request.state.user_id
    result = await filter_service.get_lifecycle_indicators(items)
    return filter_models.HierarchyFilterDataResponse(
        message="Success", status=http_status.HTTP_200_OK, user_id=user_id, data=result
    )


@filters_router.post(
    path="/product-filters"
)
async def get_product_filters(
    request_payload: filter_models.ProductFiltersRequest
):
    """
        Get distinct values from product master table based on specified column and filters.
    """

    data = await filter_service.get_product_filters(request_payload)
    return common_utils.create_response(
        data=data
    )

@filters_router.post(
    path="/store-filters"
)
async def get_store_filters(
    request_payload: filter_models.StoreFiltersRequest
):
    data = await filter_service.get_store_filters(request_payload)
    return common_utils.create_response(
        data=data
    )


@filters_router.post(
    path="/lifecycle-indicators"
)
async def get_lifecycle_indicators(
    request_payload: filter_models.LifeCycleIndicatorsRequest
):
    data = await filter_service.get_lifecycle_indicators_v2(request_payload)
    return common_utils.create_response(
        data=data
    )

@filters_router.post("/currency-filter")
async def get_currency_filter(
    request_payload: filter_models.CurrencyFilter
):
    data = await filter_service.get_currency_filter(request_payload)
    return common_utils.create_response(
        data=data
    )

@filters_router.post("/priority-filter")
async def get_priority_filter(
    request_payload: filter_models.PriorityFilter
):
    data = await filter_service.get_priority_filter(request_payload)
    return common_utils.create_response(data=data)


# Custom Filters Endpoints
@filters_router.post(
    path="/custom-filters",
)
async def create_custom_filter(
    filter_data: filter_models.CustomFilter,
    user_id: UserDependency
):
    """
    Create a new custom filter for the authenticated user.
    
    This endpoint allows users to save their filter configurations for reuse.
    Filters can be personal (visible only to the creator) or global (visible to all users with appropriate permissions).
    
    Args:
        filter_data (CustomFilter): The filter configuration data including:
            - filter_name: Unique name for the filter
            - description: Optional description
            - scope: "personal" or "global" (requires permissions for global)
            - screen_name: Specific screen where filter applies (optional for multi-screen)
            - is_multi_screen: Whether filter applies to all screens
            - filter_config: JSON configuration data
            - is_default: Whether to set as default for the screen
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Created filter details with filter_id
            - message (str): Success message
            - status (int): HTTP status code (201)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNameAlreadyExistsException: If filter name already exists
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
    """
    result = await filter_service.create_custom_filter_service(filter_data, user_id)

    return common_utils.create_response(
        message="Custom filter created successfully",
        status=http_status.HTTP_201_CREATED,
        user_id=user_id,
        data=result
    )


@filters_router.get(
    path="/custom-filters",
)
async def get_custom_filters(
    user_id: UserDependency,
    scope: Optional[str] = None,
    screen_name: Optional[str] = None,
    is_default: Optional[bool] = None
):
    """
    Retrieve custom filters for the authenticated user with optional filtering.
    
    This endpoint returns saved filters that the user can access, with support for filtering
    by scope (personal/global), screen name, and default status.
    
    Args:
        user_id (UserDependency): The authenticated user ID
        scope (Optional[str]): Filter by scope - "personal" or "global"
        screen_name (Optional[str]): Filter by specific screen name
        is_default (Optional[bool]): Filter by default status - True/False
        
    Returns:
        dict: Response containing:
            - data (List[dict]): List of filter objects with details
            - message (str): Success message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Note:
        - Personal filters are only visible to their creators
        - Global filters are visible to all users
        - is_default field indicates if filter is set as default for the user's screen
    """
    result = await filter_service.get_custom_filters_service(user_id, scope, screen_name, is_default)
    
    return common_utils.create_response(
        message="Custom filters retrieved successfully",
        status=http_status.HTTP_200_OK,
        user_id=user_id,
        data=result
    )


@filters_router.get(
    path="/custom-filters/{filter_id}",
)
async def get_custom_filter_by_id(
    filter_id: int,
    user_id: UserDependency
):
    """
    Retrieve a specific custom filter by its ID.
    
    This endpoint returns detailed information about a single filter, including
    its configuration and default status for the authenticated user.
    
    Args:
        filter_id (int): The unique identifier of the filter to retrieve
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Complete filter details including configuration
            - message (str): Success message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        
    Note:
        - Users can only access their own personal filters
        - Global filters are accessible to all users
        - is_default field indicates if filter is set as default for the user's screen
    """
    result = await filter_service.get_custom_filter_by_id_service(filter_id, user_id)
        
    return common_utils.create_response(
        message="Custom filter retrieved successfully",
        status=http_status.HTTP_200_OK,
        user_id=user_id,
        data=result
    )


@filters_router.put(
    path="/custom-filters/{filter_id}",
)
async def update_custom_filter(
    filter_id: int,
    filter_data: filter_models.UpdateCustomFilterRequest,
    user_id: UserDependency
):
    """
    Update an existing custom filter.
    
    This endpoint allows users to modify their saved filter configurations.
    Only the creator can update personal filters, while global filters require appropriate permissions.
    
    Args:
        filter_id (int): The unique identifier of the filter to update
        filter_data (UpdateCustomFilterRequest): The updated filter data (all fields optional):
            - filter_name: New name for the filter
            - description: Updated description
            - scope: "personal" or "global" (requires permissions for global)
            - screen_name: Updated screen name
            - is_multi_screen: Updated multi-screen setting
            - filter_config: Updated JSON configuration
            - is_default: Whether to set as default for the screen
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Updated filter details
            - message (str): Success message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
    """
    result = await filter_service.update_custom_filter_service(filter_id, filter_data, user_id)
    
    return common_utils.create_response(
        message="Custom filter updated successfully",
        status=http_status.HTTP_200_OK,
        user_id=user_id,
        data=result
    )


@filters_router.delete(
    path="/custom-filters/{filter_id}",
)
async def delete_custom_filter(
    filter_id: int,
    user_id: UserDependency
):
    """
    Delete a custom filter.
    
    This endpoint permanently removes a saved filter. Only the creator can delete personal filters,
    while global filters require appropriate permissions. If the deleted filter was set as default,
    the default status is automatically cleared.
    
    Args:
        filter_id (int): The unique identifier of the filter to delete
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Deletion confirmation details
            - message (str): Success message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Deletion is permanent and cannot be undone
        - If the filter was set as default, the default status is automatically removed
    """
    result = await filter_service.delete_custom_filter_service(filter_id, user_id)
    
    return common_utils.create_response(
        message="Custom filter deleted successfully",
        status=http_status.HTTP_200_OK,
        user_id=user_id,
        data=result
    )


@filters_router.post(
    path="/custom-filters/{filter_id}/copy",
)
async def copy_custom_filter(
    filter_id: int,
    copy_filter_data: filter_models.CopyFilterRequest,
    user_id: UserDependency
):
    """
    Create a copy of an existing custom filter.
    
    This endpoint allows users to duplicate an existing filter with a new name and optional modifications.
    The copied filter inherits all configuration from the original but can have different name, screen, and default settings.
    
    Args:
        filter_id (int): The unique identifier of the filter to copy
        copy_filter_data (CopyFilterRequest): The copy configuration:
            - filter_name: New name for the copied filter (required)
            - screen_name: Screen name for the copy (optional)
            - is_default: Whether to set the copy as default (optional)
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): New filter details with filter_id
            - message (str): Success message
            - status (int): HTTP status code (201)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNotFoundException: If source filter doesn't exist or user lacks access
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        
    Note:
        - The copied filter becomes a personal filter owned by the user
        - All configuration data is duplicated from the original
        - Users can copy both personal and global filters
    """
    result = await filter_service.copy_custom_filter_service(filter_id, copy_filter_data, user_id)
    
    return common_utils.create_response(
        message="Custom filter copied successfully",
        status=http_status.HTTP_201_CREATED,
        user_id=user_id,
        data=result
    )

@filters_router.get(
    path="/custom-filters/default/{screen_name}",
)
async def get_default_filter_for_screen(
    screen_name: str,
    user_id: UserDependency
):
    """
    Get the default filter for a specific screen.
    
    This endpoint retrieves the user's default filter for a given screen, with priority given to
    personal filters over global filters. If no default is set, returns null.
    
    Args:
        screen_name (str): The screen name to get the default filter for
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Default filter data or None if no default filter exists
            - message (str): Success/error message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Note:
        - Personal filters take priority over global filters
        - Returns null if no default filter is set for the screen
        - Only returns filters that are not deleted
        - Multi-screen filters cannot be set as defaults
    """
    result = await filter_service.get_default_filter_for_screen_service(screen_name, user_id)
    
    return common_utils.create_response(
        message="Default filter retrieved successfully" if result else "No default filter found",
        status=http_status.HTTP_200_OK,
        user_id=user_id,
        data=result
    )

@filters_router.post(
    path="/custom-filters/default",
)
async def update_default_filter(
    request_data: filter_models.SetDefaultFilterRequest,
    user_id: UserDependency
):
    """
    Set or unset a filter as the default for a specific screen.
    
    This endpoint allows users to manage their default filter preferences for different screens.
    When setting a filter as default, any existing default for that screen is automatically unset.
    Only one filter can be set as default per screen per user.
    
    Args:
        request_data (SetDefaultFilterRequest): The request data containing:
            - filter_id: The filter ID to set or unset as default
            - screen_name: The screen name where this filter should be set as default
            - is_default: True to set as default, False to unset as default
        user_id (UserDependency): The authenticated user ID
        
    Returns:
        dict: Response containing:
            - data (dict): Success message and filter details
            - message (str): Success/error message
            - status (int): HTTP status code (200)
            - user_id (str): User identifier
            
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterInvalidOperationException: If trying to set multi-screen filters as default
        
    Note:
        - Setting a filter as default automatically unsets any existing default for that screen
        - Only one filter can be set as default per screen per user
        - Multi-screen filters cannot be set as defaults
        - Users can only set defaults for filters they have access to
    """
    result = await filter_service.update_default_filter_service(
        request_data.filter_id, 
        request_data.screen_name,
        request_data.is_default,
        user_id
    )
    
    return common_utils.create_response(
        message=result["message"],
        status=http_status.HTTP_200_OK,
        user_id=user_id
    )
