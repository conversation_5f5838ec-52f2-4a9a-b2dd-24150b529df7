GET_HIERARCHY_FILTERS_QUERY = """ 
    select  
        {select_str}
    from 
        {client_schema}.{table_name} {table_alias}
    {where_str}
    group by 
    {group_by_str}
    order by 
        {order_by_str}
"""

GET_HIERARCHY_FILTERS_QUERY_V2 = """ 
    select  
        distinct {select_str}
    from 
        {client_schema}.{table_name} {table_alias}
    where 
        {value_column} is not null
        and
        {table_alias}.is_active = 1
        and
        {where_str}
    order by 
        {order_by_str}
"""

GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT_TYPE = """
    with event_hierarchy_cte as (
        select
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 0) as l0_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 1) as l1_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 2) as l2_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 3) as l3_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 4) as l4_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = -1) as brand_ids
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    select
        {select_str}
    from {client_schema}.{table_name} {table_alias},
    event_hierarchy_cte ehc
    {where_str}
    and (ehc.l0_ids is null or l0_cid = any(ehc.l0_ids))
    and (ehc.l1_ids is null or l1_cid = any(ehc.l1_ids))
    and (ehc.l2_ids is null or l2_cid = any(ehc.l2_ids))
    and (ehc.l3_ids is null or l3_cid = any(ehc.l3_ids))
    and (ehc.l4_ids is null or l4_cid = any(ehc.l4_ids))
    and (ehc.brand_ids is null or brand_cid = any(ehc.brand_ids))
    group by 
        {group_by_str}
    order by 
        {order_by_str}
"""


GET_PRODUCT_HIERARCHY_BASED_ON_WHOLE_CATEGORY_EVENT = """
    with event_hierarchy_cte as (
        select
            {event_select_str}
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    select
        distinct {select_str}
    from {client_schema}.{table_name} {table_alias},
    event_hierarchy_cte ehc
    where {where_str}
    and {table_alias}.is_active = 1
    and {table_alias}.{value_column} is not null
    order by 
        {order_by_str}
"""

GET_LIFECYCLE_INDIACTORS = """
    with specific_cte as (
        SELECT 
            gb.id AS value,
            gb.lifecycle_indicator as label
        FROM
            global.tb_lifecycle_indicator_config gb
        JOIN
            price_promo.product_master pm ON gb.lifecycle_indicator = pm.lifecycle_indicator
        {where_str}
        group by 
            gb.id,
            gb.lifecycle_indicator
    ),
    non_specific_cte as (
            SELECT
                id as value,
                lifecycle_indicator as label
            FROM 
                global.tb_lifecycle_indicator_config
    )
    SELECT * FROM 
        {curr_cte}
    order by 
        value
"""

GET_LIFECYCLE_INDIACTORS_V2 = """
    SELECT 
        distinct
        gb.id AS value,
        gb.lifecycle_indicator as label
    FROM
        global.tb_lifecycle_indicator_config gb
    JOIN
        price_promo.product_master pm ON gb.lifecycle_indicator = pm.lifecycle_indicator
    where {where_str}
    order by value
"""


GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT = """
    with event_hierarchy_cte as (
        select
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 0) as l0_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 1) as l1_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 2) as l2_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 3) as l3_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = 4) as l4_ids,
            array_agg(hierarchy_value_id) filter (where hierarchy_level_id = -1) as brand_ids
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    ),
    specific_cte as (
        SELECT 
            gb.id AS value,
            gb.lifecycle_indicator as label
        FROM
            global.tb_lifecycle_indicator_config gb
        JOIN
            price_markdown.product_master pm 
        ON gb.lifecycle_indicator = pm.lifecycle_indicator,
        event_hierarchy_cte ehc
        {where_str}
        and (ehc.l0_ids is null or l0_cid = any(ehc.l0_ids))
        and (ehc.l1_ids is null or l1_cid = any(ehc.l1_ids))
        and (ehc.l2_ids is null or l2_cid = any(ehc.l2_ids))
        and (ehc.l3_ids is null or l3_cid = any(ehc.l3_ids))
        and (ehc.l4_ids is null or l4_cid = any(ehc.l4_ids))
        and (ehc.brand_ids is null or brand_cid = any(ehc.brand_ids))
        group by 
            gb.id,
            gb.lifecycle_indicator
    ),
    non_specific_cte as (
        SELECT
            id as value,
            lifecycle_indicator as label
        FROM 
            global.tb_lifecycle_indicator_config
    )
    SELECT * FROM 
        {curr_cte}
    order by 
        value
"""

GET_LIFECYCLE_INDIACTORS_BASED_ON_EVENT_V2 = """
    with event_hierarchy_cte as (
        select
            {event_select_str}
        from 
            price_promo.included_event_product_hierarchy
        where event_id = {event_id}
    )
    SELECT 
        distinct
        gb.id AS value,
        gb.lifecycle_indicator as label
    FROM
        global.tb_lifecycle_indicator_config gb
    JOIN
        price_markdown.product_master pm 
    ON gb.lifecycle_indicator = pm.lifecycle_indicator,
    event_hierarchy_cte ehc
    where {where_str}
    order by value
"""

GET_CURRENCY_FILTER = """
    with country_currencies as (
        select 
            array_agg(currency_id) as currency_ids
        from 
            global.tb_country_currency_mapping 
        where 
            country_id = ANY({country_ids})
    )
    select 
        cm.currency_name as label,
        cm.currency_id as value,
        cm.currency_symbol as symbol
    from 
        price_promo.fn_get_target_currency_ids((select currency_ids from country_currencies)) tci
    inner join
        global.tb_currency_master cm on tci.target_currency_id = cm.currency_id
    order by tci.priority_number
"""


# Custom Filters Queries


INSERT_CUSTOM_FILTER = """
INSERT INTO {promo_schema}.custom_filters (
    filter_name, 
    description, 
    scope, 
    screen_name, 
    is_multi_screen,
    filter_config, 
    created_by,
    created_at,
    updated_at
) VALUES (
    {filter_name},
    {description},
    {scope},
    {screen_name},
    {is_multi_screen},
    {filter_config}::jsonb,
    {created_by},
    timezone({timezone}, now()),
    timezone({timezone}, now())
)
RETURNING filter_id;
"""

GET_CUSTOM_FILTERS = """
SELECT 
    cf.filter_id,
    cf.filter_name,
    cf.description,
    cf.scope,
    cf.screen_name,
    cf.is_multi_screen,
    cfd.filter_id IS NOT NULL as is_default
FROM {promo_schema}.custom_filters cf
LEFT JOIN {promo_schema}.custom_filters_defaults cfd 
    ON cf.filter_id = cfd.filter_id 
    AND cfd.user_id = {user_id}
    AND cf.screen_name = cfd.screen_name
WHERE cf.is_deleted = FALSE
    {scope_filter}
    {screen_filter}
ORDER BY cf.created_at DESC;
"""

GET_CUSTOM_FILTER_BY_ID = """
SELECT 
    cf.*
FROM {promo_schema}.custom_filters cf
WHERE cf.filter_id = {filter_id} 
    AND cf.is_deleted = FALSE
    AND ((cf.scope = 'personal' AND cf.created_by = {user_id}) OR cf.scope = 'global');
"""

UPDATE_CUSTOM_FILTER_PARTIAL = """
UPDATE {promo_schema}.custom_filters 
SET 
    {update_fields},
    updated_by = {updated_by},
    updated_at = timezone({timezone}, now())
WHERE filter_id = {filter_id} 
    AND is_deleted = FALSE
    AND ((scope = 'personal' AND created_by = {user_id}) OR scope = 'global');
"""

DELETE_CUSTOM_FILTER = """
UPDATE {promo_schema}.custom_filters 
SET is_deleted = TRUE, updated_by = {updated_by}, updated_at = timezone({timezone}, now())
WHERE filter_id = {filter_id} 
    AND is_deleted = FALSE
    AND ((scope = 'personal' AND created_by = {user_id}) OR scope = 'global');
"""

# User Default Filter Preferences Queries

DELETE_CUSTOM_FILTER_DEFAULT_BY_FILTER_ID = """
DELETE FROM {promo_schema}.custom_filters_defaults
WHERE user_id = {user_id} AND filter_id = {filter_id};
"""

DELETE_AND_INSERT_CUSTOM_FILTER_DEFAULTS = """
DELETE FROM {promo_schema}.custom_filters_defaults
WHERE user_id = {user_id} AND screen_name IN ({screen_values});

INSERT INTO {promo_schema}.custom_filters_defaults (
    user_id,
    screen_name,
    filter_id
) VALUES {insert_values};
"""

DELETE_CUSTOM_FILTER_DEFAULTS = """
DELETE FROM {promo_schema}.custom_filters_defaults
WHERE user_id = {user_id} AND filter_id = {filter_id} AND screen_name IN ({screen_names});
"""

# Updated query to use only the preferences table (no is_default logic)
GET_DEFAULT_FILTER_FOR_SCREEN = """
SELECT 
    cf.filter_id,
    cf.filter_name,
    cf.description,
    cf.scope,
    cf.screen_name,
    cf.is_multi_screen,
    cf.filter_config
FROM {promo_schema}.custom_filters_defaults cfd
INNER JOIN {promo_schema}.custom_filters cf ON cfd.filter_id = cf.filter_id
    AND cfd.user_id = {user_id}
    AND cfd.screen_name = {screen_name}
WHERE cf.is_deleted = FALSE
ORDER BY cf.created_at DESC;
"""

# Check if user has write permissions for global filters (configurable role codes via CTE)
CHECK_GLOBAL_FILTER_WRITE_PERMISSIONS = """
WITH eligible_role_codes AS (
    SELECT jsonb_array_elements_text(config_value::jsonb)::int as role_code
    FROM {promo_schema}.tb_tool_configurations 
    WHERE module = {filter_module}
      AND config_name = {global_filter_write_eligible_role_codes_config}
)
SELECT am.role_code
FROM {global_schema}.user_access_hierarchy_mapping uahm
LEFT JOIN {global_schema}.acl_master am ON am.acl_code = uahm.acl_code
INNER JOIN eligible_role_codes ON eligible_role_codes.role_code = am.role_code
WHERE uahm.user_code = {user_id} 
    AND am.application_code = {application_code}
"""

GET_CURENCY_IDS_USING_PROMO_ID = """
    select 
        cm.currency_name as label,
        cm.currency_id as value,
        cm.currency_symbol as symbol
    from
        price_promo.fn_get_target_currency_ids(
            (select 
                array_agg(distinct currency_id) 
            from 
                price_promo.promo_master 
            where 
                promo_id = {promo_id}) 
        ) tci
    inner join
        global.tb_currency_master cm on tci.target_currency_id = cm.currency_id
    order by tci.priority_number
"""

GET_PRIORITY_FILTER = """
    select distinct
        pr.priority_number as value,
        COALESCE(pn.priority_display_name, 'Unknown Priority') as label
    from 
        price_promo.promo_master pm
    inner join
        price_promo.ps_rules pr on pm.promo_id = pr.promo_id
    JOIN price_promo.tb_priority_number pn ON pr.priority_number = pn.priority_number
    where
        {where_str}
"""