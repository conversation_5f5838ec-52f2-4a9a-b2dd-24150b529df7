from filters import models, data as filters_data_module
from filters import constants as filter_constants
from typing import List, Optional, Dict, Any
from logger.logger import logger
from filters import exceptions as filter_exceptions

async def get_hierarchy_service(request_payload: models.HierarchyFilters):
    if request_payload.hierarchy_type in filter_constants.PRODUCT_HIERARCHIES:
        return await filters_data_module.get_product_hierarchy_data(request_payload)
    else:
        return await filters_data_module.get_store_hierarchy_data(request_payload)


async def get_lifecycle_indicators(request: models.HierarchyFilters):
    return await filters_data_module.get_lifecycle_indicators(request)


async def get_product_filters(request_payload: models.ProductFiltersRequest):
    return await filters_data_module.get_product_filters(request_payload)

async def get_lifecycle_indicators_v2(request_payload: models.LifeCycleIndicatorsRequest):
    return await filters_data_module.get_lifecycle_indicators_v2(request_payload)

async def get_store_filters(request_payload: models.StoreFiltersRequest):
    return await filters_data_module.get_store_filters(request_payload)

async def get_currency_filter(request_payload: models.CurrencyFilter):
    data = await filters_data_module.get_currency_filter(request_payload)
    return data

async def get_priority_filter(request_payload: models.PriorityFilter):
    return await filters_data_module.get_priority_filter(request_payload)

async def create_custom_filter_service(
    filter_data: models.CustomFilter,
    user_id: int
) -> Dict[str, Any]:
    """
    Create a new custom filter with business logic validation.
    
    This service function handles the creation of custom filters with proper validation
    for global filter permissions and business rules. It delegates the actual database
    operation to the data layer.
    
    Args:
        filter_data (CustomFilter): The filter configuration data including:
            - filter_name: Unique name for the filter
            - description: Optional description
            - scope: "personal" or "global" (requires permissions for global)
            - screen_name: Specific screen where filter applies
            - is_multi_screen: Whether filter applies to all screens
            - filter_config: JSON configuration data
            - is_default: Whether to set as default for the screen
        user_id (int): The authenticated user ID
        
    Returns:
        Dict[str, Any]: Created filter details with filter_id
        
    Raises:
        CustomFilterNameAlreadyExistsException: If filter name already exists
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Validates global filter permissions before creation
        - Delegates database operations to data layer
        - Handles default filter setting if requested
        - Logs successful creation for audit purposes
    """
    # Validate permissions for global scope
    if filter_data.scope == "global":
        await filters_data_module.validate_global_filter_write_permissions(user_id)
    
    result = await filters_data_module.create_custom_filter(filter_data, user_id)
    
    logger.info(f"Created custom filter with ID {result['filter_id']} for user {user_id}")
    
    return result


async def get_custom_filters_service(
    user_id: int,
    scope: Optional[str] = None,
    screen_name: Optional[str] = None,
    is_default: Optional[bool] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve custom filters with business logic and filtering.
    
    This service function retrieves custom filters that the user can access, with support
    for filtering by scope, screen name, and default status. It handles the business logic
    for determining which filters are accessible to the user.
    
    Args:
        user_id (int): The authenticated user ID
        scope (Optional[str]): Filter by scope - "personal", "global", or None for all
        screen_name (Optional[str]): Filter by specific screen name
        is_default (Optional[bool]): Filter by default status - True/False/None for all
        
    Returns:
        List[Dict[str, Any]]: List of filter objects with details including is_default status
        
    Note:
        - Personal filters are only returned for their creators
        - Global filters are returned for all users
        - is_default status is determined by user preferences
        - Results include complete filter configuration and metadata
        - Logs retrieval count for audit purposes
    """
    result = await filters_data_module.get_custom_filters(user_id, scope, screen_name, is_default)
    
    logger.info(f"Retrieved {len(result)} custom filters for user {user_id}")
    
    return result


async def get_custom_filter_by_id_service(
    filter_id: int,
    user_id: int
) -> Optional[Dict[str, Any]]:
    """
    Retrieve a specific custom filter by ID with access validation.
    
    This service function retrieves a specific filter and validates that the user
    has access to it. It handles the business logic for determining filter accessibility.
    
    Args:
        filter_id (int): The unique identifier of the filter to retrieve
        user_id (int): The authenticated user ID
        
    Returns:
        Optional[Dict[str, Any]]: Complete filter details including is_default status, or None if not found
        
    Note:
        - Users can only access their own personal filters
        - Global filters are accessible to all users
        - is_default status is determined by user preferences
        - Logs successful retrieval or not found status
        - Returns None if filter doesn't exist or user lacks access
    """
    result = await filters_data_module.get_custom_filter_by_id(filter_id, user_id)
    
    if result:
        logger.info(f"Retrieved custom filter {filter_id} for user {user_id}")
    else:
        logger.info(f"Custom filter {filter_id} not found for user {user_id}")
    
    return result


async def update_custom_filter_service(
    filter_id: int,
    filter_data: models.UpdateCustomFilterRequest,
    user_id: int
) -> Dict[str, Any]:
    """
    Update an existing custom filter with business logic validation.
    
    This service function handles the update of custom filters with proper validation
    for global filter permissions and business rules. It manages default filter changes
    and delegates database operations to the data layer.
    
    Args:
        filter_id (int): The unique identifier of the filter to update
        filter_data (UpdateCustomFilterRequest): The updated filter data (all fields optional):
            - filter_name: New name for the filter
            - description: Updated description
            - scope: "personal" or "global" (requires permissions for global)
            - screen_name: Updated screen name
            - is_multi_screen: Updated multi-screen setting
            - filter_config: Updated JSON configuration
            - is_default: Whether to set as default for the screen
        user_id (int): The authenticated user ID
        
    Returns:
        Dict[str, Any]: Updated filter details
        
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Validates global filter permissions before update
        - Only the creator can update personal filters
        - Handles default filter preference changes
        - Logs successful update for audit purposes
    """
    await filters_data_module.update_custom_filter(filter_id, filter_data, user_id)
    
    logger.info(f"Updated custom filter with ID {filter_id} for user {user_id}")


async def delete_custom_filter_service(
    filter_id: int,
    user_id: int
) -> Dict[str, Any]:
    """
    Delete a custom filter with business logic validation.
    
    This service function handles the deletion of custom filters with proper validation
    for permissions and business rules. It manages cleanup of default filter preferences
    and delegates database operations to the data layer.
    
    Args:
        filter_id (int): The unique identifier of the filter to delete
        user_id (int): The authenticated user ID
        
    Returns:
        Dict[str, Any]: Deletion confirmation details
        
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterInsufficientPermissionsException: If user lacks global filter permissions
        
    Note:
        - Only the creator can delete personal filters
        - Global filters require appropriate permissions
        - Automatically cleans up default filter preferences
        - Uses soft deletion to maintain data integrity
        - Logs successful deletion for audit purposes
    """
    await filters_data_module.delete_custom_filter(filter_id, user_id)
    
    logger.info(f"Deleted custom filter with ID {filter_id} for user {user_id}")


async def copy_custom_filter_service(
    filter_id: int,
    copy_filter_data: models.CopyFilterRequest,
    user_id: int
) -> Dict[str, Any]:
    """
    Create a copy of an existing custom filter with business logic validation.
    
    This service function handles the copying of custom filters with proper validation
    and business rules. It creates a personal copy of the original filter with the
    specified modifications.
    
    Args:
        filter_id (int): The unique identifier of the filter to copy
        copy_filter_data (CopyFilterRequest): The copy configuration:
            - filter_name: New name for the copied filter (required)
            - screen_name: Screen name for the copy (optional)
            - is_default: Whether to set the copy as default (optional)
        user_id (int): The authenticated user ID
        
    Returns:
        Dict[str, Any]: New filter details with filter_id
        
    Raises:
        CustomFilterNotFoundException: If source filter doesn't exist or user lacks access
        CustomFilterNameAlreadyExistsException: If new name conflicts with existing filter
        
    Note:
        - Users can copy both personal and global filters
        - The copied filter becomes a personal filter owned by the user
        - All configuration data is duplicated from the original
        - Handles default filter setting for the copy if requested
        - Logs successful copy for audit purposes
    """
    # Get existing filter for copy data
    existing_filter = await filters_data_module.get_custom_filter_by_id(filter_id, user_id)
    if not existing_filter:
        raise filter_exceptions.CustomFilterNotFoundException()
    
    result = await filters_data_module.copy_custom_filter(copy_filter_data, existing_filter, user_id)
    
    logger.info(f"Copied custom filter with ID {result['filter_id']} for user {user_id}")
    
    return result

async def get_default_filter_for_screen_service(
    screen_name: str,
    user_id: int
) -> Optional[Dict[str, Any]]:
    """
    Get the default filter for a screen with business logic.
    
    This service function retrieves the user's default filter for a given screen,
    handling the business logic for determining which filter should be returned.
    
    Args:
        screen_name (str): The screen name to get the default filter for
        user_id (int): The authenticated user ID
        
    Returns:
        Optional[Dict[str, Any]]: The default filter data with complete details, or None if no default exists
        
    Note:
        - Uses the custom_filters_defaults table for user preferences
        - Only returns non-deleted filters
        - Returns None if no default filter is set for the screen
        - Includes complete filter configuration and metadata
        - Logs retrieval or not found status for audit purposes
    """
    result = await filters_data_module.get_default_filter_for_screen(screen_name, user_id)
    
    if result:
        logger.info(f"Retrieved default filter for screen '{screen_name}' and user {user_id}")
    else:
        logger.info(f"No default filter found for screen '{screen_name}' and user {user_id}")
    
    return result

async def update_default_filter_service(
    filter_id: int,
    screen_name: str,
    is_default: bool,
    user_id: int
) -> Dict[str, Any]:
    """
    Set or unset a filter as the default for a specific screen based on is_default flag.
    
    This service function manages user default filter preferences with proper validation
    and business rules. It handles both setting and unsetting default filters based on
    the is_default flag.
    
    Args:
        filter_id (int): The filter ID to set or unset as default
        screen_name (str): The screen name where this filter should be set as default
        is_default (bool): True to set as default, False to unset as default
        user_id (int): The authenticated user ID
        
    Returns:
        Dict[str, Any]: The operation result with success message and details
        
    Raises:
        CustomFilterNotFoundException: If filter doesn't exist or user lacks access
        CustomFilterInvalidOperationException: If trying to set multi-screen filters as default
        
    Note:
        - Validates filter exists and user has access to it
        - Prevents multi-screen filters from being set as defaults
        - Setting a filter as default automatically unsets any existing default
        - Only one filter can be set as default per screen per user
        - Logs successful operations for audit purposes
    """
    # Verify the filter exists and user has access to it
    filter_data = await filters_data_module.get_custom_filter_by_id(filter_id, user_id)
    if not filter_data:
        raise filter_exceptions.CustomFilterNotFoundException()
    
    await filters_data_module.handle_user_default_filter_preferences(is_default, screen_name, filter_data["is_multi_screen"], filter_id, user_id)
    
    logger.info(f"Default filter updated successfully for screen '{screen_name}' and user {user_id}")
    
    return {"message": "Default filter updated successfully"}