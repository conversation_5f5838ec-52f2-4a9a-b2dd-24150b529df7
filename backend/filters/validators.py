"""
Common validation functions for filters module
"""

from typing import Optional
from filters.constants import CUSTOM_FILTER_VALID_SCREENS


def validate_screen_name_value(value: Optional[str]) -> Optional[str]:
    """
    Common screen name validation function
    
    Args:
        value: Screen name to validate
        
    Returns:
        Validated screen name or None
        
    Raises:
        ValueError: If screen name is invalid
    """
    if value is not None and value not in CUSTOM_FILTER_VALID_SCREENS:
        raise ValueError(f"Invalid screen: {value}. Valid screens: {CUSTOM_FILTER_VALID_SCREENS}")
    return value


def validate_default_requires_screen_name(value: bool, data: dict) -> bool:
    """
    Validate that screen_name is provided when is_default is True
    
    Args:
        value: The is_default value being validated
        data: The complete data dictionary containing all fields
        
    Returns:
        The validated is_default value
        
    Raises:
        ValueError: If is_default is True but screen_name is not provided
    """
    if value:
        screen_name = data.get("screen_name")
        
        # For default filters, screen_name is always required
        if not screen_name:
            raise ValueError("screen_name is required when is_default is True")
    
    return value 