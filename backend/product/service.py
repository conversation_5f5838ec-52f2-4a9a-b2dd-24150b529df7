from __future__ import annotations
from typing import Optional

from starlette import status

import product.data as product_data
from exceptions.exceptions import (
    NoStoreColumnException,
    InvalidTemplateException
)
from fastapi import UploadFile, HTTPException
from product import constants as product_constants
from product import utils as product_utils
from product import models as product_models
from pricesmart_common.data import get_config_value
from filters.types import HierarchyFiltersInfoType
from enums.Enums import ConfigModuleEnum,ConfigKeyEnum
from product.exceptions import MultipleCountriesNotAllowedException

async def get_product_details_service(request_payload: product_models.ProductListRequest):
    product_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    product_hierarchies = {
        product_hierarchy_config[hierarchy_key]["id_column"]: hierarchy_values
        for hierarchy_key,hierarchy_values in request_payload.product_hierarchies.items()
        if product_hierarchy_config[hierarchy_key]["id"] is not None
    }

    product_details = await product_data.get_product_details_data(
        request_payload,
        product_hierarchies
    )
    return product_details


async def get_store_details_service(
    request_payload: product_models.StoretListRequest
):
    store_hierarchy_config: dict[str,HierarchyFiltersInfoType] = await get_config_value(ConfigModuleEnum.STORE,ConfigKeyEnum.HIERARCHY_FILTERS)
    store_hierarchies = {
        store_hierarchy_config[hierarchy_key]["id_column"]: hierarchy_values
        for hierarchy_key,hierarchy_values in request_payload.store_hierarchies.items()
        if store_hierarchy_config[hierarchy_key]["id"] is not None
    }

    store_hierarchies["store_id"] = request_payload.store_code

    product_details = await product_data.get_store_details_data(
        request_payload,
    )
    return product_details


async def validate_and_get_sku_details_service(
    request_payload: product_models.ProductDetails
):
    products_excel_config_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.EXCEL_UPLOAD_CONFIGURATION
    )

    sku_ids_list = [sublist[0].strip() for sublist in request_payload.products_data]
    await validate_single_brand_service(sku_ids_list)
    return await product_data.validate_and_get_product_data(
        request_payload,
        products_excel_config_info,
    )


async def validate_and_get_store_details_service(
    request_payload: product_models.StoreDetails
):
    stores_excel_config_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.EXCEL_UPLOAD_CONFIGURATION
    )

    return await product_data.validate_and_get_store_data(
        request_payload,
        stores_excel_config_info
    )


async def get_offer_type_details_service():
    return await product_data.get_offer_type_details_data()


async def get_products_from_file(file: UploadFile,event_id: Optional[int]=None):
    product_excel_config_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.PRODUCT,
        ConfigKeyEnum.EXCEL_UPLOAD_CONFIGURATION
    )

    df = await product_utils.get_data_frame_from_file(file)
    df.columns = [col.strip().lower() for col in df.columns]
    
    missing_columns_in_df = set(product_excel_config_info.keys()) - set(df.columns)
    additional_columns_in_df = set(df.columns) - set(product_excel_config_info.keys())

    if len(additional_columns_in_df) or len(missing_columns_in_df):
        raise InvalidTemplateException

    if df.empty:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No SKU mapping found in the file."
        )
    
    excel_upload_column_name = sorted(product_excel_config_info, key=lambda k: product_excel_config_info[k]["order"])[0]

    sku_ids_list = [
        str(sku_id)
        for sku_id in df[excel_upload_column_name].to_list()
    ]

    await validate_single_brand_service(sku_ids_list)
    
    products_data = product_utils.create_tuples_for_upload_xl(product_excel_config_info, df)
    return await product_data.validate_and_get_product_data(
        product_models.ProductDetails(products_data=products_data,event_id=event_id),
        product_excel_config_info,
    )


async def get_stores_from_file(file: UploadFile,event_id: Optional[int]):
    stores_excel_config_info: dict[str,HierarchyFiltersInfoType] = await get_config_value(
        ConfigModuleEnum.STORE,
        ConfigKeyEnum.EXCEL_UPLOAD_CONFIGURATION
    )

    df = await product_utils.get_data_frame_from_file(file)
    df.columns = [col.strip().lower() for col in df.columns]

    missing_columns_in_df = set(stores_excel_config_info.keys()) - set(df.columns)
    additional_columns_in_df = set(df.columns) - set(stores_excel_config_info.keys())

    if len(additional_columns_in_df) or len(missing_columns_in_df):
        raise InvalidTemplateException

    
    if df.empty:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No store mapping found in the file.")

    stores_data = product_utils.create_tuples_for_upload_xl(stores_excel_config_info, df)

    return await product_data.validate_and_get_store_data(
        product_models.StoreDetails(stores_data=stores_data,event_id=event_id), stores_excel_config_info
    )


async def get_customer_type():
    return await product_data.get_customer_type()


async def get_distribution_channel_type():
    return await product_data.get_distribution_channel_type()


async def get_config_details_service(config):
    return await product_data.get_config_details_data(config)

async def get_product_hierarchy_id_mapping()-> dict[int,HierarchyFiltersInfoType]:
    product_hierarchy_config = await get_config_value(ConfigModuleEnum.PRODUCT,ConfigKeyEnum.HIERARCHY_FILTERS)
    return {
        hierarchy_value["id"]: hierarchy_value
        for hierarchy_value in product_hierarchy_config.values()
    }

async def validate_single_brand_service(sku_ids: list[str]):
    sku_ids = list(set(sku_ids))
    unique_brands_count = await product_data.get_unique_brands_count(sku_ids)
    if unique_brands_count > 1:
        raise MultipleCountriesNotAllowedException()