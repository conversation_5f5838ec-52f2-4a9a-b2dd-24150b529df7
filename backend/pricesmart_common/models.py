from datetime import date,datetime
from typing import Annotated, Any, Optional
from fastapi import status as http_status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import AfterValidator, BaseModel, Field, PlainSerializer, ValidationInfo, field_validator
from enums.Enums import EnhancedFilteringMetricsEnum
from pricesmart_common import constants as common_constants
from pricesmart_common.validators import validate_priority_numbers
from typing import Literal


class BaseResponseBody(BaseModel):
    message: Optional[str] = ""
    status: int = http_status.HTTP_200_OK
    success: bool = True
    user_id: Optional[int] = None
    data: Optional[Any] = None
    error: Optional[Any] = None


class BaseJSONResponse(JSONResponse):
    def __init__(
        self,
        status_code: int = http_status.HTTP_200_OK,
        message: str = "",
        success: bool = True,
        user_id: Optional[int] = None,
        data: Optional[Any] = None,
        error: Optional[Any] = None,
        headers: Optional[dict[str, str]] = None,
        media_type: Optional[str] = None,
        background: Optional[Any] = None,
    ) -> None:
        content = BaseResponseBody(
            message=message,
            status=status_code,
            success=success,
            user_id=user_id,
            data=data if data is not None else [],
            error=error,
        )
        super().__init__(
            content=jsonable_encoder(content),
            status_code=status_code,
            headers=headers,
            media_type=media_type,
            background=background,
        )

CustomDate = Annotated[
    date, PlainSerializer(lambda x: x.strftime(common_constants.PYTHON_DATE_FORMAT), return_type=str, when_used='json')
]

CustomDateTime = Annotated[
    datetime, PlainSerializer(lambda x: x.strftime(common_constants.PYTHON_DATETIME_FORMAT), return_type=str, when_used='json')
]

PriorityNumbers = Annotated[
    Optional[list[int]],
    AfterValidator(validate_priority_numbers),
    Field(default=None, validate_default=True)
]

class BaseFilters(BaseModel):
    start_date: date
    end_date: date
    show_partially_overlapping_events: bool = True
    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    event_ids: Optional[list[int]] = None

class OptionalBaseFilters(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    show_partially_overlapping_events: bool = True
    metrics_display_mode: EnhancedFilteringMetricsEnum = EnhancedFilteringMetricsEnum.ENTIRE_OFFER_DURATION
    product_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    store_hierarchies: dict[str,list[int]] = Field(default_factory=dict)
    target_currency_id: Optional[int] = None
    priority_numbers: PriorityNumbers

class TableFilters(BaseModel):
    column_name: str
    operator: str
    value: Optional[str] = None
    value1: Optional[int] = None
    value2: Optional[int] = None
    type: Literal["string", "numeric"] = "string"

    @field_validator("value")
    def escape_sql_wildcards(cls, v: str, info: ValidationInfo) -> str:
        # Escape special characters used in LIKE patterns for PostgreSQL
        if (
            isinstance(v, str) and 
            info.data.get("operator") in (
                "contains",
                "not_contains",
                "starts_with",
                "ends_with"
            )
        ):
            v = v.replace(
                    "\\", "\\\\"
                ).replace(
                    "_", "\\_"
                ).replace(
                    "%", "%%"
                )

        return v