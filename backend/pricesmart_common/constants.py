from datetime import datetime, timedelta, timezone
from typing import Union
import pytz

# constants.py
SWAGGER_UI_DEFAULT_MODELS_EXPAND_DEPTH_COLLAPSED = -1
SWAGGER_UI_DEFAULT_MODELS_EXPAND_DEPTH_SHOWN = 0
SWAGGER_UI_DEFAULT_MODELS_EXPAND_DEPTH_ONE_LEVEL = 1
# Add more levels if needed

UTF_ENCODING_KEY = "utf-8"
EMAIL_ENDPOINTS = "https://api.mailgun.net/v3/impactanalytics.co/messages"
API_PREFIX = "/pricesmart/api/v1"
API_V3_PREFIX = "/pricesmart/api/v3"
MARKDOWN_API_PREFIX = "/pricesmart/markdown/api/v1"
BIGLOTS_URL_PREFIX = "https://saksfifthavenue.devs.impactsmartsuite.com"
API_TITLE = "Saks"
PROJECT_ID = "saksfifthavenue-27032024"
PROJECT_RESOURCE = "saksfifthavenue"

# CLIENT_TIMEZONE = "EST"
SUCCESS_MESSAGE = "Successful"
application_code = 21

#################   Database   ##################

BEGIN_TRANSACTION = "Begin Transaction;"
END_TRANSACTION = "End Transaction;"
ROLLBACK_TRANSACTION = "ROLLBACK;"
BEGIN_SP = "BEGIN"
COMMIT_SP = "COMMIT"
END_SP = "END"

# Date Format
PYTHON_DATE_FORMAT = "%Y-%m-%d"
DB_DATETIME_FORMAT = "mm/dd/yyyy HH24:mi:ss"
DB_DATE_FORMAT = "MM/dd/YYYY"
PYTHON_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"


# Time Zone
def get_client_timezone():
    # Get the current datetime in UTC timezone
    utc_now = datetime.now(timezone.utc)

    # Convert UTC time to Eastern Time (ET)
    eastern = pytz.timezone("America/New_York")
    eastern_now = utc_now.astimezone(eastern)

    # Check if the current time is in Eastern Standard Time (EST) or Eastern Daylight Time (EDT)
    if eastern_now.dst() == timedelta(hours=0):  # If DST is not in effect, it's EST
        return "EST"
    return "EDT"


CLIENT_TIMEZONE = get_client_timezone()
# CLIENT_TIMEZONE_FOR_PYTHON = "EST"
CLIENT_TIMEZONE_FOR_PYTHON = "US/Eastern"

MAINTENANCE_FLAG = "markdown_maintenance"
CACHE_ENABLED_FLAG = "enable_cache"


#################   Database Tables    ##################

# Price Promo Schema Tables
PRODUCT_MASTER = "product_master"
PROMO_MASTER = "promo_master"
FISCAL_DATE_MAPPING = "fiscal_date_mapping"
PS_RULES = "ps_rules"
TB_PLACEHOLDER_TARGETS = "tb_placeholder_targets"
TB_PLACEHOLDER_PRICING = "tb_placeholder_pricing"

PROMO_PRODUCT = "promo_product"
PROMO_PRODUCT_HIERARCHY = "promo_product_hierarchy"

PROMO_STORE = "promo_store"
PROMO_STORE_HIERARCHY = "promo_store_hierarchy"

TB_PROMO_PRODUCT_GROUPS = "tb_promo_product_groups"
PROMO_PRODUCT_PG_HIERARCHY = "promo_product_pg_hierarchy"

TB_PROMO_STORE_GROUPS = "tb_promo_store_groups"
PROMO_STORE_SG_HIERARCHY = "promo_store_sg_hierarchy"

BXGY_PERCENTAGE = "bxgy_percentage"
BUDGET_MASTER_SKU = "budget_master_sku"
BUDGET_MASTER_SKU_ECOM = "budget_master_sku_ecom"
STORE_SPLIT_OPT = "store_split_opt"

PROMO_STATUS_CONFIG = "promo_status_config"
PRODUCT_SELECTION_TYPE_CONFIG = "product_selection_type_config"
STORE_SELECTION_TYPE_CONFIG = "store_selection_type_config"
TB_OFFER_DISTRIBUTOR_CHANNEL_CONFIG = "tb_offer_distributor_channel_config"
TB_CUSTOMER_TYPE_CONFIG = "tb_customer_type_config"

EVENT_HIERARCHY_TABLE = "event_product_hierarchy"

# Global Schema Tables
TB_STORE_MASTER = "tb_store_master"

TB_PRODUCT_GROUP = "tb_product_group"
TB_PG_HIERARCHY = "tb_pg_hierarchy"
TB_PG_PRODUCT = "tb_pg_product"

TB_STORE_GROUP = "tb_store_group"
TB_SG_HIERARCHY = "tb_sg_hierarchy"
TB_SG_STORE = "tb_sg_store"

TB_GROUP_CONFIG = "tb_group_config"
TB_LIFECYCLE_INDICATOR_CONFIG = "tb_lifecycle_indicator_config"

################ FASTAPI ######################

# End-Points
PRODUCTS = "/products"
# Product Tags
PRODUCT_CONFIG = "product-config"
PRODUCT_FILTERS = "/filters/"
# API Tags
PRODUCT_API_TAG = "products"
PROMOTION_API_TAG = "promotions"
EVENT_API_TAG = "events"
FILTERS_API_TAG = "filters"
AUTHENTICATION_API_TAG = "authentication"
ACCESS_MANAGEMENT_API_TAG = "access_management"
SERVER_API_TAG = "server"
DASHBOARD = "decision_dashboard"
REPORTS_API_TAG = "reports"
USER_NOTIFY_TAG = "/user/notify"
DOWNLOADS = "/downloads"
COMMON_API_TAG = "common"

###################### Default - Dates - For get APIs #################
START_DATE = "default"
END_DATE = "default"
QUARTER = (
    ""  ### Possible values are : current,last , if nothing current will be considered
)

# list of general actions performed
ACTION_CREATE = "create"
ACTION_EDIT = "edit"
ACTION_DELETE = "delete"
ACTION_GET = "get"
ACTION_VALUES = [ACTION_CREATE, ACTION_EDIT, ACTION_DELETE, ACTION_GET]


def string_format_date_time(calendar_date: str) -> Union[datetime, str]:
    if calendar_date:
        month, today_date, year = calendar_date.split("/")
        new_date = datetime(year=int(year), month=int(month), day=int(today_date))
        return new_date
    return calendar_date


# Generic user ID that can be used for all system initiated operation
GENERIC_USER_ID = -1


ADVERTISED = 1
UNADVERTISED = 2


CRON_EMAIL_LIST = [
    "<EMAIL>",
]

WHERE_CONDITION = "where "
AND = " and "
UNION_ALL = " union all "

STORE_HIERARCHY_INT_MAPPING = {
    "s0_ids": 0,
    "s1_ids": 1,
    "s2_ids": 2,
    "s3_ids": 3,
    "s4_ids": 4,
    "s5_ids": 5
}


CACHED_ENV_FILE_PATH = ".cached_env"
APP_VERSION_HEADER = "Appversion"
APP_VERSION_MISMATCH = "Tool not on latest version of app. Please clear cache (Hard reload)"

LOCAL_ENV = "local"


USE_EVENTS = False
GLOBAL_EVENT_ID = -1

TEMPLATE_LOCATIONS = {
    'markdown_sku_store': (
        'pricesmart_common/excel_templates/markdown_sku_store_mapping_template.xlsx',
        'markdown_sku_store_mapping_template.xlsx'
    ),
    'markdown_product': (
        'pricesmart_common/excel_templates/markdown_product_selection_template.xlsx',
        'markdown_product_selection_template.xlsx'
    ),
    'markdown_store_ids': (
        'pricesmart_common/excel_templates/store_selection_template.xlsx',
        'store_selection_template.xlsx'
    ),
    'product_group_upload': (
        'pricesmart_common/excel_templates/promo_product_selection_template.xlsx',
        'product_selection_template.xlsx'
    ),
    'store_group_upload': (
        'pricesmart_common/excel_templates/store_selection_template.xlsx',
        'store_selection_template.xlsx'
    ),
    'promo_product_upload': (
        'pricesmart_common/excel_templates/promo_product_selection_template.xlsx',
        'promo_product_selection_template.xlsx'
    ),
    'promo_exclusion_upload': (
        'pricesmart_common/excel_templates/promo_product_exclusion_template.xlsx',
        'promo_product_exclusion_template.xlsx'
    ),
}

REPORT_DOWNLOAD_MESSAGE = "Request received. You will be notified once the downloads are ready."