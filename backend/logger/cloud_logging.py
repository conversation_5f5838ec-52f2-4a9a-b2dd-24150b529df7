import logging
import os
import sys
import uvicorn
from google.cloud.logging_v2.handlers import CloudLoggingHandler
import google.cloud.logging_v2 as glogging
from pricesmart_common.config.settings import get_settings
from configuration.environment import environment

settings = get_settings()
cur_dir = os.getcwd()

def configure_logging():
    log_config = uvicorn.config.LOGGING_CONFIG
    if settings.environment != environment.deployment_env:
        client = glogging.Client()
        log_config["handlers"].update({
            "stackdriver": {
                # "formatter": "access",
                "class": "google.cloud.logging_v2.handlers.CloudLoggingHandler",
                "client": client,
                "name": f"backend-{settings.environment}"
            }
        }
        )
        log_config["loggers"]["uvicorn"]["handlers"].append("stackdriver")
        log_config["loggers"]["uvicorn.access"]["handlers"].append("stackdriver")
    return log_config


class PackagePathFilter(logging.Filter):
    def filter(self, record):
        pathname = record.pathname
        record.relativepath = None
        abs_sys_paths = map(os.path.abspath, sys.path)
        for path in sorted(abs_sys_paths, key=len, reverse=True):  # longer paths first
            if not path.endswith(os.sep):
                path += os.sep
            if pathname.startswith(path):
                record.relativepath = os.path.relpath(pathname, path)
                break
        return True


def get_logger(module_name: str = None, log_name: str = None, msg=None):
    #log_config = configure_logging()
    #dictConfig(log_config)
    if module_name:
        logger_name = f"backend-{settings.environment}" if not log_name else f"backend-{settings.environment}-{log_name}"
        handler = CloudLoggingHandler(glogging.Client(), name=logger_name) \
            if settings.environment != environment.deployment_env else logging.StreamHandler()
        handler.addFilter(PackagePathFilter())
        formatter = logging.Formatter(
            '[%(asctime)s:%(relativepath)s:%(lineno)s:%(levelname)s] %(message)s'
        )
        handler.setFormatter(formatter)
        logger = logging.getLogger(module_name) if not log_name else logging.getLogger(logger_name)
        logger.addHandler(handler)
        logger.setLevel(logging.DEBUG)
    return logger