from io import Bytes<PERSON>
from datetime import datetime

import pandas as pd
import pytz
from configuration.environment import environment
from pricesmart_common.utils import async_execute_query
from pricesmart_common.utils import create_upload_file, send_email
from pricesmart_common import constants as global_constants
from scheduler import queries as sched_queries
from server_sent_events.utils import insert_notification
import client_configuration.constants as client_configuration_constants


def get_current_date_in_client_timezone():
    client_tz = pytz.timezone(global_constants.CLIENT_TIMEZONE_FOR_PYTHON)
    current_time = datetime.now(client_tz)
    return current_time.strftime('%d %B %Y')


def generate_excel_attachment(promos):
    """Generate an Excel file as an attachment from promo data."""
    # Convert promo data into a pandas DataFrame
    df = pd.DataFrame(promos)
    excel_buffer = BytesIO()

    # Write DataFrame to an Excel buffer
    with pd.ExcelWriter(excel_buffer, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name="PromoIDs")

    # Reset buffer's position to the beginning and read content
    excel_buffer.seek(0)
    excel_content = excel_buffer.read()

    # Create an UploadFile instance for the generated Excel attachment
    return create_upload_file("promo_ids.xlsx", excel_content)


async def send_success_email(message, attachment):
    """Send a success email with optional Excel attachment."""
    current_date = get_current_date_in_client_timezone()
    subject = f"[Success] [{environment.client_name.upper()}] [{environment.deployment_env.upper()}] [{current_date}] [{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.upper()}]- {message}!"
    message = f"{message} SUCCESSFUL."

    # Attachments are added only if not None
    attachments = [attachment] if attachment else []

    await send_email(subject, message, subtype="html", attachments=attachments)


async def send_failure_email(today, trace, message, attachment):
    """Send a failure email with error traceback and optional attachment."""
    current_date = get_current_date_in_client_timezone()
    subject = f"[Error] [{environment.client_name.upper()}] [{environment.deployment_env.upper()}] [{current_date}] [{client_configuration_constants.PROMO_IDENTIFIER_PRIMARY.upper()}]- {message}!"
    html_message = f"<p><b><i>Job Time : {str(today)}</i></b></p><pre>{trace}</pre>"

    # Attachments are added only if not None
    attachments = [attachment] if attachment else []

    await send_email(subject, html_message, subtype="html", attachments=attachments)


async def fetch_valid_promo_ids(select_str, where_str):
    """Fetch valid promo IDs from the promo_master table based on specified conditions."""
    query = f"""
        SELECT
            {select_str}
        FROM price_promo.promo_master
        WHERE
            {where_str}
        ;
    """
    print("=" * 50)
    print(query)
    print("=" * 50)
    # Execute the query asynchronously
    return await async_execute_query(query)


async def process_promos_with_users(promos):
    """Process promos by collecting unique user IDs (created_by, updated_by) associated with each promo."""
    filtered_promos = []

    for row in promos:
        promo_id = int(row["promo_id"])
        promo_name = row["promo_name"]

        # Collect unique user IDs from created_by and updated_by fields
        users = list({row["created_by"], row["updated_by"]})

        filtered_promos.append(
            {"promo_id": promo_id, "promo_name": promo_name, "users": users}
        )

    return filtered_promos


async def extract_promo_ids(promos):
    """Extract and return a list of promo IDs from the given list of promos."""
    return [int(row["promo_id"]) for row in promos if "promo_id" in row]


async def send_placeholder_offers_expiry_notification(promos, status):
    """Send notifications for placeholder promo expiry reminders."""
    for promo_detail in promos:
        promo_id = promo_detail["promo_id"]
        promo_name = promo_detail["promo_name"]
        header_text = "Placeholder Expiry Reminder"
        identifier = "Placeholder Expiry Reminder"

        # Construct message based on the status of the operation
        if status:
            message = f"{promo_name} will expire in the next 21 days."
        else:
            message = f"Sending reminder for placeholder {promo_name} failed. Retry later or contact support."

        # Send notification to each user associated with the promo
        for user_id in promo_detail["users"]:
            request = {"user_id": user_id, "action": None, "guid": None}
            if user_id:
                await insert_notification(
                    request=request,
                    user_id=user_id,
                    module="promotions",
                    action="cron",
                    message=message,
                    promo_id=promo_id,
                    status=status,
                    identifier=identifier,
                    header_text=header_text,
                )

    return None


async def archiving_finalized_offers(promos):
    """Archive finalized offers based on provided promo IDs."""
    # Format promo IDs into SQL-compatible string for inclusion in the query
    formatted_promos = f"({promos[0]})" if len(promos) == 1 else str(tuple(promos))

    # Use the formatted promo IDs in the query template
    query = sched_queries.ARCHIVE_FINALIZED_OFFERS_QUERY.format(
        promo_ids=formatted_promos
    )

    # Execute the query asynchronously
    return await async_execute_query(query)


async def archiving_past_and_ongoing_placeholder_offers(promos):
    """Archive past and ongoing placeholder offers based on promo IDs."""
    # Format promo IDs into SQL-compatible string for inclusion in the query
    formatted_promos = f"({promos[0]})" if len(promos) == 1 else str(tuple(promos))

    # Use the formatted promo IDs in the query template
    query = sched_queries.ARCHIVING_PAST_AND_ONGOING_PLACEHOLDER_OFFERS_QUERY.format(
        promo_ids=formatted_promos
    )

    # Execute the query asynchronously
    return await async_execute_query(query)
