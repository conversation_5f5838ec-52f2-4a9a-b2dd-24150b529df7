from fastapi.routing import APIRouter
from fastapi import status
from enums.Enums import ConfigKeyEnum, ConfigModuleEnum
from pricesmart_common import constants as global_constants
from events import service as events_service
from events import models as event_models
from app.dependencies import UserDependency
from pricesmart_common import utils as common_utils 
from fastapi import BackgroundTasks,Depends
from logger.logger import logger
from events import exceptions as events_exceptions
from typing import Annotated
from client_configuration import constants as client_configuration_constants

events_router = APIRouter(tags=[global_constants.EVENT_API_TAG])

@events_router.post(path="/event")
async def create_event(event_details : event_models.EventModel, 
                       user_id: UserDependency):
    await events_service.create_event(event_details, user_id)
    return common_utils.create_response(
        message=f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} created successfully"
    )

@events_router.get(path="/event-types")
async def get_event_types():
    data = await events_service.get_event_types()
    return common_utils.create_response(
        data=data
    )

@events_router.get(path="/event-objectives")
async def get_event_objectives():
    data = await events_service.get_event_objectives()
    return common_utils.create_response(
        data=data
    )


@events_router.post(path="/get-events")
async def get_events_list(
    request_payload: event_models.GetEvents
):
    data = await events_service.get_events(request_payload)
    return common_utils.create_response(
        data=data
    )


@events_router.get(path="/events/{event_id}")
async def get_event_details(event_id: int):
    data = await events_service.get_event(event_id)
    return common_utils.create_response(
        data=data
    )

@events_router.put("/events/{event_id}")
async def update_event(
    event_id: int,
    request_payload: event_models.UpdateEventModel,
    user_id: UserDependency,
    background_tasks: BackgroundTasks
):
    if await events_service.is_event_locked(event_id):
        raise events_exceptions.EventLockedException(
            f"Locked {client_configuration_constants.EVENT_IDENTIFIER_PRIMARY} cannot be edited"
        )

    effected_offers = await events_service.get_event_effected_offers(event_id, request_payload)
    logger.info(f"effected_offers: {effected_offers}")
    if effected_offers:
        request_payload._effected_offers = effected_offers
        background_tasks.add_task(events_service.update_event_and_its_effected_offers,request_payload,user_id,event_id)
        return common_utils.create_response(
            message=(
                "Event and it's effected offers will be updated in background."
                "You will receive notification once it is completed."
            ),
            status=status.HTTP_200_OK
        )
    else:
        data = await events_service.update_event(event_id, request_payload, user_id)
        return common_utils.create_response(
            data=data
        )

@events_router.post("/events/{event_id}/effected-offers")
async def get_event_effected_offers(
    event_id: int,
    request_payload: event_models.EventModel,
):
    data = await events_service.get_event_effected_offers(event_id, request_payload)
    return common_utils.create_response(
        data=data
    )

@events_router.get("/events/{event_id}/is-under-processing")
async def get_event_is_under_processing(
    event_id: int
):
    data = await events_service.get_event_is_under_processing(event_id)
    return common_utils.create_response(
        data=data
    )

@events_router.post("/lock-unlock-events")
async def lock_unlock_events(
    request_payload: event_models.LockUnlockEvents,
    user_id: UserDependency
):
    await events_service.lock_unlock_events(request_payload,user_id)
    lock_value = "locked" if request_payload.is_locked else "unlocked"
    if len(request_payload.event_ids) == 1:
        event_name = (
            await events_service.get_event_details(request_payload.event_ids[0],select="name")
        )["name"]
        message = f"{event_name} is successfully {lock_value}"
    else:
        message = f"{len(request_payload.event_ids)} {client_configuration_constants.EVENT_IDENTIFIER_PLURAL} are successfully {lock_value}"
    return common_utils.create_response(message=message)

@events_router.post("/events/finalized-promos")
async def get_finalized_promos_under_events(
    request_payload: event_models.DeleteEvents,
):
    data = await events_service.get_finalized_promos_under_events(request_payload.event_ids)
    return common_utils.create_response(
        data=data
    )


@events_router.post("/delete-events")
async def delete_events(
    request_payload: event_models.DeleteEvents,
    user_id: UserDependency,
    background_tasks: BackgroundTasks
):
    await events_service.validate_event_lock(
        request_payload.event_ids,
        ConfigModuleEnum.EVENT,
        ConfigKeyEnum.ALLOW_EVENT_DELETE_AFTER_EVENT_LOCK,
        f"Locked {client_configuration_constants.EVENT_IDENTIFIER_PLURAL} cannot be deleted"
    )
    await events_service.delete_events(
        event_ids=request_payload.event_ids,
        user_id=user_id,
        background_tasks=background_tasks
        )
    return common_utils.create_response()


@events_router.get("/event_attribute_options")
async def event_attributes_value_list():
    data = await events_service.get_event_attributes_value_list()
    return common_utils.create_response(
        data=data
    )


@events_router.post("/copy-events")
async def copy_events(
    request_payload: event_models.CopyEventsRequest,
    user_id: UserDependency
):
    data = await events_service.copy_events(request_payload, user_id)
    return common_utils.create_response(
        data=data,
        message=f"{client_configuration_constants.EVENT_IDENTIFIER_PRIMARY.capitalize()} successfully copied!" if data else None
    )


@events_router.post("/resimulate-events")
async def resimulate_events(
    request_payload: event_models.ResimulateEvents,
    user_id: UserDependency
):
    request_payload._effected_promo_ids = await events_service.get_effected_promo_ids_for_events(request_payload.event_ids)
    await events_service.resimulate_events(request_payload,user_id)
    return common_utils.create_response(
        message = f"{client_configuration_constants.EVENT_IDENTIFIER_PLURAL.capitalize()} Resimulation is in progress. You will receive notification once it is completed."
    )
@events_router.get("/events/{event_id}/products")
async def get_product_details_of_event(event_id: int):
    """
    Get product list details based on event Id 
    """
    data = await events_service.get_product_details_of_event(event_id)
    return common_utils.create_response(data=data)

@events_router.get("/events/{event_id}/stores")
async def get_store_details_of_event(event_id: int):
    """
    Get store list details based on event Id 
    """
    data = await events_service.get_store_details_of_event(event_id)
    return common_utils.create_response(data=data)
