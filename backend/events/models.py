from pydantic import BaseModel, PrivateAttr, field_validator, PydanticUserError, BeforeValidator, AfterValidator, Field
from datetime import date
from typing import Optional, Annotated
from pricesmart_common import constants as common_constants

def validate_event_id(v: int):
    if common_constants.USE_EVENTS and v == common_constants.GLOBAL_EVENT_ID:
        raise EventIdError(message="Required field missing: Event ID", code=400)
    
    return v


EventIdType = Annotated[
    int,
    AfterValidator(validate_event_id),
    Field(default=common_constants.GLOBAL_EVENT_ID,validate_default=True)
]

class DateRestriction(BaseModel):
    sameAsEvent: Optional[bool] = True
    minPromotionDays: Optional[int] = None
    maxPromotionDays: Optional[int] = None
    promotionStartDay: Optional[date] = None
    promotionEndDay: Optional[date] = None

    #Added to handle empty string to None and string numbers to integers. Can be removed if the frontend is updated.
    @field_validator('minPromotionDays', 'maxPromotionDays', mode='before')
    @classmethod
    def handle_empty_string(cls, value, info):
        """Convert empty string to None and string numbers to integers"""
        if value == "":
            return None
        if isinstance(value, str):
            try:
                return int(value)
            except ValueError:
                raise PydanticUserError(
                    f"{info.field_name} must be a valid integer",
                    code="value_error.missing"
                )
        return value


class ProductRestriction(BaseModel):
    product_restriction_level: Optional[str] = None
    lock: Optional[bool] = None
    specific_product_type: Optional[str] = None
    products: list[int] = []
    product_groups: list[int] = []
    product_hierarchy: dict[str,list[int]] = Field(default_factory=dict)


class StoreRestriction(BaseModel):
    store_restriction_level: Optional[str] = None
    lock: Optional[bool] = None
    stores: list[int] = []
    store_groups: list[int] = []


class ProductExclusion(BaseModel):
    product_exclusion_level: Optional[str] = None
    specific_product_type: Optional[str] = None
    products: list[int] = []
    product_groups: list[int] = []
    product_hierarchy: dict[str,list[int]] = Field(default_factory=dict)


class EventModel(BaseModel):
    name: str
    start_date: date
    end_date: date
    submit_offers_by: date
    # marketing_notes: str
    # event_ad_type: str
    # event_type: Optional[str]
    # event_objective: Optional[str]
    created_by: Optional[int] = 0
    date_restriction: DateRestriction
    product_restriction: ProductRestriction 
    store_restriction: StoreRestriction 
    product_exclusion: ProductExclusion
    additional_attributes: Optional[dict] = {} 
    

class UpdateEventModel(EventModel):
    _effected_offers: list[dict] = PrivateAttr(default_factory=list)

class DeleteEvents(BaseModel):
    event_ids: list[int]

class LockUnlockEvents(BaseModel):
    event_ids: list[int]
    is_locked: bool = True

class EventIdError(PydanticUserError):
    pass


class ResimulateEvents(BaseModel):
    event_ids: list[int]
    _effected_promo_ids: list[int] = PrivateAttr(default_factory=list)



class CopyEventDetails(BaseModel):
    start_date: date
    end_date: date
    event_id: EventIdType
    new_event_name: str
    submit_offers_by: date


class CopyEventsRequest(BaseModel):
    events: list[CopyEventDetails]

class GetEvents(BaseModel):
    country_id: Optional[int] = None
    is_locked: Optional[bool] = None