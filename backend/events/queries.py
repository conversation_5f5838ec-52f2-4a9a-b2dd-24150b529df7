CREATE_EVENT_QUERY = """
    SELECT * from price_promo.fn_create_event_new_flow(
        ROW(
            {name},
            {start_date}::date,
            {end_date}::date,
            {submit_offers_by}::date,
            {additional_attributes}::jsonb,
            {user_id},
            ROW(
                {same_as_event}::bool,
                {min_promo_days}::int4,
                {max_promo_days}::int4,
                {promo_start_date}::date,
                {promo_end_date}::date
            )::price_promo.date_restriction,
            ROW(
                {product_restriction_level}, 
                {locked_product_restriction}::bool, 
                {specific_product_type}, 
                {products}::bigint[], 
                {product_groups}::bigint[], 
                {product_hierarchy}::jsonb
            )::price_promo.product_restriction,
            ROW(
                {store_restriction_level}, 
                {locked_store_restriction}::bool, 
                {stores}::bigint[], 
                {store_groups}::bigint[] 
            )::price_promo.store_restriction,
            ROW(
                {product_exclusion_level}, 
                {excluded_specific_product_type}, 
                {excluded_products}::bigint[], 
                {excluded_product_groups}::bigint[], 
                {excluded_product_hierarchy}::jsonb
            )::price_promo.product_exclusion
        )::price_promo.event_model_new_flow
    );
"""

GET_EVENT_TYPE_AND_OBJECTIVE_QUERY = """
    select config_value value, display_name label from {promo_schema}.tb_event_attribute_dropdown_value_config where config_type = {config}
"""

GET_EVENTS_QUERY = """
    with config_values_cte as (
        select 
            max(case when config_name = 'show_events_buffer_days' then config_value end)::text as show_events_buffer_days,
            date(timezone('{timezone}', now())) as current_date
        from
            price_promo.tb_tool_configurations
    )
    select 
        A.event_id, 
        A.name as event_name, 
        A.start_date, 
        A.end_date, 
        A.submit_by,
        A.is_locked,
        B.use_same_as_event,
        B.min_promo_duration,
        B.max_promo_duration,
        B.promo_end_date,
        B.promo_start_date
    from 
        {promo_schema}.event_master A
    left join 
        {promo_schema}.event_date_restrictions B on A.event_id = B.event_id
    join config_values_cte cv on true
    where 
        A.is_deleted = 0
        and (
            -- Case 1: Events with use_same_as_event = true (restrict offer date same as event date)
            (
                B.use_same_as_event = true 
                and A.submit_by >= cv.current_date
                and A.start_date + (cv.show_events_buffer_days || ' days')::interval >= cv.current_date
            )
            or
            -- Case 2: Events with no restriction on promo date (use_same_as_event = false or null)
            (
                (B.use_same_as_event is null or B.use_same_as_event = false)
                and A.end_date >= cv.current_date  -- Event is still yet to end.
                and (B.min_promo_duration is null 
                     or A.end_date >= cv.current_date + (B.min_promo_duration * interval '1 day'))  -- Min promo days can be achieved
                and (B.promo_start_date is null 
                     or B.promo_start_date >= cv.current_date)  -- Promotion start date >= today (if not null)
                and (B.promo_end_date is null 
                     or B.promo_end_date >= cv.current_date)  -- Promotion end date >= today (if not null)
            )
        )
        {country_id_condition}
        {is_locked_condition}
    order by event_id
"""

GET_EVENT_QUERY = """
    select * from {promo_schema}.fn_get_event_new_flow({event_id});
"""

GET_EVENT_DETAILS = """
    select
        {select}
    from 
        price_promo.event_master
    where event_id = {event_id}
"""


UPDATE_EVENT_QUERY = """
    SELECT * from price_promo.fn_update_event_new_flow(
        {event_id},
        ROW(
            {name},
            {start_date}::date,
            {end_date}::date,
            {submit_offers_by}::date,
            {additional_attributes}::jsonb,
            null,
            ROW(
                {same_as_event}::bool,
                {min_promo_days}::int4,
                {max_promo_days}::int4,
                {promo_start_date}::date,
                {promo_end_date}::date
            )::price_promo.date_restriction,
            ROW(
                {product_restriction_level}, 
                {locked_product_restriction}::bool, 
                {specific_product_type}, 
                {products}::bigint[], 
                {product_groups}::bigint[], 
                {product_hierarchy}::jsonb
            )::price_promo.product_restriction,
            ROW(
                {store_restriction_level}, 
                {locked_store_restriction}::bool, 
                {stores}::bigint[], 
                {store_groups}::bigint[] 
            )::price_promo.store_restriction,
            ROW(
                {product_exclusion_level}, 
                {excluded_specific_product_type}, 
                {excluded_products}::bigint[], 
                {excluded_product_groups}::bigint[], 
                {excluded_product_hierarchy}::jsonb
            )::price_promo.product_exclusion
        )::price_promo.event_model_new_flow,
        {user_id}
    ) event_id; 
"""

GET_EVENT_EFFECTED_OFFERS_QUERY = """
    select 
        r_promo_id as promo_id,
        r_promo_name as promo_name,
        psc.status_name as promo_status,
        r_promo_status as promo_status_id,
        r_start_date as promo_start_date,
        r_end_date as promo_end_date,
        r_action as action,
        case 
            when r_new_status = 0 then 'Draft'
            when r_new_status = 6 then 'Archived'
        end  as new_status,
        r_new_status as new_status_id,
        r_operation_to_perform as operation_to_perform
    from {promo_schema}.fn_get_event_effected_offers_new_flow(
        {event_id},
        ROW(
            {name},
            {start_date}::date,
            {end_date}::date,
            {submit_offers_by}::date,
            {additional_attributes}::jsonb,
            null,
            ROW(
                {same_as_event}::bool,
                {min_promo_days}::int4,
                {max_promo_days}::int4,
                {promo_start_date}::date,
                {promo_end_date}::date
            )::price_promo.date_restriction,
            ROW(
                {product_restriction_level}, 
                {locked_product_restriction}::bool, 
                {specific_product_type}, 
                {products}::bigint[], 
                {product_groups}::bigint[], 
                {product_hierarchy}::jsonb
            )::price_promo.product_restriction,
            ROW(
                {store_restriction_level}, 
                {locked_store_restriction}::bool, 
                {stores}::bigint[], 
                {store_groups}::bigint[] 
            )::price_promo.store_restriction,
            ROW(
                {product_exclusion_level}, 
                {excluded_specific_product_type}, 
                {excluded_products}::bigint[], 
                {excluded_product_groups}::bigint[], 
                {excluded_product_hierarchy}::jsonb
            )::price_promo.product_exclusion
        )::price_promo.event_model_new_flow
    ) eo
    left join {promo_schema}.promo_status_config psc 
    on psc.status_id = eo.r_promo_status
    ;
"""

SET_EVENT_UNDER_PROCESSING = """
    update price_promo.event_master 
    set is_under_processing = {is_under_processing}
    where event_id = {event_id}
    ;
"""

GET_EVENT_IS_UNDER_PROCESSING = """
    select is_under_processing from price_promo.event_master where event_id = {event_id};
"""

GET_FINALIZED_PROMOS_UNDER_EVENTS_QUERY = """
    select 
        pm.promo_id, 
        pm.name as promo_name, 
        em.name as event_name,
        pm.start_date, 
        pm.end_date, 
        psc.status_name 
    from 
        price_promo.promo_master pm
    join
        price_promo.promo_status_config psc
    on 
        pm.status = psc.status_id
    join
        price_promo.event_master em
    on 
        em.event_id = pm.event_id
    where 
        pm.event_id = ANY({event_ids}) 
        and pm.status = ANY({status_ids});
"""

DELETE_EVENTS_AND_FETCH_EFFECTED_PROMOS_QUERY = """
    update price_promo.event_master 
    set is_deleted = 1 
    where event_id = ANY({event_ids});

    select array_agg(promo_id) as promo_ids from price_promo.promo_master where event_id = ANY({event_ids});
"""

IS_EVENT_LOCKED = """
    select is_locked from price_promo.event_master where event_id = {event_id};
"""

LOCK_UNLOCK_EVENTS = """
    update price_promo.event_master 
    set is_locked = {is_locked},
        updated_at = now(),
        updated_by = {user_id}
    where event_id = ANY({event_ids});
"""

HAS_LOCKED_EVENTS = """
    select 1 as has_locked_events from price_promo.event_master 
    where 
        event_id = ANY({event_ids}) 
        and is_locked = true
    limit 1;
"""

GET_EVENT_IDS_BY_PROMO_IDS = """
    select array_agg(event_id) as event_ids from price_promo.promo_master where promo_id = ANY({promo_ids});
"""

HAS_LIVE_EVENTS = """
    with config_values_cte as (
        select 
            max(case when config_name = 'manual_unlocking_buffer_days' then config_value end)::text as manual_unlocking_buffer_days
        from
            price_promo.tb_tool_configurations
    )
    select true as has_live_events from price_promo.event_master 
    join config_values_cte cv on true
    where 
        event_id = ANY({event_ids}) 
        and start_date + (cv.manual_unlocking_buffer_days || ' days')::interval <= timezone('{timezone}', now())
    limit 1;
"""


GET_EVENT_ATTRIBUTES_VALUE_LIST = """
    WITH distinct_types AS (
        SELECT DISTINCT config_type as type
        FROM price_promo.tb_event_attribute_dropdown_value_config 
    ),
    type_arrays AS (
        SELECT 
            dt.type,
            jsonb_agg(
                jsonb_build_object(
                    'label', a.display_name,
                    'value', a.config_value
                )
            ) as type_array
        FROM distinct_types dt
        JOIN price_promo.tb_event_attribute_dropdown_value_config  a ON a.config_type = dt.type
        GROUP BY dt.type
    )
    SELECT jsonb_object_agg(
        type,
        type_array
    ) as result
    FROM type_arrays;
"""

COPY_EVENTS_QUERY = """
    select 
        * 
    from 
        price_promo.fn_copy_events({event_details}, {user_id});
"""

GET_UNDER_PROCESSING_EVENTS = """
    select array_agg(event_id) as under_processing_events
    from price_promo.event_master 
    where event_id = ANY({event_ids}::int[])
    and is_under_processing = true
    ;
"""

SET_EVENTS_UNDER_PROCESSING = """
    update price_promo.event_master 
    set is_under_processing = {is_under_processing}
    where event_id = ANY({event_ids}::int[]);
"""

GET_EFFECTED_PROMO_IDS_FOR_EVENTS = """
    select array_agg(promo_id) as effected_promo_ids
    from price_promo.promo_master 
    where event_id = ANY({event_ids}::int[])
    and step_count >= 3
    and status != 6
    and start_date >= date(timezone('{timezone}', now()))
    ;
"""
    
GET_PRODUCT_DETAILS_OF_EVENT = """
    select
        pm.*,
        cm.currency_symbol as currency_symbol,
        tlia.total_inventory,
        ROUND(prf.revenue::decimal,2) as finalized_revenue,
        ROUND(prf.margin::decimal,2) as finalized_margin,
        ROUND(prf.units::decimal,2) as finalized_units,
        ROUND(((prf.margin * 100) / nullif(prf.revenue,0))::decimal, 2) as finalized_gm_percent,
        ROUND(pra.revenue::decimal,2) as actual_revenue,
        ROUND(pra.margin::decimal,2) as actual_margin,
        ROUND(pra.units::decimal,2) as actual_units,
        ROUND(((pra.margin * 100) / nullif(pra.revenue,0))::decimal, 2) as actual_gm_percent
    from
        (
            SELECT
                product_id
            FROM
                {promo_schema}.event_product
            WHERE
                event_id = {event_id}
        ) as pp
        left join {promo_schema}.product_master as pm on pp.product_id = pm.product_id
        left join {global_schema}.tb_latest_inventory_agg as tlia on pm.product_id = tlia.product_id
        join global.tb_currency_master as cm using (currency_id)
        left join (
            select
                product_id,
                avg(revenue) as revenue,
                avg(margin) as margin,
                avg(sales_units) as units
            from
                {promo_schema}.ps_recommended_finalized as pf
            where promo_id = any(array{promo_list}::integer[])
            group by
                product_id
        ) as prf on prf.product_id = pm.product_id
        left join (
            select
                product_id,
                avg(revenue) as revenue,
                avg(margin) as margin,
                avg(sales_units) as units
            from
                {promo_schema}.ps_recommended_actuals as pa
            where promo_id = any(array{promo_list}::integer[])
            group by
                product_id
        ) as pra on pra.product_id = pm.product_id;
"""

GET_STORE_DETAILS_OF_EVENT = """
     WITH
        store_selection AS (
            SELECT store_selection_type
            FROM {promo_schema}.event_master
            WHERE event_id = {event_id}
        )
    SELECT
        tsm.store_id,
        tsm.store_name,
        tsm.s0_name,
        tsm.s1_name,
        tsm.s2_name,
        tsm.s3_name,
        tsm.s4_name
    FROM
        global.tb_store_master tsm
    WHERE
        (SELECT store_selection_type FROM store_selection) = 'all_stores'
        OR tsm.store_id IN (
            SELECT store_id
            FROM {promo_schema}.event_stores es 
            WHERE event_id = {event_id}
        )
"""

GET_PROMOS_OF_EVENT = """
    select promo_id from {promo_schema}.promo_master where event_id = {event_id}
"""