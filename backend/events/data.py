from configuration.environment import environment
from events import queries as events_queries
from events import models as event_models

from pricesmart_common.utils import async_execute_query, get_array_format, get_json_format, get_str_repr
from events import models as event_models
from pricesmart_common import constants as common_constants
from promotions import constants as promo_constants
from logger.logger import logger
from typing import Optional
import json


async def get_event_query(
    action: str,
    event_details: event_models.EventModel,
    *,
    event_id: Optional[int] = None,
    user_id: Optional[int] = None
) -> str:

    if action == 'create':
        query = events_queries.CREATE_EVENT_QUERY
    elif action == 'update':
        query = events_queries.UPDATE_EVENT_QUERY
    elif action == 'get_effected_offers':
        query = events_queries.GET_EVENT_EFFECTED_OFFERS_QUERY

    query = query.format(
        event_id=event_id,
        name=get_str_repr(event_details.name),
        start_date=get_str_repr(event_details.start_date),
        end_date=get_str_repr(event_details.end_date),
        submit_offers_by=get_str_repr(event_details.submit_offers_by),
        user_id=user_id,

        same_as_event=get_str_repr(event_details.date_restriction.sameAsEvent),
        min_promo_days=get_str_repr(event_details.date_restriction.minPromotionDays), 
        max_promo_days=get_str_repr(event_details.date_restriction.maxPromotionDays),
        promo_start_date=get_str_repr(event_details.date_restriction.promotionStartDay), 
        promo_end_date=get_str_repr(event_details.date_restriction.promotionEndDay),

        product_restriction_level=get_str_repr(event_details.product_restriction.product_restriction_level),
        locked_product_restriction=get_str_repr(event_details.product_restriction.lock),
        specific_product_type=get_str_repr(event_details.product_restriction.specific_product_type),
        products=get_array_format(event_details.product_restriction.products),
        product_groups=get_array_format(event_details.product_restriction.product_groups),
        product_hierarchy=get_str_repr(event_details.product_restriction.product_hierarchy),

        store_restriction_level=get_str_repr(event_details.store_restriction.store_restriction_level),
        locked_store_restriction=event_details.store_restriction.lock,
        stores=get_array_format(event_details.store_restriction.stores),
        store_groups=get_array_format(event_details.store_restriction.store_groups),

        product_exclusion_level=get_str_repr(event_details.product_exclusion.product_exclusion_level),
        excluded_specific_product_type=get_str_repr(event_details.product_exclusion.specific_product_type),
        excluded_products=get_array_format(event_details.product_exclusion.products),
        excluded_product_groups=get_array_format(event_details.product_exclusion.product_groups),
        excluded_product_hierarchy=get_str_repr(event_details.product_exclusion.product_hierarchy),
        promo_schema=environment.promo_schema,
        additional_attributes=get_str_repr(event_details.additional_attributes)
    )
    logger.info(query)
    return query

async def create_event(event_details: event_models.EventModel, user_id: int):
    query = await get_event_query('create', event_details, user_id=user_id)
    data = await async_execute_query(query)
    return data[0]['fn_create_event_new_flow']


async def get_event_types():
    query = events_queries.GET_EVENT_TYPE_AND_OBJECTIVE_QUERY.format(
        config=get_str_repr('event_type'),
        promo_schema=environment.promo_schema
    )
    data = await async_execute_query(query)
    return data


async def get_event_objectives():
    query = events_queries.GET_EVENT_TYPE_AND_OBJECTIVE_QUERY.format(
        config=get_str_repr('event_objective'),
        promo_schema=environment.promo_schema
    )
    data = await async_execute_query(query)
    return data


async def get_events(request_payload: event_models.GetEvents):
    query = events_queries.GET_EVENTS_QUERY.format(
        promo_schema=environment.promo_schema,
        timezone=common_constants.CLIENT_TIMEZONE,
        is_locked_condition = (
            f"""
            and is_locked = {get_str_repr(request_payload.is_locked)}
            """ 
            if request_payload.is_locked is not None
            else ""
        ),
        country_id_condition = (
            f"""
            and A.country_id = {get_str_repr(request_payload.country_id)}
            """
            if request_payload.country_id is not None
            else ""
        )
    )
    data = await async_execute_query(query)
    return data


async def get_event(event_id: int):
    query = events_queries.GET_EVENT_QUERY.format(
        event_id=event_id,
        promo_schema=environment.promo_schema
    )
    data = await async_execute_query(query)
    return data[0]['final_response']


async def get_event_details(
    event_id: int,
    select: str
) -> dict:
    query = events_queries.GET_EVENT_DETAILS.format(
        event_id = event_id,
        select = select
    )

    data = await async_execute_query(query)
    return data[0]


async def update_event(event_id: int, request_payload: event_models.EventModel, user_id: int):
    query = await get_event_query('update', request_payload, event_id=event_id, user_id=user_id)
    data = await async_execute_query(query)
    return data[0]


async def get_event_effected_offers(event_id: int, request_payload: event_models.EventModel):
    query = await get_event_query('get_effected_offers', request_payload, event_id=event_id)
    data = await async_execute_query(query)
    return data


async def get_finalized_promos_under_events(event_ids: list[int]):
    query = events_queries.GET_FINALIZED_PROMOS_UNDER_EVENTS_QUERY.format(
        event_ids = get_array_format(event_ids),
        status_ids = get_array_format([
            promo_constants.OFFER_STATUS[promo_constants.FINALIZED], 
            promo_constants.OFFER_STATUS[promo_constants.EXECUTION]
        ])
    )
    print(query)
    data = await async_execute_query(query)
    return data


async def delete_events_and_fetch_effected_promos(event_ids: list[int]):
    query = events_queries.DELETE_EVENTS_AND_FETCH_EFFECTED_PROMOS_QUERY.format(
        event_ids = get_array_format(event_ids)
    )
    data = await async_execute_query(query)
    return data[0]["promo_ids"] or []


async def set_event_under_processing(event_id: int):
    query = events_queries.SET_EVENT_UNDER_PROCESSING.format(
        event_id=event_id,
        is_under_processing="true"
    )
    await async_execute_query(query)


async def get_event_is_under_processing(event_id: int):
    query = events_queries.GET_EVENT_IS_UNDER_PROCESSING.format(
        event_id=event_id
    )
    data = await async_execute_query(query)
    return data[0]


async def unset_event_under_processing(event_id: int):
    query = events_queries.SET_EVENT_UNDER_PROCESSING.format(
        event_id=event_id,
        is_under_processing="false"
    )
    await async_execute_query(query)

async def is_event_locked(event_id: int):
    query = events_queries.IS_EVENT_LOCKED.format(
        event_id=event_id
    )
    data = await async_execute_query(query)
    return data[0]["is_locked"] if data else False

async def lock_unlock_events(request_payload: event_models.LockUnlockEvents,user_id: int):
    query = events_queries.LOCK_UNLOCK_EVENTS.format(
        event_ids = get_array_format(request_payload.event_ids),
        is_locked = request_payload.is_locked,
        user_id = user_id
    )
    await async_execute_query(query)

async def has_locked_events(event_ids: list[int]):
    query = events_queries.HAS_LOCKED_EVENTS.format(
        event_ids = get_array_format(event_ids)
    )
    data = await async_execute_query(query)
    return data[0]["has_locked_events"] if data else False

async def get_event_ids_by_promo_ids(promo_ids: list[int]):
    query = events_queries.GET_EVENT_IDS_BY_PROMO_IDS.format(
        promo_ids = get_array_format(promo_ids)
    )
    data = await async_execute_query(query)
    return data[0]["event_ids"] if data else []

async def has_live_events(event_ids: list[int]):
    query = events_queries.HAS_LIVE_EVENTS.format(
        event_ids = get_array_format(event_ids),
        timezone = common_constants.CLIENT_TIMEZONE
    )
    data = await async_execute_query(query)
    return data[0]["has_live_events"] if data else False


async def get_event_attributes_value_list():
    query = events_queries.GET_EVENT_ATTRIBUTES_VALUE_LIST.format(
        promo_schema = environment.promo_schema
    )
    data = await async_execute_query(query)
    return data[0]["result"] if data else {}


async def copy_events(request_payload: event_models.CopyEventsRequest, user_id: int):
    query = events_queries.COPY_EVENTS_QUERY.format(
        event_details = get_json_format(
            json.dumps(
                [i.model_dump(mode='json') for i in request_payload.events]
            )
        ),
        user_id =  user_id
    )
    print(query)
    data = await async_execute_query(query)
    return data[0]["fn_copy_events"]


async def get_under_processing_events(event_ids: list[int]):
    query = events_queries.GET_UNDER_PROCESSING_EVENTS.format(
        event_ids = get_array_format(event_ids)
    )
    data = await async_execute_query(query)
    logger.info(f"under processing events : {data}")
    return data[0]["under_processing_events"] if data else []

async def set_events_under_processing(event_ids: list[int]):
    query = events_queries.SET_EVENTS_UNDER_PROCESSING.format(
        event_ids = get_array_format(event_ids),
        is_under_processing="true"
    )
    await async_execute_query(query)

async def unset_events_under_processing(event_ids: list[int]):
    query = events_queries.SET_EVENTS_UNDER_PROCESSING.format(
        event_ids = get_array_format(event_ids),
        is_under_processing="false"
    )
    await async_execute_query(query)

async def get_effected_promo_ids_for_events(event_ids: list[int]):
    query = events_queries.GET_EFFECTED_PROMO_IDS_FOR_EVENTS.format(
        event_ids = get_array_format(event_ids),
        timezone = common_constants.CLIENT_TIMEZONE
    )
    data = await async_execute_query(query)
    logger.info(f"effected promos : {data}")
    return data[0]["effected_promo_ids"] or []

async def get_product_details_of_event(event_id: int):
    promo_list = await get_promo_ids_for_event(event_id)
    query = events_queries.GET_PRODUCT_DETAILS_OF_EVENT.format(
        event_id = event_id,
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        promo_list = promo_list
    )
    logger.info(query)
    result = await async_execute_query(query=query)
    return result

async def get_store_details_of_event(event_id: int):
    query = events_queries.GET_STORE_DETAILS_OF_EVENT.format(
        event_id = event_id,
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
    )
    logger.info(query)
    result = await async_execute_query(query=query)
    return result

async def get_promo_ids_for_event(event_id: int):
    query = events_queries.GET_PROMOS_OF_EVENT.format(
        promo_schema=environment.promo_schema,
        event_id=event_id
    )
    print(query)
    result = await async_execute_query(query=query)
    return [row['promo_id'] for row in result]
