from typing import Optional, TypedDict

from pricesmart_common.utils import async_execute_query
from pricesmart_common.utils import get_str_repr
from client_configuration import constants as client_configuration_constants
from pydantic import BaseModel
from enum import Enum


class Parameters(TypedDict):
    user_id: int


class NotificationModel(BaseModel):
    message: str
    module: str
    action: str
    navigate_to: str
    identifier: str
    expiry: int = 4320
    status: bool = True
    header_text: Optional[str] = None
    sse_action: Optional[str] = None
    application: Optional[str] = "promo"


class NotificationModules(Enum):
    PROMOTIONS = "promotions"
    STORE_GROUP = "store_group"
    REPORT = "report"
    PROMO = "promo"
    PRODUCT_GROUP = "product_group"
    EVENT = "event"

class NotificationHeaderText(Enum):
    OFFER_REFRESH = f"{client_configuration_constants.PROMO_IDENTIFIER_ALIAS.capitalize()} Refresh"
    OVERRIDE = "Override #"
    EXECUTION_APPROVAL = "Execution Approval"
    WITHDRAWAL = "Withdrawal"

# pylint: disable=too-many-arguments
async def create_notification(  # noqa: PLR0913
    message: str,
    module: str,
    action: str,
    user_id: str,
    expiry: int = 4320,
    identifier: str | None = None,
    status: bool = True,
    header_text: str | None = None,
    navigate_to=None,
    promo_ids=None,
    application: str = "promo",
    **_
):
    query = """
                select
                    *
                from
                    global.fn_insert_notification(
                        {module}::varchar,
                        {application}::varchar,
                        {message}::varchar,
                        {expiry}::integer,
                        {action}::varchar,
                        {navigate_to}::varchar,
                        {user_id}::integer,
                        {status}::bool,
                        {identifier}::varchar,
                        {header_text}::varchar,
                        {promo_ids}::int[]
                    )
            """.format(
        module=get_str_repr(module),
        application=get_str_repr(application),
        message=get_str_repr(message),
        action=get_str_repr(action),
        navigate_to=get_str_repr(str(navigate_to))
        if navigate_to is not None
        else "null",
        expiry=get_str_repr(expiry),
        user_id=user_id,
        identifier=get_str_repr(identifier),
        status=status,
        header_text=get_str_repr(header_text),
        promo_ids=get_str_repr(promo_ids) if promo_ids is None else "'{" + ",".join(map(str, promo_ids)) + "}'",
    )
    notification_data = await async_execute_query(query)
    return notification_data[0] if notification_data else {}
