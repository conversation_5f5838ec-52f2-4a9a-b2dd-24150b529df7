import configparser
import json
import os
from logging import exception
from pricesmart_common.constants import CACHED_ENV_FILE_PATH,LOCAL_ENV

from google.cloud import secretmanager


class ProjectEnvironment:
    def __init__(self):
        """
        The __init__ function is called automatically when a new instance of the class is created.
        It sets up all the variables that will be used by the class, and performs any other setup
        that the class needs to do. In this case, it loads configuration information from a file.

        :param self: Refer to the object itself
        :return: The self object
        :author: <PERSON><PERSON>[1533]
        """

        self.configfilebasepath = os.getcwd()
        self.environment = None
        self.config = configparser.RawConfigParser()
        self.load_config(self.configfilebasepath)
        self.set_environment()
        self.set_env_parameters()
        self.set_keys_from_secret_manager()

    def get_environment(self, configfilebasepath):
        """
        The get_environment function is used to determine the environment that the application is running in.
        The function will first check if an environment has been specified in the config file, and if not it will
        look for a .ini file with a name that matches one of the environments defined in environments/environments.ini

        :param self: Reference the class itself
        :param configfilebasepath: Specify the base path of the configuration file
        :return: The name of the environment
        :author: Arun <PERSON>[1533]
        """

        environment_name = self.environment
        if self.environment == None:
            envitonmentdir = configfilebasepath + r"/environment/"
            for a, b, file_names in os.walk(envitonmentdir):
                for environment_file in file_names:
                    if environment_file.endswith(".ini"):
                        return os.path.splitext(environment_file)[0]
        return environment_name

    def load_config(self, configfilebasepath):
        configfilepath = configfilebasepath + r"/environment.conf"
        self.config.read(configfilepath)

    def set_environment(self):
        """
        The set_environment function sets the environment variable to be used in the program.
        It will use the default environment if none is specified, otherwise it will use what is specified.
        If no environment is found, it will throw an exception.

        :param self: Reference the class object
        :return: The environment variable
        :author: Arun Kumar[1533]
        """

        try:
            self.environment = os.getenv("env")
        except exception:
            print("using the file based environment because of ::",exception)
        if self.environment is None:
            self.environment = self.get_environment(self.configfilebasepath)
        print("environment", self.environment)

    def set_env_variables(self, key_name):
        """
        The set_env_variables function is used to set the environment variables for a given key name.
        The function takes two parameters: self and key_name. Self refers to the class instance, in this case GoogleSecretLoader,
        and key_name is a string that represents the name of an environment variable stored in your config file.

        :param self: Access variables that belongs to the class
        :param key_name: Get the value of a key in the config file
        :return: The value of the key_name parameter from the configuration file
        :author: Arun Kumar[1533]
        """

        return self.config.get(self.environment, key_name)

    def set_env_parameters(self):
        """
        The set_env_parameters function is used to set the environment variables for the application.
        The function takes in a single parameter, self, which is used to access class attributes and methods.
        This function will be called before any other functions in this module.

        :param self: Access variables that belongs to the class
        :return: A dictionary of all the environment variables
        :author: Arun Kumar[1533]
        """

        self.secret_project = self.set_env_variables("secret_project")
        self.secret_id = self.set_env_variables("secret_id")
        self.secret_version = self.set_env_variables("secret_version")
        self.project_id = self.set_env_variables("project_id")
        self.project_resource = self.set_env_variables("project_resource")
        self.client_schema = self.set_env_variables("client_schema")
        self.promo_schema = self.set_env_variables("promo_schema")
        self.markdown_schema = self.set_env_variables("markdown_schema")
        self.promo_opt_schema = self.set_env_variables("promo_opt_schema")
        self.meta_schema = self.set_env_variables("meta_schema")
        self.global_schema = self.set_env_variables("global_schema")
        self.is_slack_logger = self.set_env_variables("is_slack_logger")
        self.app_date_time_format = self.set_env_variables("app_date_time_format")
        self.app_date_format = self.set_env_variables("app_date_format")
        # self.overall_level_logging = self.set_env_variables("overall_level_logging")
        self.log_stash_host = self.set_env_variables("log_stash_host")
        self.log_stash_port = self.set_env_variables("log_stash_port")
        self.log_level = self.set_env_variables("log_level")
        self.deployment_env = self.set_env_variables("deployment_env")
        # self.BACKEND_URL = self.set_env_variables("BACKEND_URL")
        self.elastic_apm_url = self.set_env_variables("elastic_apm_url")
        self.elastic_apm_key = self.set_env_variables("elastic_apm_key")
        self.cache_type_env_variable = self.set_env_variables("cache_type_env_variable")
        self.cache_distributed_server = self.set_env_variables(
            "cache_distributed_server"
        )
        self.cache_distributed_port = self.set_env_variables("cache_distributed_port")
        self.logger_name = self.set_env_variables("logger_name")
        self.is_logstash = self.set_env_variables("is_logstash")
        self.append_user_and_system_info_to_logger = self.set_env_variables(
            "append_user_and_system_info_to_logger"
        )
        self.client_id = self.set_env_variables("client_id")
        self.client_name = self.set_env_variables("client_name")
        self.OPTIMISE_API_URL = self.set_env_variables("OPTIMISE_API_URL")
        self.RESIMULATE_API_URL = self.set_env_variables("RESIMULATE_API_URL")
        self.REPORT_GENERATOR_API = self.set_env_variables("REPORT_GENERATOR_API")
        self.SAKS_URL_PREFIX = self.set_env_variables("SAKS_URL_PREFIX")
        self.simulation_schema = self.set_env_variables("simulation_schema")
        self.um_schema = self.set_env_variables("um_schema")
        self.GLOBAL_LABELS = self.set_env_variables("GLOBAL_LABELS")
        self.log_function_execution_time = self.set_env_variables(
            "log_function_execution_time"
        )
        self.sso_sign_in_provider = self.set_env_variables("sso_sign_in_provider")
        self.mojo_base_url = self.set_env_variables("mojo_base_url")
        self.mojo_api_url = self.set_env_variables("mojo_api_url")
        self.cache_enabled = self.set_env_variables("cache_enabled")
        self.login_provider = self.set_env_variables("login_provider")
        self.ENVIRONMENT = self.set_env_variables('ENVIRONMENT')
        self.LOG_TITLE = self.set_env_variables('LOG_TITLE')
        self.is_local = True if self.environment==LOCAL_ENV else False
        self.EMAIL_KEY = self.set_env_variables('EMAIL_KEY')
        self.DOWNSTREAM_CLOUD_FUNCTION_URL = self.set_env_variables('DOWNSTREAM_CLOUD_FUNCTION_URL')
        self.DOWNSTREAM_BUCKET_NAME = self.set_env_variables('DOWNSTREAM_BUCKET_NAME')
        self.REFRESH_API_URL = self.set_env_variables('REFRESH_API_URL')    
        self.OPT_CALLBACK_ENDPOINT = self.set_env_variables('OPT_CALLBACK_ENDPOINT')
        self.OPT_EVENT_EDIT_API_URL = self.set_env_variables('OPT_EVENT_EDIT_API_URL')
        self.OPT_EVENT_RESIMULATE_API_URL = self.set_env_variables('OPT_EVENT_RESIMULATE_API_URL')

        
    def set_keys_from_secret_manager(self):
        r"""
        The set_keys_from_secret_manager function is used to set the keys from a secret manager.
        The function takes in self as an argument and sets the following attributes:
        self.parent = f&quot;projects/{self.secret_project}&quot;
        self.client = secretmanager.SecretManagerServiceClient()
        self.secret = json.loads(client access_secret_value(request={'name': parent + '/secrets/' + self key}).payload\data)decode('utf-8')&quot;)  # noQA E501 line too long (131 &gt; 120 characters)  # noQA E501 line

        :param self: Allow a function to refer to itself
        :return: A dictionary of the secrets that are stored in secret manager
        :author: Arun Kumar[1533]
        """

        self.parent = f"projects/{self.secret_project}"
        self.secret = self.get_environment_data()
        
        self.conn_string = self.secret["connection_string"]
        self.google_api_key = self.secret["google_api_key"]
        self.mojo_access_key = self.secret["mojo_access_key"]
        self.mojo_ticket_queue_id = self.secret["mojo_ticket_queue_id"]
        self.db_location = self.secret["db_location"]
        self.db_user = self.secret["db_user"]
        self.db_password = self.secret["db_password"]
        self.db_host = self.secret["db_host"]
        self.db_name = self.secret["db_name"]
        self.db_port = self.secret["db_port"]
        self.x509_cert = self.secret["x509_cert"]
        self.BE_KEY = self.secret["be_key"]
        self.OPENAI_API_KEY = self.secret["OPENAI_API_KEY"]
    

    def get_environment_data(self)->dict:
        if self.environment != LOCAL_ENV:
            return self.get_data_from_secret_manager()
        else:
            path = os.path.join(CACHED_ENV_FILE_PATH,self.secret_id)
            if os.path.exists(path):
                with open(path) as file_obj:
                    return json.load(file_obj)
            else:
                secret_data = self.get_data_from_secret_manager()
                with open(path, "w") as file_obj:
                    json.dump(secret_data, file_obj)
                return secret_data
    
    def get_data_from_secret_manager(self):
        client = secretmanager.SecretManagerServiceClient()
        return json.loads(
            client.access_secret_version(
                request={
                    "name": self.parent
                    + "/secrets/"
                    + self.secret_id
                    + "/versions/"
                    + self.secret_version
                }
            ).payload.data.decode("utf-8")
        )



environment = ProjectEnvironment()